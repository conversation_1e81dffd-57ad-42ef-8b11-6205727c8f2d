// This file was automatically generated.  Do not modify.

'use strict';

Blockly.Msg["ADD_COMMENT"] = "Pievienot komentāru";
Blockly.Msg["CANNOT_DELETE_VARIABLE_PROCEDURE"] = "Can't delete the variable '%1' because it's part of the definition of the function '%2'";  // untranslated
Blockly.Msg["CHANGE_VALUE_TITLE"] = "Mainīt vērtību:";
Blockly.Msg["CLEAN_UP"] = "Sakopt blokus";
Blockly.Msg["COLLAPSED_WARNINGS_WARNING"] = "Collapsed blocks contain warnings.";  // untranslated
Blockly.Msg["COLLAPSE_ALL"] = "Sakļaut blokus";
Blockly.Msg["COLLAPSE_BLOCK"] = "Sakļaut bloku";
Blockly.Msg["COLOUR_BLEND_COLOUR1"] = "1. krāsa";
Blockly.Msg["COLOUR_BLEND_COLOUR2"] = "2. krāsa";
Blockly.Msg["COLOUR_BLEND_HELPURL"] = "https://meyerweb.com/eric/tools/color-blend/#:::rgbp";  // untranslated
Blockly.Msg["COLOUR_BLEND_RATIO"] = "attiecība";
Blockly.Msg["COLOUR_BLEND_TITLE"] = "sajaukt";
Blockly.Msg["COLOUR_BLEND_TOOLTIP"] = "Sajauc kopā divas krāsas ar doto attiecību (0.0 - 1.0).";
Blockly.Msg["COLOUR_PICKER_HELPURL"] = "https://lv.wikipedia.org/wiki/Krāsa";
Blockly.Msg["COLOUR_PICKER_TOOLTIP"] = "Izvēlēties krāsu no paletes.";
Blockly.Msg["COLOUR_RANDOM_HELPURL"] = "http://randomcolour.com";  // untranslated
Blockly.Msg["COLOUR_RANDOM_TITLE"] = "nejauša krāsa";
Blockly.Msg["COLOUR_RANDOM_TOOLTIP"] = "Izvēlēties krāsu pēc nejaušības principa.";
Blockly.Msg["COLOUR_RGB_BLUE"] = "zila";
Blockly.Msg["COLOUR_RGB_GREEN"] = "zaļa";
Blockly.Msg["COLOUR_RGB_HELPURL"] = "https://www.december.com/html/spec/colorpercompact.html";  // untranslated
Blockly.Msg["COLOUR_RGB_RED"] = "sarkana";
Blockly.Msg["COLOUR_RGB_TITLE"] = "veido krāsu no";
Blockly.Msg["COLOUR_RGB_TOOLTIP"] = "Izveidot krāsu ar norādīto daudzumu sarkanā, zaļā un zilā toņu. Visas vērtības ir starp 0 un 100.";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_HELPURL"] = "https://github.com/google/blockly/wiki/Loops#loop-termination-blocks";  // untranslated
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_OPERATOR_BREAK"] = "iet ārā no cikla";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_OPERATOR_CONTINUE"] = "turpināt ar cikla nākamo iterāciju";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_TOOLTIP_BREAK"] = "Iet ārā no iekļaujošā cikla";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_TOOLTIP_CONTINUE"] = "Nepildīt atlikušo cikla daļu bet sākt nākamo iterāciju.";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_WARNING"] = "Brīdinājums: šo bloku drīkst izmantot tikai cikla iekšienē.";
Blockly.Msg["CONTROLS_FOREACH_HELPURL"] = "https://github.com/google/blockly/wiki/Loops#for-each";  // untranslated
Blockly.Msg["CONTROLS_FOREACH_TITLE"] = "visiem %1 no saraksta %2";
Blockly.Msg["CONTROLS_FOREACH_TOOLTIP"] = "Katram objektam no saraksta piešķirt mainīgajam '%1' šo objektu un izpildīt komandas.";
Blockly.Msg["CONTROLS_FOR_HELPURL"] = "https://github.com/google/blockly/wiki/Loops#count-with";  // untranslated
Blockly.Msg["CONTROLS_FOR_TITLE"] = "skaitīt %1 no %2 līdz %3 ar soli %4";
Blockly.Msg["CONTROLS_FOR_TOOLTIP"] = "Ļauj mainīgajam '%1' pieņemt vērtības no sākuma līdz beigu vērtībai, un izpildīt iekļautos blokus katrai no šīm pieņemtajām vērtībām.";
Blockly.Msg["CONTROLS_IF_ELSEIF_TOOLTIP"] = "Pievienot nosacījumu \"ja\" blokam.";
Blockly.Msg["CONTROLS_IF_ELSE_TOOLTIP"] = "Pievienot gala nosacījumu \"ja\" blokam.";
Blockly.Msg["CONTROLS_IF_HELPURL"] = "https://github.com/google/blockly/wiki/IfElse";  // untranslated
Blockly.Msg["CONTROLS_IF_IF_TOOLTIP"] = "Pievienot, noņemt vai mainīt sekciju secību šim \"ja\" blokam.";
Blockly.Msg["CONTROLS_IF_MSG_ELSE"] = "citādi";
Blockly.Msg["CONTROLS_IF_MSG_ELSEIF"] = "citādi, ja";
Blockly.Msg["CONTROLS_IF_MSG_IF"] = "ja";
Blockly.Msg["CONTROLS_IF_TOOLTIP_1"] = "Ja vērtība ir patiesa, tad izpildīt komandas.";
Blockly.Msg["CONTROLS_IF_TOOLTIP_2"] = "Ja vērtība ir patiesa, tad izpildīt pirmo bloku ar komandām. Citādi izpildīt otro bloku ar komandām.";
Blockly.Msg["CONTROLS_IF_TOOLTIP_3"] = "Ja pirmā vērtība ir patiesa, tad izpildīt pirmo bloku ar komandām. Citādi, ja otrā vērtība ir patiesa, izpildīt otro bloku ar komandām.";
Blockly.Msg["CONTROLS_IF_TOOLTIP_4"] = "Ja pirmā vērtība ir patiesa, tad izpildīt pirmo bloku ar komandām. Citādi, ja otrā vērtība ir patiesa, izpildīt otro bloku ar komandām. Ja neviena no vertībām nav patiesa, tad izpildīt pēdējo bloku ar komandām.";
Blockly.Msg["CONTROLS_REPEAT_HELPURL"] = "https://lv.wikipedia.org/wiki/Cikls";
Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"] = "izpildi";
Blockly.Msg["CONTROLS_REPEAT_TITLE"] = "atkārtot %1 reizes";
Blockly.Msg["CONTROLS_REPEAT_TOOLTIP"] = "Izpildīt komandas vairākas reizes.";
Blockly.Msg["CONTROLS_WHILEUNTIL_HELPURL"] = "https://github.com/google/blockly/wiki/Loops#repeat";  // untranslated
Blockly.Msg["CONTROLS_WHILEUNTIL_OPERATOR_UNTIL"] = "atkārtot līdz";
Blockly.Msg["CONTROLS_WHILEUNTIL_OPERATOR_WHILE"] = "atkārtot kamēr";
Blockly.Msg["CONTROLS_WHILEUNTIL_TOOLTIP_UNTIL"] = "Izpildīt komandas, kamēr vērtība ir nepatiesa.";
Blockly.Msg["CONTROLS_WHILEUNTIL_TOOLTIP_WHILE"] = "Izpildīt komandas, kamēr vērtība ir patiesa.";
Blockly.Msg["DELETE_ALL_BLOCKS"] = "Izdzēst visus %1 blokus?";
Blockly.Msg["DELETE_BLOCK"] = "Izmest bloku";
Blockly.Msg["DELETE_VARIABLE"] = "Izdzēst mainīgo \"%1\"";
Blockly.Msg["DELETE_VARIABLE_CONFIRMATION"] = "Mainīgais \"%2\" tiek izmantots %1 vietās. Dzēst?";
Blockly.Msg["DELETE_X_BLOCKS"] = "Izmest %1 blokus";
Blockly.Msg["DIALOG_CANCEL"] = "Atcelt";
Blockly.Msg["DIALOG_OK"] = "Labi";
Blockly.Msg["DISABLE_BLOCK"] = "Atspējot bloku";
Blockly.Msg["DUPLICATE_BLOCK"] = "Dublēt";
Blockly.Msg["DUPLICATE_COMMENT"] = "Duplicate Comment";  // untranslated
Blockly.Msg["ENABLE_BLOCK"] = "Iespējot bloku";
Blockly.Msg["EXPAND_ALL"] = "Izvērst blokus";
Blockly.Msg["EXPAND_BLOCK"] = "Izvērst bloku";
Blockly.Msg["EXTERNAL_INPUTS"] = "Ārējie ievaddati";
Blockly.Msg["HELP"] = "Palīdzība";
Blockly.Msg["INLINE_INPUTS"] = "Iekšējie ievaddati";
Blockly.Msg["LISTS_CREATE_EMPTY_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#create-empty-list";  // untranslated
Blockly.Msg["LISTS_CREATE_EMPTY_TITLE"] = "izveidot tukšu sarakstu";
Blockly.Msg["LISTS_CREATE_EMPTY_TOOLTIP"] = "Izveidot sarakstu bez elementiem tajā.";
Blockly.Msg["LISTS_CREATE_WITH_CONTAINER_TITLE_ADD"] = "saraksts";
Blockly.Msg["LISTS_CREATE_WITH_CONTAINER_TOOLTIP"] = "Pievienot, noņemt vai mainīt sekciju secību šim \"saraksta\" blokam.";
Blockly.Msg["LISTS_CREATE_WITH_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#create-list-with";  // untranslated
Blockly.Msg["LISTS_CREATE_WITH_INPUT_WITH"] = "izveidot sarakstu no";
Blockly.Msg["LISTS_CREATE_WITH_ITEM_TOOLTIP"] = "Pievienot objektu sarakstam.";
Blockly.Msg["LISTS_CREATE_WITH_TOOLTIP"] = "Izveidot sarakstu no jebkura skaita vienību.";
Blockly.Msg["LISTS_GET_INDEX_FIRST"] = "pirmo";
Blockly.Msg["LISTS_GET_INDEX_FROM_END"] = "no beigām numur";
Blockly.Msg["LISTS_GET_INDEX_FROM_START"] = "#";  // untranslated
Blockly.Msg["LISTS_GET_INDEX_GET"] = "paņemt";
Blockly.Msg["LISTS_GET_INDEX_GET_REMOVE"] = "paņemt uz dzēst";
Blockly.Msg["LISTS_GET_INDEX_LAST"] = "pēdējo";
Blockly.Msg["LISTS_GET_INDEX_RANDOM"] = "nejauši izvēlētu";
Blockly.Msg["LISTS_GET_INDEX_REMOVE"] = "dzēst";
Blockly.Msg["LISTS_GET_INDEX_TAIL"] = "";  // untranslated
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_FIRST"] = "Atgriež pirmo saraksta elementu.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_FROM"] = "Atgriež norādīto elementu no saraksta.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_LAST"] = "Atgriež pēdējo saraksta elementu.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_RANDOM"] = "Atgriež nejauši izvēlētu saraksta elementu";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_FIRST"] = "Atgriež un izdzēš saraksta pirmo elementu.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_FROM"] = "Atgriež un izdzēš no saraksta norādīto elementu.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_LAST"] = "Atgriež un izdzēš saraksta pēdējo elementu.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_RANDOM"] = "Atgriež un izdzēš no saraksta nejauši izvēlētu elementu.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_REMOVE_FIRST"] = "Izdēš pirmo saraksta elementu.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_REMOVE_FROM"] = "Izdēš norādīto elementu no saraksta.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_REMOVE_LAST"] = "Izdēš pēdējo saraksta elementu.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_REMOVE_RANDOM"] = "Izdzēš no saraksta nejauši izvēlētu elementu.";
Blockly.Msg["LISTS_GET_SUBLIST_END_FROM_END"] = "līdz pozīcijai no beigām";
Blockly.Msg["LISTS_GET_SUBLIST_END_FROM_START"] = "līdz pozīcijai";
Blockly.Msg["LISTS_GET_SUBLIST_END_LAST"] = "līdz beigām";
Blockly.Msg["LISTS_GET_SUBLIST_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#getting-a-sublist";  // untranslated
Blockly.Msg["LISTS_GET_SUBLIST_START_FIRST"] = "paņemt apakšsarakstu no sākuma";
Blockly.Msg["LISTS_GET_SUBLIST_START_FROM_END"] = "paņemt apakšsarakstu no beigām no pozīcijas";
Blockly.Msg["LISTS_GET_SUBLIST_START_FROM_START"] = "paņemt apakšsarakstu no pozīcijas";
Blockly.Msg["LISTS_GET_SUBLIST_TAIL"] = "";  // untranslated
Blockly.Msg["LISTS_GET_SUBLIST_TOOLTIP"] = "Nokopēt daļu no dotā saraksta.";
Blockly.Msg["LISTS_INDEX_FROM_END_TOOLTIP"] = "Saraksta elementu numerācija no beigām sākas no %1";
Blockly.Msg["LISTS_INDEX_FROM_START_TOOLTIP"] = "Saraksta elementu numerācija sākas no %1";
Blockly.Msg["LISTS_INDEX_OF_FIRST"] = "atrast pirmo elementu, kas vienāds ar";
Blockly.Msg["LISTS_INDEX_OF_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#getting-items-from-a-list";  // untranslated
Blockly.Msg["LISTS_INDEX_OF_LAST"] = "atrast pēdējo elementu, kas vienāds ar";
Blockly.Msg["LISTS_INDEX_OF_TOOLTIP"] = "Atgriež pozīciju sarakstā, kurā atrodas dotais objekts.  Atgriež %1 ja objekts neatrodas sarakstā.";
Blockly.Msg["LISTS_INLIST"] = "sarakstā";
Blockly.Msg["LISTS_ISEMPTY_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#is-empty";  // untranslated
Blockly.Msg["LISTS_ISEMPTY_TITLE"] = "%1 ir tukšs";
Blockly.Msg["LISTS_ISEMPTY_TOOLTIP"] = "Patiess, ja saraksts ir tukšs.";
Blockly.Msg["LISTS_LENGTH_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#length-of";  // untranslated
Blockly.Msg["LISTS_LENGTH_TITLE"] = "%1 garums";
Blockly.Msg["LISTS_LENGTH_TOOLTIP"] = "Atgriež elementu skaitu srakstā.";
Blockly.Msg["LISTS_REPEAT_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#create-list-with";  // untranslated
Blockly.Msg["LISTS_REPEAT_TITLE"] = "saraksts no %1 atkārtots %2 reizes";
Blockly.Msg["LISTS_REPEAT_TOOLTIP"] = "Izveido sarakstu, kas sastāv no dotās vērtības noteiktu reižu skaita.";
Blockly.Msg["LISTS_REVERSE_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#reversing-a-list";  // untranslated
Blockly.Msg["LISTS_REVERSE_MESSAGE0"] = "reverse %1";  // untranslated
Blockly.Msg["LISTS_REVERSE_TOOLTIP"] = "Reverse a copy of a list.";  // untranslated
Blockly.Msg["LISTS_SET_INDEX_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#in-list--set";  // untranslated
Blockly.Msg["LISTS_SET_INDEX_INPUT_TO"] = "kā";
Blockly.Msg["LISTS_SET_INDEX_INSERT"] = "ievieto";
Blockly.Msg["LISTS_SET_INDEX_SET"] = "aizvieto";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_INSERT_FIRST"] = "Ievieto elementu saraksta sākumā.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_INSERT_FROM"] = "Ievieto sarakstā elementu norādītajā pozīcijā.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_INSERT_LAST"] = "Pievieno elementu saraksta beigās.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_INSERT_RANDOM"] = "Ievieto sarakstā jaunu elementu nejauši izvēlētā pozīcijā.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_SET_FIRST"] = "Aizvieto elementu saraksta sākumā.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_SET_FROM"] = "Aizvieto sarakstā elementu norādītajā pozīcijā.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_SET_LAST"] = "Aizvieto elementu saraksta beigās.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_SET_RANDOM"] = "Aizvieto sarakstā elementu nejauši izvēlētā pozīcijā.";
Blockly.Msg["LISTS_SORT_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#sorting-a-list";  // untranslated
Blockly.Msg["LISTS_SORT_ORDER_ASCENDING"] = "augošā";
Blockly.Msg["LISTS_SORT_ORDER_DESCENDING"] = "dilstošā";
Blockly.Msg["LISTS_SORT_TITLE"] = "Sakārtot sarakstu no %3 elementiem %2 secībā %1";
Blockly.Msg["LISTS_SORT_TOOLTIP"] = "Saraksta sakārtota kopija.";
Blockly.Msg["LISTS_SORT_TYPE_IGNORECASE"] = "pēc alfabēta, ignorēt mazos/lielos burtus";
Blockly.Msg["LISTS_SORT_TYPE_NUMERIC"] = "skaitliski";
Blockly.Msg["LISTS_SORT_TYPE_TEXT"] = "pēc alfabēta";
Blockly.Msg["LISTS_SPLIT_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#splitting-strings-and-joining-lists";  // untranslated
Blockly.Msg["LISTS_SPLIT_LIST_FROM_TEXT"] = "vārdu saraksts no teksta";
Blockly.Msg["LISTS_SPLIT_TEXT_FROM_LIST"] = "izveidot tekstu no saraksta";
Blockly.Msg["LISTS_SPLIT_TOOLTIP_JOIN"] = "Apvienot tekstu izmantojot atdalītāju.";
Blockly.Msg["LISTS_SPLIT_TOOLTIP_SPLIT"] = "Sadalīt tekstu vārdos izmantojot atdalītāju.";
Blockly.Msg["LISTS_SPLIT_WITH_DELIMITER"] = "ar atdalītāju";
Blockly.Msg["LOGIC_BOOLEAN_FALSE"] = "aplams";
Blockly.Msg["LOGIC_BOOLEAN_HELPURL"] = "https://github.com/google/blockly/wiki/Logic#values";  // untranslated
Blockly.Msg["LOGIC_BOOLEAN_TOOLTIP"] = "Atgriež rezultātu \"patiess\" vai \"aplams\".";
Blockly.Msg["LOGIC_BOOLEAN_TRUE"] = "patiess";
Blockly.Msg["LOGIC_COMPARE_HELPURL"] = "https://lv.wikipedia.org/wiki/Nevien%C4%81d%C4%ABba";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_EQ"] = "Patiess, ja abas puses ir vienādas.";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_GT"] = "Patiess, ja kreisā puse ir lielāka par labo pusi.";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_GTE"] = "Patiess, ja kreisā puse ir lielāka vai vienāda ar labo pusi.";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_LT"] = "Patiess, ja kreisā puse ir mazāka par labo pusi.";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_LTE"] = "Patiess, ja kreisā puse ir mazāka vai vienāda ar labo pusi.";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_NEQ"] = "Patiess, ja abas puses nav vienādas.";
Blockly.Msg["LOGIC_NEGATE_HELPURL"] = "https://github.com/google/blockly/wiki/Logic#not";  // untranslated
Blockly.Msg["LOGIC_NEGATE_TITLE"] = "ne %1";
Blockly.Msg["LOGIC_NEGATE_TOOLTIP"] = "Patiess, ja arguments ir aplams.";
Blockly.Msg["LOGIC_NULL"] = "nekas";
Blockly.Msg["LOGIC_NULL_HELPURL"] = "https://en.wikipedia.org/wiki/Nullable_type";  // untranslated
Blockly.Msg["LOGIC_NULL_TOOLTIP"] = "Atgriež neko.";
Blockly.Msg["LOGIC_OPERATION_AND"] = "un";
Blockly.Msg["LOGIC_OPERATION_HELPURL"] = "https://github.com/google/blockly/wiki/Logic#logical-operations";  // untranslated
Blockly.Msg["LOGIC_OPERATION_OR"] = "vai";
Blockly.Msg["LOGIC_OPERATION_TOOLTIP_AND"] = "Patiess, ja abas puses ir patiesas.";
Blockly.Msg["LOGIC_OPERATION_TOOLTIP_OR"] = "Patiess, ja vismaz viena puse ir patiesa.";
Blockly.Msg["LOGIC_TERNARY_CONDITION"] = "nosacījums";
Blockly.Msg["LOGIC_TERNARY_HELPURL"] = "https://en.wikipedia.org/wiki/%3F:";  // untranslated
Blockly.Msg["LOGIC_TERNARY_IF_FALSE"] = "ja aplams";
Blockly.Msg["LOGIC_TERNARY_IF_TRUE"] = "ja patiess";
Blockly.Msg["LOGIC_TERNARY_TOOLTIP"] = "Pārbaudīt nosacījumu. Ja 'nosacījums' ir patiess, atgriež vērtību 'ja patiess', pretējā gadījumā vērtību 'ja aplams'.";
Blockly.Msg["MATH_ADDITION_SYMBOL"] = "+";  // untranslated
Blockly.Msg["MATH_ARITHMETIC_HELPURL"] = "https://lv.wikipedia.org/wiki/Aritm%C4%93tika";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_ADD"] = "Atgriež divu skaitļu summu.";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_DIVIDE"] = "Atgriež divu skaitļu dalījumu.";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_MINUS"] = "Atgriež divu skaitļu starpību.";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_MULTIPLY"] = "Atgriež divu skaitļu reizinājumu.";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_POWER"] = "Atgriež pirmo skaitli kāpinātu pakāpē otrais skaitlis.";
Blockly.Msg["MATH_ATAN2_HELPURL"] = "https://en.wikipedia.org/wiki/Atan2";  // untranslated
Blockly.Msg["MATH_ATAN2_TITLE"] = "atan2 of X:%1 Y:%2";  // untranslated
Blockly.Msg["MATH_ATAN2_TOOLTIP"] = "Return the arctangent of point (X, Y) in degrees from -180 to 180.";  // untranslated
Blockly.Msg["MATH_CHANGE_HELPURL"] = "https://en.wikipedia.org/wiki/Programming_idiom#Incrementing_a_counter";
Blockly.Msg["MATH_CHANGE_TITLE"] = "izmainīt %1 par %2";
Blockly.Msg["MATH_CHANGE_TOOLTIP"] = "Pieskaitīt doto skaitli mainīgajam '%1'.";
Blockly.Msg["MATH_CONSTANT_HELPURL"] = "https://en.wikipedia.org/wiki/Mathematical_constant";
Blockly.Msg["MATH_CONSTANT_TOOLTIP"] = "Atgriež kādu no matemātikas konstantēm: π (3.141…), e (2.718…), φ (1.618…), √(2) (1.414…), √(½) (0.707…), ∞ (bezgalība).";
Blockly.Msg["MATH_CONSTRAIN_HELPURL"] = "https://en.wikipedia.org/wiki/Clamping_(graphics)";  // untranslated
Blockly.Msg["MATH_CONSTRAIN_TITLE"] = "ierobežot %1 no %2 līdz %3";
Blockly.Msg["MATH_CONSTRAIN_TOOLTIP"] = "Ierobežo skaitli no noteiktajās robežās (ieskaitot galapunktus).";
Blockly.Msg["MATH_DIVISION_SYMBOL"] = "÷";  // untranslated
Blockly.Msg["MATH_IS_DIVISIBLE_BY"] = "dalās bez atlikuma ar";
Blockly.Msg["MATH_IS_EVEN"] = "ir pāra";
Blockly.Msg["MATH_IS_NEGATIVE"] = "ir negatīvs";
Blockly.Msg["MATH_IS_ODD"] = "ir nepāra";
Blockly.Msg["MATH_IS_POSITIVE"] = "ir pozitīvs";
Blockly.Msg["MATH_IS_PRIME"] = "ir pirmskaitlis";
Blockly.Msg["MATH_IS_TOOLTIP"] = "Pārbauda, vai skaitlis ir pāra, nepāra, vesels, pozitīvs, negatīvs vai dalās ar noteiktu skaitli. Atgriež \"patiess\" vai \"aplams\".";
Blockly.Msg["MATH_IS_WHOLE"] = "ir vesels";
Blockly.Msg["MATH_MODULO_HELPURL"] = "https://en.wikipedia.org/wiki/Modulo_operation";
Blockly.Msg["MATH_MODULO_TITLE"] = "atlikums no %1 ÷ %2";
Blockly.Msg["MATH_MODULO_TOOLTIP"] = "Atlikums no divu skaitļu dalījuma.";
Blockly.Msg["MATH_MULTIPLICATION_SYMBOL"] = "×";  // untranslated
Blockly.Msg["MATH_NUMBER_HELPURL"] = "https://lv.wikipedia.org/wiki/Skaitlis";
Blockly.Msg["MATH_NUMBER_TOOLTIP"] = "Skaitlis.";
Blockly.Msg["MATH_ONLIST_HELPURL"] = "";  // untranslated
Blockly.Msg["MATH_ONLIST_OPERATOR_AVERAGE"] = "vidējais";
Blockly.Msg["MATH_ONLIST_OPERATOR_MAX"] = "lielākais";
Blockly.Msg["MATH_ONLIST_OPERATOR_MEDIAN"] = "mediāna";
Blockly.Msg["MATH_ONLIST_OPERATOR_MIN"] = "mazākais";
Blockly.Msg["MATH_ONLIST_OPERATOR_MODE"] = "moda";
Blockly.Msg["MATH_ONLIST_OPERATOR_RANDOM"] = "nejaušs";
Blockly.Msg["MATH_ONLIST_OPERATOR_STD_DEV"] = "standartnovirze";
Blockly.Msg["MATH_ONLIST_OPERATOR_SUM"] = "summa";
Blockly.Msg["MATH_ONLIST_TOOLTIP_AVERAGE"] = "Atgriež dotā saraksta vidējo aritmētisko vērtību.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_MAX"] = "Atgriež lielāko vērtību no saraksta.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_MEDIAN"] = "Atgriež dotā saraksta mediānas vērtību.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_MIN"] = "Atgriež mazāko vērtību no saraksta.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_MODE"] = "Atgriež dotā saraksta biežāk sastopamās vērtības (modas).";
Blockly.Msg["MATH_ONLIST_TOOLTIP_RANDOM"] = "Atgriež nejauši izvēlētu vērtību no dotā saraksta.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_STD_DEV"] = "Atgriež dotā saraksta standartnovirzi.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_SUM"] = "Saskaitīt visus skaitļus no dotā saraksta.";
Blockly.Msg["MATH_POWER_SYMBOL"] = "^";  // untranslated
Blockly.Msg["MATH_RANDOM_FLOAT_HELPURL"] = "https://en.wikipedia.org/wiki/Random_number_generation";
Blockly.Msg["MATH_RANDOM_FLOAT_TITLE_RANDOM"] = "nejaušs skaitlis [0..1)";
Blockly.Msg["MATH_RANDOM_FLOAT_TOOLTIP"] = "Atgriež nejaušu reālo skaitli robežās no 0 (iekļaujot) līdz 1 (neiekļaujot).";
Blockly.Msg["MATH_RANDOM_INT_HELPURL"] = "https://en.wikipedia.org/wiki/Random_number_generation";
Blockly.Msg["MATH_RANDOM_INT_TITLE"] = "nejaušs vesels skaitlis no %1 līdz %2";
Blockly.Msg["MATH_RANDOM_INT_TOOLTIP"] = "Atgriež nejaušu veselu skaitli dotajās robežās (iekļaujot galapunktus)";
Blockly.Msg["MATH_ROUND_HELPURL"] = "https://en.wikipedia.org/wiki/Rounding";
Blockly.Msg["MATH_ROUND_OPERATOR_ROUND"] = "noapaļot";
Blockly.Msg["MATH_ROUND_OPERATOR_ROUNDDOWN"] = "apaļot uz leju";
Blockly.Msg["MATH_ROUND_OPERATOR_ROUNDUP"] = "apaļot uz augšu";
Blockly.Msg["MATH_ROUND_TOOLTIP"] = "Noapaļot skaitli uz augšu vai uz leju.";
Blockly.Msg["MATH_SINGLE_HELPURL"] = "https://lv.wikipedia.org/wiki/Kvadr%C4%81tsakne";
Blockly.Msg["MATH_SINGLE_OP_ABSOLUTE"] = "absolūtā vērtība";
Blockly.Msg["MATH_SINGLE_OP_ROOT"] = "kvadrātsakne";
Blockly.Msg["MATH_SINGLE_TOOLTIP_ABS"] = "Atgriež skaitļa absolūto vērtību.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_EXP"] = "Atgriež e pakāpē dotais skaitlis.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_LN"] = "Atgriež skaitļa naturālo logaritmu.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_LOG10"] = "Atgriež skaitļa logaritmu pie bāzes 10.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_NEG"] = "Atgriež pretējo skaitli.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_POW10"] = "Atgriež 10 pakāpē dotais skaitlis.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_ROOT"] = "Atgriež skaitļa kvadrātsakni.";
Blockly.Msg["MATH_SUBTRACTION_SYMBOL"] = "-";  // untranslated
Blockly.Msg["MATH_TRIG_ACOS"] = "acos";  // untranslated
Blockly.Msg["MATH_TRIG_ASIN"] = "asin";  // untranslated
Blockly.Msg["MATH_TRIG_ATAN"] = "atan";  // untranslated
Blockly.Msg["MATH_TRIG_COS"] = "cos";  // untranslated
Blockly.Msg["MATH_TRIG_HELPURL"] = "https://lv.wikipedia.org/wiki/Trigonometrisk%C4%81s_funkcijas";
Blockly.Msg["MATH_TRIG_SIN"] = "sin";  // untranslated
Blockly.Msg["MATH_TRIG_TAN"] = "tan";  // untranslated
Blockly.Msg["MATH_TRIG_TOOLTIP_ACOS"] = "Arkkosinuss (grādos).";
Blockly.Msg["MATH_TRIG_TOOLTIP_ASIN"] = "Arksinuss (grādos).";
Blockly.Msg["MATH_TRIG_TOOLTIP_ATAN"] = "Arktangenss (grādos).";
Blockly.Msg["MATH_TRIG_TOOLTIP_COS"] = "Kosinuss no grādiem (nevis radiāniem).";
Blockly.Msg["MATH_TRIG_TOOLTIP_SIN"] = "Sinuss no grādiem (nevis radiāniem).";
Blockly.Msg["MATH_TRIG_TOOLTIP_TAN"] = "Tangenss no grādiem (nevis radiāniem).";
Blockly.Msg["NEW_COLOUR_VARIABLE"] = "Create colour variable...";  // untranslated
Blockly.Msg["NEW_NUMBER_VARIABLE"] = "Create number variable...";  // untranslated
Blockly.Msg["NEW_STRING_VARIABLE"] = "Create string variable...";  // untranslated
Blockly.Msg["NEW_VARIABLE"] = "Izveidot mainīgo...";
Blockly.Msg["NEW_VARIABLE_TITLE"] = "Jaunā mainīgā vārds:";
Blockly.Msg["NEW_VARIABLE_TYPE_TITLE"] = "New variable type:";  // untranslated
Blockly.Msg["ORDINAL_NUMBER_SUFFIX"] = "";  // untranslated
Blockly.Msg["PROCEDURES_ALLOW_STATEMENTS"] = "atļaut apakškomandas";
Blockly.Msg["PROCEDURES_BEFORE_PARAMS"] = "ar:";
Blockly.Msg["PROCEDURES_CALLNORETURN_HELPURL"] = "https://en.wikipedia.org/wiki/Procedure_%28computer_science%29";
Blockly.Msg["PROCEDURES_CALLNORETURN_TOOLTIP"] = "Izpildīt iepriekš definētu funkcju '%1'.";
Blockly.Msg["PROCEDURES_CALLRETURN_HELPURL"] = "https://en.wikipedia.org/wiki/Procedure_%28computer_science%29";
Blockly.Msg["PROCEDURES_CALLRETURN_TOOLTIP"] = "Izpildīt iepriekš definētu funkcju '%1' un izmantot tās rezultātu.";
Blockly.Msg["PROCEDURES_CALL_BEFORE_PARAMS"] = "ar:";
Blockly.Msg["PROCEDURES_CREATE_DO"] = "Izveidot '%1' izsaukumu";
Blockly.Msg["PROCEDURES_DEFNORETURN_COMMENT"] = "Funkcijas apraksts...";
Blockly.Msg["PROCEDURES_DEFNORETURN_DO"] = "";  // untranslated
Blockly.Msg["PROCEDURES_DEFNORETURN_HELPURL"] = "https://en.wikipedia.org/wiki/Subroutine";  // untranslated
Blockly.Msg["PROCEDURES_DEFNORETURN_PROCEDURE"] = "darīt kaut ko";
Blockly.Msg["PROCEDURES_DEFNORETURN_TITLE"] = "funkcija";
Blockly.Msg["PROCEDURES_DEFNORETURN_TOOLTIP"] = "Izveido funkciju, kas neatgriež rezultātu.";
Blockly.Msg["PROCEDURES_DEFRETURN_HELPURL"] = "https://en.wikipedia.org/wiki/Subroutine";  // untranslated
Blockly.Msg["PROCEDURES_DEFRETURN_RETURN"] = "atgriezt";
Blockly.Msg["PROCEDURES_DEFRETURN_TOOLTIP"] = "Izveido funkciju, kas atgriež rezultātu.";
Blockly.Msg["PROCEDURES_DEF_DUPLICATE_WARNING"] = "Brīdinājums: funkcijai ir vienādi argumenti.";
Blockly.Msg["PROCEDURES_HIGHLIGHT_DEF"] = "Izcelt funkcijas definīciju";
Blockly.Msg["PROCEDURES_IFRETURN_HELPURL"] = "http://c2.com/cgi/wiki?GuardClause";  // untranslated
Blockly.Msg["PROCEDURES_IFRETURN_TOOLTIP"] = "Ja pirmā vērtība ir \"patiesa\", tad atgriezt otro vērtību.";
Blockly.Msg["PROCEDURES_IFRETURN_WARNING"] = "Brīdinājums: Šo bloku var izmantot tikai funkcijas definīcijā.";
Blockly.Msg["PROCEDURES_MUTATORARG_TITLE"] = "arguments:";
Blockly.Msg["PROCEDURES_MUTATORARG_TOOLTIP"] = "Pievienot funkcijai argumentu.";
Blockly.Msg["PROCEDURES_MUTATORCONTAINER_TITLE"] = "argumenti";
Blockly.Msg["PROCEDURES_MUTATORCONTAINER_TOOLTIP"] = "Pievienot, pārkārtot vai dzēst funkcijas argumentus.";
Blockly.Msg["REDO"] = "Atcelt atsaukšanu";
Blockly.Msg["REMOVE_COMMENT"] = "Noņemt komentāru";
Blockly.Msg["RENAME_VARIABLE"] = "Pārdēvēt mainīgo...";
Blockly.Msg["RENAME_VARIABLE_TITLE"] = "Pārdēvējiet visus '%1' mainīgos:";
Blockly.Msg["TEXT_APPEND_HELPURL"] = "https://github.com/google/blockly/wiki/Text#text-modification";  // untranslated
Blockly.Msg["TEXT_APPEND_TITLE"] = "tekstam %1 pievienot tekstu %2";
Blockly.Msg["TEXT_APPEND_TOOLTIP"] = "Pievienot tekstu mainīgajam '%1'.";
Blockly.Msg["TEXT_CHANGECASE_HELPURL"] = "https://github.com/google/blockly/wiki/Text#adjusting-text-case";  // untranslated
Blockly.Msg["TEXT_CHANGECASE_OPERATOR_LOWERCASE"] = "kā mazie burti";
Blockly.Msg["TEXT_CHANGECASE_OPERATOR_TITLECASE"] = "kā Nosaukuma Burti";
Blockly.Msg["TEXT_CHANGECASE_OPERATOR_UPPERCASE"] = "kā LIELIE BURTI";
Blockly.Msg["TEXT_CHANGECASE_TOOLTIP"] = "Atgriež teksta kopiju ar mainītiem lielajiem/mazajiem burtiem.";
Blockly.Msg["TEXT_CHARAT_FIRST"] = "paņemt pirmo burtu";
Blockly.Msg["TEXT_CHARAT_FROM_END"] = "paņemt no beigām burtu #";
Blockly.Msg["TEXT_CHARAT_FROM_START"] = "paņemt burtu #";
Blockly.Msg["TEXT_CHARAT_HELPURL"] = "https://github.com/google/blockly/wiki/Text#extracting-text";  // untranslated
Blockly.Msg["TEXT_CHARAT_LAST"] = "paņemt pēdējo burtu";
Blockly.Msg["TEXT_CHARAT_RANDOM"] = "paņemt nejaušu burtu";
Blockly.Msg["TEXT_CHARAT_TAIL"] = "";  // untranslated
Blockly.Msg["TEXT_CHARAT_TITLE"] = "in text %1 %2";  // untranslated
Blockly.Msg["TEXT_CHARAT_TOOLTIP"] = "Atgriež burtu dotajā pozīcijā.";
Blockly.Msg["TEXT_COUNT_HELPURL"] = "https://github.com/google/blockly/wiki/Text#counting-substrings";  // untranslated
Blockly.Msg["TEXT_COUNT_MESSAGE0"] = "count %1 in %2";  // untranslated
Blockly.Msg["TEXT_COUNT_TOOLTIP"] = "Count how many times some text occurs within some other text.";  // untranslated
Blockly.Msg["TEXT_CREATE_JOIN_ITEM_TOOLTIP"] = "Pievienot tekstam objektu.";
Blockly.Msg["TEXT_CREATE_JOIN_TITLE_JOIN"] = "savienot";
Blockly.Msg["TEXT_CREATE_JOIN_TOOLTIP"] = "Pievienot, noņemt vai mainīt sekciju secību šim \"teksta\" blokam.";
Blockly.Msg["TEXT_GET_SUBSTRING_END_FROM_END"] = "līdz burtam nr (no beigām)";
Blockly.Msg["TEXT_GET_SUBSTRING_END_FROM_START"] = "līdz burtam nr";
Blockly.Msg["TEXT_GET_SUBSTRING_END_LAST"] = "līdz pēdējam burtam";
Blockly.Msg["TEXT_GET_SUBSTRING_HELPURL"] = "https://github.com/google/blockly/wiki/Text#extracting-a-region-of-text";  // untranslated
Blockly.Msg["TEXT_GET_SUBSTRING_INPUT_IN_TEXT"] = "no teksta";
Blockly.Msg["TEXT_GET_SUBSTRING_START_FIRST"] = "paņemt apakšvirkni no sākuma";
Blockly.Msg["TEXT_GET_SUBSTRING_START_FROM_END"] = "paņemt apakšvirkni no beigām sākot ar burta nr";
Blockly.Msg["TEXT_GET_SUBSTRING_START_FROM_START"] = "paņemt apakšvirkni sākot no burta nr";
Blockly.Msg["TEXT_GET_SUBSTRING_TAIL"] = "";  // untranslated
Blockly.Msg["TEXT_GET_SUBSTRING_TOOLTIP"] = "Atgriež norādīto teksta daļu.";
Blockly.Msg["TEXT_INDEXOF_HELPURL"] = "https://github.com/google/blockly/wiki/Text#finding-text";  // untranslated
Blockly.Msg["TEXT_INDEXOF_OPERATOR_FIRST"] = "meklēt pirmo vietu, kur sākas teksts";
Blockly.Msg["TEXT_INDEXOF_OPERATOR_LAST"] = "meklēt pēdējo vietu, kur sākas teksts";
Blockly.Msg["TEXT_INDEXOF_TITLE"] = "tekstā %1 %2 %3";
Blockly.Msg["TEXT_INDEXOF_TOOLTIP"] = "Meklē pirmā teksta rindu otrajā tekstā.  Atgriež pozīciju otrajā tekstā, kurā sākas pirmais teksts. Atgriež %1 ja pirmā teksta rinda nav atrasta.";
Blockly.Msg["TEXT_ISEMPTY_HELPURL"] = "https://github.com/google/blockly/wiki/Text#checking-for-empty-text";  // untranslated
Blockly.Msg["TEXT_ISEMPTY_TITLE"] = "%1 ir tukšs";
Blockly.Msg["TEXT_ISEMPTY_TOOLTIP"] = "Patiess, ja teksts ir tukšs.";
Blockly.Msg["TEXT_JOIN_HELPURL"] = "https://github.com/google/blockly/wiki/Text#text-creation";  // untranslated
Blockly.Msg["TEXT_JOIN_TITLE_CREATEWITH"] = "veidot tekstu no";
Blockly.Msg["TEXT_JOIN_TOOLTIP"] = "Izveidot tekstu savienojot dotos argumentus.";
Blockly.Msg["TEXT_LENGTH_HELPURL"] = "https://github.com/google/blockly/wiki/Text#text-modification";  // untranslated
Blockly.Msg["TEXT_LENGTH_TITLE"] = "garums tekstam %1";
Blockly.Msg["TEXT_LENGTH_TOOLTIP"] = "Atgriež burtu skaitu (ieskaitot atstarpes) dotajā tekstā.";
Blockly.Msg["TEXT_PRINT_HELPURL"] = "https://github.com/google/blockly/wiki/Text#printing-text";  // untranslated
Blockly.Msg["TEXT_PRINT_TITLE"] = "parādīt %1";
Blockly.Msg["TEXT_PRINT_TOOLTIP"] = "Parādīt norādīto tekstu vai skaitli.";
Blockly.Msg["TEXT_PROMPT_HELPURL"] = "https://github.com/google/blockly/wiki/Text#getting-input-from-the-user";  // untranslated
Blockly.Msg["TEXT_PROMPT_TOOLTIP_NUMBER"] = "Palūgt lietotāju ievadīt skaitli.";
Blockly.Msg["TEXT_PROMPT_TOOLTIP_TEXT"] = "Palūgt lietotāju ievadīt tekstu.";
Blockly.Msg["TEXT_PROMPT_TYPE_NUMBER"] = "palūgt ievadīt skaitli ar ziņu";
Blockly.Msg["TEXT_PROMPT_TYPE_TEXT"] = "palūgt ievadīt tekstu ar ziņu";
Blockly.Msg["TEXT_REPLACE_HELPURL"] = "https://github.com/google/blockly/wiki/Text#replacing-substrings";  // untranslated
Blockly.Msg["TEXT_REPLACE_MESSAGE0"] = "replace %1 with %2 in %3";  // untranslated
Blockly.Msg["TEXT_REPLACE_TOOLTIP"] = "Replace all occurances of some text within some other text.";  // untranslated
Blockly.Msg["TEXT_REVERSE_HELPURL"] = "https://github.com/google/blockly/wiki/Text#reversing-text";  // untranslated
Blockly.Msg["TEXT_REVERSE_MESSAGE0"] = "reverse %1";  // untranslated
Blockly.Msg["TEXT_REVERSE_TOOLTIP"] = "Reverses the order of the characters in the text.";  // untranslated
Blockly.Msg["TEXT_TEXT_HELPURL"] = "https://en.wikipedia.org/wiki/String_(computer_science)";
Blockly.Msg["TEXT_TEXT_TOOLTIP"] = "Burts, vārds vai jebkāda teksta rinda.";
Blockly.Msg["TEXT_TRIM_HELPURL"] = "https://github.com/google/blockly/wiki/Text#trimming-removing-spaces";  // untranslated
Blockly.Msg["TEXT_TRIM_OPERATOR_BOTH"] = "Dzēst atstarpes no abām pusēm";
Blockly.Msg["TEXT_TRIM_OPERATOR_LEFT"] = "Dzēst atstarpes no sākuma";
Blockly.Msg["TEXT_TRIM_OPERATOR_RIGHT"] = "Dzēst atstarpes no beigām";
Blockly.Msg["TEXT_TRIM_TOOLTIP"] = "Atgriež teksta kopiju ar noņemtām atstarpēm vienā vai otrā galā.";
Blockly.Msg["TODAY"] = "Šodiena";
Blockly.Msg["UNDO"] = "Atsaukt";
Blockly.Msg["UNNAMED_KEY"] = "nenosaukts";
Blockly.Msg["VARIABLES_DEFAULT_NAME"] = "vienums";
Blockly.Msg["VARIABLES_GET_CREATE_SET"] = "Izveidot piešķiršanu mainīgajam %1";
Blockly.Msg["VARIABLES_GET_HELPURL"] = "https://github.com/google/blockly/wiki/Variables#get";  // untranslated
Blockly.Msg["VARIABLES_GET_TOOLTIP"] = "Atgriež mainīgā vērtību.";
Blockly.Msg["VARIABLES_SET"] = "piešķirt mainīgajam %1 vērtību %2";
Blockly.Msg["VARIABLES_SET_CREATE_GET"] = "Izveidot 'ņem %1'";
Blockly.Msg["VARIABLES_SET_HELPURL"] = "https://github.com/google/blockly/wiki/Variables#set";  // untranslated
Blockly.Msg["VARIABLES_SET_TOOLTIP"] = "Piešķirt mainīgajam vērtību.";
Blockly.Msg["VARIABLE_ALREADY_EXISTS"] = "Mainīgais '%1' jau eksistē.";
Blockly.Msg["VARIABLE_ALREADY_EXISTS_FOR_ANOTHER_TYPE"] = "A variable named '%1' already exists for another type: '%2'.";  // untranslated
Blockly.Msg["WORKSPACE_ARIA_LABEL"] = "Blockly Workspace";  // untranslated
Blockly.Msg["WORKSPACE_COMMENT_DEFAULT_TEXT"] = "Say something...";  // untranslated
Blockly.Msg["CONTROLS_FOREACH_INPUT_DO"] = Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"];
Blockly.Msg["CONTROLS_FOR_INPUT_DO"] = Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"];
Blockly.Msg["CONTROLS_IF_ELSEIF_TITLE_ELSEIF"] = Blockly.Msg["CONTROLS_IF_MSG_ELSEIF"];
Blockly.Msg["CONTROLS_IF_ELSE_TITLE_ELSE"] = Blockly.Msg["CONTROLS_IF_MSG_ELSE"];
Blockly.Msg["CONTROLS_IF_IF_TITLE_IF"] = Blockly.Msg["CONTROLS_IF_MSG_IF"];
Blockly.Msg["CONTROLS_IF_MSG_THEN"] = Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"];
Blockly.Msg["CONTROLS_WHILEUNTIL_INPUT_DO"] = Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"];
Blockly.Msg["LISTS_CREATE_WITH_ITEM_TITLE"] = Blockly.Msg["VARIABLES_DEFAULT_NAME"];
Blockly.Msg["LISTS_GET_INDEX_HELPURL"] = Blockly.Msg["LISTS_INDEX_OF_HELPURL"];
Blockly.Msg["LISTS_GET_INDEX_INPUT_IN_LIST"] = Blockly.Msg["LISTS_INLIST"];
Blockly.Msg["LISTS_GET_SUBLIST_INPUT_IN_LIST"] = Blockly.Msg["LISTS_INLIST"];
Blockly.Msg["LISTS_INDEX_OF_INPUT_IN_LIST"] = Blockly.Msg["LISTS_INLIST"];
Blockly.Msg["LISTS_SET_INDEX_INPUT_IN_LIST"] = Blockly.Msg["LISTS_INLIST"];
Blockly.Msg["MATH_CHANGE_TITLE_ITEM"] = Blockly.Msg["VARIABLES_DEFAULT_NAME"];
Blockly.Msg["PROCEDURES_DEFRETURN_COMMENT"] = Blockly.Msg["PROCEDURES_DEFNORETURN_COMMENT"];
Blockly.Msg["PROCEDURES_DEFRETURN_DO"] = Blockly.Msg["PROCEDURES_DEFNORETURN_DO"];
Blockly.Msg["PROCEDURES_DEFRETURN_PROCEDURE"] = Blockly.Msg["PROCEDURES_DEFNORETURN_PROCEDURE"];
Blockly.Msg["PROCEDURES_DEFRETURN_TITLE"] = Blockly.Msg["PROCEDURES_DEFNORETURN_TITLE"];
Blockly.Msg["TEXT_APPEND_VARIABLE"] = Blockly.Msg["VARIABLES_DEFAULT_NAME"];
Blockly.Msg["TEXT_CREATE_JOIN_ITEM_TITLE_ITEM"] = Blockly.Msg["VARIABLES_DEFAULT_NAME"];

Blockly.Msg["MATH_HUE"] = "230";
Blockly.Msg["LOOPS_HUE"] = "120";
Blockly.Msg["LISTS_HUE"] = "260";
Blockly.Msg["LOGIC_HUE"] = "210";
Blockly.Msg["VARIABLES_HUE"] = "330";
Blockly.Msg["TEXTS_HUE"] = "160";
Blockly.Msg["PROCEDURES_HUE"] = "290";
Blockly.Msg["COLOUR_HUE"] = "20";
Blockly.Msg["VARIABLES_DYNAMIC_HUE"] = "310";