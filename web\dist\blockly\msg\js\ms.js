// This file was automatically generated.  Do not modify.

'use strict';

Blockly.Msg["ADD_COMMENT"] = "Berikan Komen";
Blockly.Msg["CANNOT_DELETE_VARIABLE_PROCEDURE"] = "Can't delete the variable '%1' because it's part of the definition of the function '%2'";  // untranslated
Blockly.Msg["CHANGE_VALUE_TITLE"] = "Ubah nilai:";
Blockly.Msg["CLEAN_UP"] = "Kemaskan Blok";
Blockly.Msg["COLLAPSED_WARNINGS_WARNING"] = "Collapsed blocks contain warnings.";  // untranslated
Blockly.Msg["COLLAPSE_ALL"] = "Lipat Blok²";
Blockly.Msg["COLLAPSE_BLOCK"] = "Lipat Blok";
Blockly.Msg["COLOUR_BLEND_COLOUR1"] = "warna 1";
Blockly.Msg["COLOUR_BLEND_COLOUR2"] = "warna 2";
Blockly.Msg["COLOUR_BLEND_HELPURL"] = "https://meyerweb.com/eric/tools/color-blend/#:::rgbp";  // untranslated
Blockly.Msg["COLOUR_BLEND_RATIO"] = "nisbah";
Blockly.Msg["COLOUR_BLEND_TITLE"] = "adun";
Blockly.Msg["COLOUR_BLEND_TOOLTIP"] = "Campurkan dua warna sekali pada nisbah yang ditentukan (0.0 - 1.0).";
Blockly.Msg["COLOUR_PICKER_HELPURL"] = "https://ms.wikipedia.org/wiki/Warna";
Blockly.Msg["COLOUR_PICKER_TOOLTIP"] = "Pilih satu warna daripada palet.";
Blockly.Msg["COLOUR_RANDOM_HELPURL"] = "http://randomcolour.com";  // untranslated
Blockly.Msg["COLOUR_RANDOM_TITLE"] = "warna rawak";
Blockly.Msg["COLOUR_RANDOM_TOOLTIP"] = "Pilih satu warna secara rawak.";
Blockly.Msg["COLOUR_RGB_BLUE"] = "biru";
Blockly.Msg["COLOUR_RGB_GREEN"] = "hijau";
Blockly.Msg["COLOUR_RGB_HELPURL"] = "https://www.december.com/html/spec/colorpercompact.html";  // untranslated
Blockly.Msg["COLOUR_RGB_RED"] = "merah";
Blockly.Msg["COLOUR_RGB_TITLE"] = "warnakan";
Blockly.Msg["COLOUR_RGB_TOOLTIP"] = "Peroleh satu warna dengan menentukan amaun campuran merah, hijau dan biru. Kesemua nilai haruslah antara 0 hingga 100.";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_HELPURL"] = "https://github.com/google/blockly/wiki/Loops#loop-termination-blocks";  // untranslated
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_OPERATOR_BREAK"] = "hentikan gelung";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_OPERATOR_CONTINUE"] = "teruskan dengan lelaran gelung seterusnya";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_TOOLTIP_BREAK"] = "Keluar dari gelung pengandung.";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_TOOLTIP_CONTINUE"] = "Langkau seluruh gelung yang tinggal dan bersambung dengan lelaran seterusnya.";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_WARNING"] = "Amaran: Blok ini hanya boleh digunakan dalam satu gelung.";
Blockly.Msg["CONTROLS_FOREACH_HELPURL"] = "https://github.com/google/blockly/wiki/Loops#for-each";  // untranslated
Blockly.Msg["CONTROLS_FOREACH_TITLE"] = "untuk setiap perkara %1 dalam senarai %2";
Blockly.Msg["CONTROLS_FOREACH_TOOLTIP"] = "Untuk setiap perkara dalam senarai, tetapkan pembolehubah '%1' pada perkara, kemudian lakukan beberapa perintah.";
Blockly.Msg["CONTROLS_FOR_HELPURL"] = "https://github.com/google/blockly/wiki/Loops#count-with";  // untranslated
Blockly.Msg["CONTROLS_FOR_TITLE"] = "kira dengan %1 dari %2 hingga %3 selang %4";
Blockly.Msg["CONTROLS_FOR_TOOLTIP"] = "Gunakan pembolehubah '%1' pada nilai-nilai dari nombor pangkal hingga nombor hujung, mengira mengikut selang yang ditentukan, dan lakukan blok-blok yang tertentu.";
Blockly.Msg["CONTROLS_IF_ELSEIF_TOOLTIP"] = "Tambah satu syarat kepada blok jika.";
Blockly.Msg["CONTROLS_IF_ELSE_TOOLTIP"] = "Tambah yang terakhir, alihkan semua keadaan ke blok jika.";
Blockly.Msg["CONTROLS_IF_HELPURL"] = "https://github.com/google/blockly/wiki/IfElse";  // untranslated
Blockly.Msg["CONTROLS_IF_IF_TOOLTIP"] = "Tambah, alih keluar, atau susun semula bahagian-bahagian untuk menyusun semula blok jika.";
Blockly.Msg["CONTROLS_IF_MSG_ELSE"] = "lain";
Blockly.Msg["CONTROLS_IF_MSG_ELSEIF"] = "lain jika";
Blockly.Msg["CONTROLS_IF_MSG_IF"] = "jika";
Blockly.Msg["CONTROLS_IF_TOOLTIP_1"] = "Jika nilai yang benar, lakukan beberapa penyata.";
Blockly.Msg["CONTROLS_IF_TOOLTIP_2"] = "Jika suatu nilai benar, lakukan penyata blok pertama.  Jika tidak, bina penyata blok kedua.";
Blockly.Msg["CONTROLS_IF_TOOLTIP_3"] = "Jika nilai yang pertama adalah benar, lakukan penyata pertama blok.  Sebaliknya, jika nilai kedua adalah benar, lakukan penyata blok kedua.";
Blockly.Msg["CONTROLS_IF_TOOLTIP_4"] = "Jika nilai yang pertama adalah benar, lakukan penyata blok pertama.  Sebaliknya, jika nilai kedua adalah benar, lakukan penyata blok kedua.  Jika tiada nilai adalah benar, lakukan penyata blok terakhir.";
Blockly.Msg["CONTROLS_REPEAT_HELPURL"] = "https://en.wikipedia.org/wiki/For_loop";
Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"] = "lakukan";
Blockly.Msg["CONTROLS_REPEAT_TITLE"] = "ulang %1 kali";
Blockly.Msg["CONTROLS_REPEAT_TOOLTIP"] = "Lakukan perintah berulang kali.";
Blockly.Msg["CONTROLS_WHILEUNTIL_HELPURL"] = "https://github.com/google/blockly/wiki/Loops#repeat";  // untranslated
Blockly.Msg["CONTROLS_WHILEUNTIL_OPERATOR_UNTIL"] = "ulangi sehingga";
Blockly.Msg["CONTROLS_WHILEUNTIL_OPERATOR_WHILE"] = "ulangi apabila";
Blockly.Msg["CONTROLS_WHILEUNTIL_TOOLTIP_UNTIL"] = "Lakukan beberapa perintah apabila nilainya palsu (false).";
Blockly.Msg["CONTROLS_WHILEUNTIL_TOOLTIP_WHILE"] = "Lakukan beberapa perintah apabila nilainya benar (true).";
Blockly.Msg["DELETE_ALL_BLOCKS"] = "Hapuskan kesemua %1 blok?";
Blockly.Msg["DELETE_BLOCK"] = "Hapuskan Blok";
Blockly.Msg["DELETE_VARIABLE"] = "Delete the '%1' variable";  // untranslated
Blockly.Msg["DELETE_VARIABLE_CONFIRMATION"] = "Delete %1 uses of the '%2' variable?";  // untranslated
Blockly.Msg["DELETE_X_BLOCKS"] = "Hapuskan %1 Blok";
Blockly.Msg["DIALOG_CANCEL"] = "Batalkan";
Blockly.Msg["DIALOG_OK"] = "OK";
Blockly.Msg["DISABLE_BLOCK"] = "Matikan Blok";
Blockly.Msg["DUPLICATE_BLOCK"] = "Pendua";
Blockly.Msg["DUPLICATE_COMMENT"] = "Duplicate Comment";  // untranslated
Blockly.Msg["ENABLE_BLOCK"] = "Hidupkan Blok";
Blockly.Msg["EXPAND_ALL"] = "Buka Blok²";
Blockly.Msg["EXPAND_BLOCK"] = "Buka Blok";
Blockly.Msg["EXTERNAL_INPUTS"] = "Input Luaran";
Blockly.Msg["HELP"] = "Bantuan";
Blockly.Msg["INLINE_INPUTS"] = "Input Sebaris";
Blockly.Msg["LISTS_CREATE_EMPTY_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#create-empty-list";  // untranslated
Blockly.Msg["LISTS_CREATE_EMPTY_TITLE"] = "Wujudkan senarai kosong";
Blockly.Msg["LISTS_CREATE_EMPTY_TOOLTIP"] = "Kembalikan senarai panjang 0, yang tidak mengandungi rekod data";
Blockly.Msg["LISTS_CREATE_WITH_CONTAINER_TITLE_ADD"] = "senarai";
Blockly.Msg["LISTS_CREATE_WITH_CONTAINER_TOOLTIP"] = "Tambah, alih keluar, atau susun semula bahagian-bahagian untuk menyusun semula senarai blok.";
Blockly.Msg["LISTS_CREATE_WITH_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#create-list-with";
Blockly.Msg["LISTS_CREATE_WITH_INPUT_WITH"] = "wujudkan senarai dengan";
Blockly.Msg["LISTS_CREATE_WITH_ITEM_TOOLTIP"] = "Tambah item ke dalam senarai.";
Blockly.Msg["LISTS_CREATE_WITH_TOOLTIP"] = "Wujudkan senarai dengan apa jua nombor item.";
Blockly.Msg["LISTS_GET_INDEX_FIRST"] = "pertama";
Blockly.Msg["LISTS_GET_INDEX_FROM_END"] = "# dari akhir";
Blockly.Msg["LISTS_GET_INDEX_FROM_START"] = "#";
Blockly.Msg["LISTS_GET_INDEX_GET"] = "dapatkan";
Blockly.Msg["LISTS_GET_INDEX_GET_REMOVE"] = "dapat dan alihkan";
Blockly.Msg["LISTS_GET_INDEX_LAST"] = "terakhir";
Blockly.Msg["LISTS_GET_INDEX_RANDOM"] = "rawak";
Blockly.Msg["LISTS_GET_INDEX_REMOVE"] = "alihkan";
Blockly.Msg["LISTS_GET_INDEX_TAIL"] = "";  // untranslated
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_FIRST"] = "Kembalikan item pertama dalam senarai.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_FROM"] = "Kembalikan item dalam kedudukan yang ditetapkan dalam senarai.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_LAST"] = "Kembalikan item pertama dalam senarai.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_RANDOM"] = "Kembalikan item rawak dalam senarai.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_FIRST"] = "Alihkan dan kembalikan item pertama dalam senarai.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_FROM"] = "Alihkan dan kembalikan item mengikut spesifikasi posisi dalam senarai.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_LAST"] = "Alihkan dan kembalikan item terakhir dalam senarai.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_RANDOM"] = "Alihkan dan kembalikan item rawak dalam senarai.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_REMOVE_FIRST"] = "Alihkan item pertama dalam senarai.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_REMOVE_FROM"] = "Alihkan item pada posisi mengikut spesifikasi dalam senarai.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_REMOVE_LAST"] = "Alihkan item terakhir dalam senarai.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_REMOVE_RANDOM"] = "Alihkan item rawak dalam senarai.";
Blockly.Msg["LISTS_GET_SUBLIST_END_FROM_END"] = "ke  # dari akhir";
Blockly.Msg["LISTS_GET_SUBLIST_END_FROM_START"] = "ke #";
Blockly.Msg["LISTS_GET_SUBLIST_END_LAST"] = "ke akhir";
Blockly.Msg["LISTS_GET_SUBLIST_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#getting-a-sublist";  // untranslated
Blockly.Msg["LISTS_GET_SUBLIST_START_FIRST"] = "dapatkan sub-senarai daripada pertama";
Blockly.Msg["LISTS_GET_SUBLIST_START_FROM_END"] = "dapatkan sub-senarai daripada # daripada terakhir";
Blockly.Msg["LISTS_GET_SUBLIST_START_FROM_START"] = "dapatkan sub-senarai daripada #";
Blockly.Msg["LISTS_GET_SUBLIST_TAIL"] = "";  // untranslated
Blockly.Msg["LISTS_GET_SUBLIST_TOOLTIP"] = "Wujudkan salinan bahagian yang ditentukan dari senarai.";
Blockly.Msg["LISTS_INDEX_FROM_END_TOOLTIP"] = "%1 ialah item terakhir.";
Blockly.Msg["LISTS_INDEX_FROM_START_TOOLTIP"] = "%1 ialah item pertama.";
Blockly.Msg["LISTS_INDEX_OF_FIRST"] = "cari pertama item kejadian";
Blockly.Msg["LISTS_INDEX_OF_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#getting-items-from-a-list";  // untranslated
Blockly.Msg["LISTS_INDEX_OF_LAST"] = "cari kejadian akhir item";
Blockly.Msg["LISTS_INDEX_OF_TOOLTIP"] = "Menyatakan indeks kejadian pertama/terakhir item berkenaan dalam senarai. Menyatakan %1 jika item berkenaan tidak ditemui.";
Blockly.Msg["LISTS_INLIST"] = "dalam senarai";
Blockly.Msg["LISTS_ISEMPTY_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#is-empty";  // untranslated
Blockly.Msg["LISTS_ISEMPTY_TITLE"] = "%1 adalah kosong";
Blockly.Msg["LISTS_ISEMPTY_TOOLTIP"] = "Kembalikan benar jika senarai kosong.";
Blockly.Msg["LISTS_LENGTH_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#length-of";  // untranslated
Blockly.Msg["LISTS_LENGTH_TITLE"] = "panjang %1";
Blockly.Msg["LISTS_LENGTH_TOOLTIP"] = "Kembalikan panjang senarai";
Blockly.Msg["LISTS_REPEAT_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#create-list-with";  // untranslated
Blockly.Msg["LISTS_REPEAT_TITLE"] = "wujudkan senarai dengan item %1 diulangi %2 kali";
Blockly.Msg["LISTS_REPEAT_TOOLTIP"] = "Wujudkan senarai yang terdiri daripada nilai berulang mengikut nombor yang ditentukan.";
Blockly.Msg["LISTS_REVERSE_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#reversing-a-list";  // untranslated
Blockly.Msg["LISTS_REVERSE_MESSAGE0"] = "reverse %1";  // untranslated
Blockly.Msg["LISTS_REVERSE_TOOLTIP"] = "Reverse a copy of a list.";  // untranslated
Blockly.Msg["LISTS_SET_INDEX_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#in-list--set";  // untranslated
Blockly.Msg["LISTS_SET_INDEX_INPUT_TO"] = "sebagai";
Blockly.Msg["LISTS_SET_INDEX_INSERT"] = "masukkan pada";
Blockly.Msg["LISTS_SET_INDEX_SET"] = "set";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_INSERT_FIRST"] = "Selit item pada permulaan senarai.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_INSERT_FROM"] = "Masukkan item pada posisi yand ditentukan dalam senarai.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_INSERT_LAST"] = "Tambahkan item dalam senarai akhir.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_INSERT_RANDOM"] = "Selit item secara rawak di dalam senarai.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_SET_FIRST"] = "Set item pertama dalam senarai.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_SET_FROM"] = "Masukkan item pada posisi yang ditentukan dalam senarai.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_SET_LAST"] = "Set item terakhir dalam senarai.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_SET_RANDOM"] = "Set item rawak dalam senarai.";
Blockly.Msg["LISTS_SORT_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#sorting-a-list";  // untranslated
Blockly.Msg["LISTS_SORT_ORDER_ASCENDING"] = "ascending";  // untranslated
Blockly.Msg["LISTS_SORT_ORDER_DESCENDING"] = "descending";  // untranslated
Blockly.Msg["LISTS_SORT_TITLE"] = "sort %1 %2 %3";  // untranslated
Blockly.Msg["LISTS_SORT_TOOLTIP"] = "Sort a copy of a list.";  // untranslated
Blockly.Msg["LISTS_SORT_TYPE_IGNORECASE"] = "alphabetic, ignore case";  // untranslated
Blockly.Msg["LISTS_SORT_TYPE_NUMERIC"] = "numeric";  // untranslated
Blockly.Msg["LISTS_SORT_TYPE_TEXT"] = "alphabetic";  // untranslated
Blockly.Msg["LISTS_SPLIT_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#splitting-strings-and-joining-lists";
Blockly.Msg["LISTS_SPLIT_LIST_FROM_TEXT"] = "buat senarai dgn teks";
Blockly.Msg["LISTS_SPLIT_TEXT_FROM_LIST"] = "buat teks drpd senarai";
Blockly.Msg["LISTS_SPLIT_TOOLTIP_JOIN"] = "Cantumkan senarai teks menjadi satu teks, dipecahkan oleh delimiter.";
Blockly.Msg["LISTS_SPLIT_TOOLTIP_SPLIT"] = "Pecahkan teks kepada senarai teks, berpecah di setiap delimiter.";
Blockly.Msg["LISTS_SPLIT_WITH_DELIMITER"] = "dengan delimiter";
Blockly.Msg["LOGIC_BOOLEAN_FALSE"] = "palsu";
Blockly.Msg["LOGIC_BOOLEAN_HELPURL"] = "https://github.com/google/blockly/wiki/Logic#values";  // untranslated
Blockly.Msg["LOGIC_BOOLEAN_TOOLTIP"] = "Kembalikan samada benar atau palsu.";
Blockly.Msg["LOGIC_BOOLEAN_TRUE"] = "benar";
Blockly.Msg["LOGIC_COMPARE_HELPURL"] = "https://id.wikipedia.org/wiki/Pertidaksamaan";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_EQ"] = "Kembali benar jika kedua-dua input benar antara satu sama lain.";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_GT"] = "Kembali benar jika input pertama adalah lebih besar daripada input kedua.";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_GTE"] = "Kembali benar jika input pertama adalah lebih besar daripada atau sama dengan input kedua.";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_LT"] = "Kembali benar jika input pertama adalah lebih kecil daripada input kedua.";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_LTE"] = "Kembali benar jika input pertama adalah lebih kecil daripada atau sama dengan input kedua.";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_NEQ"] = "Taip balik benar jika kedua-dua input tidak sama.";
Blockly.Msg["LOGIC_NEGATE_HELPURL"] = "https://github.com/google/blockly/wiki/Logic#not";  // untranslated
Blockly.Msg["LOGIC_NEGATE_TITLE"] = "bukan %1";
Blockly.Msg["LOGIC_NEGATE_TOOLTIP"] = "'Benar' akan dibalas jika inputnya salah. 'Salah' akan dibalas jika inputnya benar.";
Blockly.Msg["LOGIC_NULL"] = "null";
Blockly.Msg["LOGIC_NULL_HELPURL"] = "https://en.wikipedia.org/wiki/Nullable_type";  // untranslated
Blockly.Msg["LOGIC_NULL_TOOLTIP"] = "Returns null.";
Blockly.Msg["LOGIC_OPERATION_AND"] = "dan";
Blockly.Msg["LOGIC_OPERATION_HELPURL"] = "https://github.com/google/blockly/wiki/Logic#logical-operations";  // untranslated
Blockly.Msg["LOGIC_OPERATION_OR"] = "atau";
Blockly.Msg["LOGIC_OPERATION_TOOLTIP_AND"] = "Return true if both inputs are true.";
Blockly.Msg["LOGIC_OPERATION_TOOLTIP_OR"] = "Return true if at least one of the inputs is true.";
Blockly.Msg["LOGIC_TERNARY_CONDITION"] = "test";
Blockly.Msg["LOGIC_TERNARY_HELPURL"] = "https://en.wikipedia.org/wiki/%3F:";  // untranslated
Blockly.Msg["LOGIC_TERNARY_IF_FALSE"] = "Jika palsu";
Blockly.Msg["LOGIC_TERNARY_IF_TRUE"] = "Jika benar";
Blockly.Msg["LOGIC_TERNARY_TOOLTIP"] = "Check the condition in 'test'. If the condition is true, returns the 'if true' value; otherwise returns the 'if false' value.";
Blockly.Msg["MATH_ADDITION_SYMBOL"] = "+";
Blockly.Msg["MATH_ARITHMETIC_HELPURL"] = "https://ms.wikipedia.org/wiki/Aritmetik";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_ADD"] = "Kembalikan jumlah kedua-dua bilangan.";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_DIVIDE"] = "Taip balik hasil bahagi dua nombor tersebut.";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_MINUS"] = "Taip balik hasil tolak dua nombor tersebut.";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_MULTIPLY"] = "Taip balik hasil darab dua nombor tersebut.";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_POWER"] = "Return the first number raised to the power of the second number.";
Blockly.Msg["MATH_ATAN2_HELPURL"] = "https://en.wikipedia.org/wiki/Atan2";  // untranslated
Blockly.Msg["MATH_ATAN2_TITLE"] = "atan2 of X:%1 Y:%2";  // untranslated
Blockly.Msg["MATH_ATAN2_TOOLTIP"] = "Return the arctangent of point (X, Y) in degrees from -180 to 180.";  // untranslated
Blockly.Msg["MATH_CHANGE_HELPURL"] = "https://id.wikipedia.org/wiki/Perjumlahan";
Blockly.Msg["MATH_CHANGE_TITLE"] = "perubahan %1 oleh %2";
Blockly.Msg["MATH_CHANGE_TOOLTIP"] = "Tambah nombor kepada pembolehubah '%1'.";
Blockly.Msg["MATH_CONSTANT_HELPURL"] = "https://ms.wikipedia.org/wiki/Pemalar_matematik";
Blockly.Msg["MATH_CONSTANT_TOOLTIP"] = "Return one of the common constants: π (3.141…), e (2.718…), φ (1.618…), sqrt(2) (1.414…), sqrt(½) (0.707…), or ∞ (infinity).";
Blockly.Msg["MATH_CONSTRAIN_HELPURL"] = "https://en.wikipedia.org/wiki/Clamping_(graphics)";  // untranslated
Blockly.Msg["MATH_CONSTRAIN_TITLE"] = "constrain %1 low %2 high %3";
Blockly.Msg["MATH_CONSTRAIN_TOOLTIP"] = "Constrain a number to be between the specified limits (inclusive).";
Blockly.Msg["MATH_DIVISION_SYMBOL"] = "÷";
Blockly.Msg["MATH_IS_DIVISIBLE_BY"] = "Boleh dibahagikan dengan";
Blockly.Msg["MATH_IS_EVEN"] = "Adalah genap";
Blockly.Msg["MATH_IS_NEGATIVE"] = "negatif";
Blockly.Msg["MATH_IS_ODD"] = "aneh";
Blockly.Msg["MATH_IS_POSITIVE"] = "adalah positif";
Blockly.Msg["MATH_IS_PRIME"] = "is prime";
Blockly.Msg["MATH_IS_TOOLTIP"] = "Check if a number is an even, odd, prime, whole, positive, negative, or if it is divisible by certain number.  Returns true or false.";
Blockly.Msg["MATH_IS_WHOLE"] = "is whole";
Blockly.Msg["MATH_MODULO_HELPURL"] = "https://id.wikipedia.org/wiki/Operasi_modulus";
Blockly.Msg["MATH_MODULO_TITLE"] = "remainder of %1 ÷ %2";
Blockly.Msg["MATH_MODULO_TOOLTIP"] = "Taip balik baki yang didapat daripada pembahagian dua nombor tersebut.";
Blockly.Msg["MATH_MULTIPLICATION_SYMBOL"] = "×";
Blockly.Msg["MATH_NUMBER_HELPURL"] = "https://ms.wikipedia.org/wiki/Nombor";
Blockly.Msg["MATH_NUMBER_TOOLTIP"] = "Suatu nombor.";
Blockly.Msg["MATH_ONLIST_HELPURL"] = "";  // untranslated
Blockly.Msg["MATH_ONLIST_OPERATOR_AVERAGE"] = "purata daripada senarai";
Blockly.Msg["MATH_ONLIST_OPERATOR_MAX"] = "Max senarai";
Blockly.Msg["MATH_ONLIST_OPERATOR_MEDIAN"] = "Median senarai";
Blockly.Msg["MATH_ONLIST_OPERATOR_MIN"] = "min dalam senarai";
Blockly.Msg["MATH_ONLIST_OPERATOR_MODE"] = "jenis senarai";
Blockly.Msg["MATH_ONLIST_OPERATOR_RANDOM"] = "Item rawak daripada senarai";
Blockly.Msg["MATH_ONLIST_OPERATOR_STD_DEV"] = "sisihan piawai bagi senarai";
Blockly.Msg["MATH_ONLIST_OPERATOR_SUM"] = "Jumlah senarai";
Blockly.Msg["MATH_ONLIST_TOOLTIP_AVERAGE"] = "Kembalikan purata (min aritmetik) nilai-nilai angka di dalam senarai.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_MAX"] = "Pulangkan jumlah terbesar dalam senarai.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_MEDIAN"] = "Kembalikan nombor median dalam senarai.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_MIN"] = "Kembalikan nombor terkecil dalam senarai.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_MODE"] = "Kembali senarai item yang paling biasa dalam senarai.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_RANDOM"] = "Kembalikan elemen rawak daripada senarai.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_STD_DEV"] = "Kembali dengan sisihan piawai daripada senarai.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_SUM"] = "Kembalikan jumlah semua nombor dalam senarai.";
Blockly.Msg["MATH_POWER_SYMBOL"] = "^";
Blockly.Msg["MATH_RANDOM_FLOAT_HELPURL"] = "https://en.wikipedia.org/wiki/Random_number_generation";
Blockly.Msg["MATH_RANDOM_FLOAT_TITLE_RANDOM"] = "pecahan rawak";
Blockly.Msg["MATH_RANDOM_FLOAT_TOOLTIP"] = "Kembali sebahagian kecil rawak antara 0.0 (inklusif) dan 1.0 (eksklusif).";
Blockly.Msg["MATH_RANDOM_INT_HELPURL"] = "https://en.wikipedia.org/wiki/Random_number_generation";
Blockly.Msg["MATH_RANDOM_INT_TITLE"] = "integer rawak dari %1ke %2";
Blockly.Msg["MATH_RANDOM_INT_TOOLTIP"] = "Kembalikan integer rawak diantara dua had yang ditentukan, inklusif.";
Blockly.Msg["MATH_ROUND_HELPURL"] = "https://en.wikipedia.org/wiki/Rounding";
Blockly.Msg["MATH_ROUND_OPERATOR_ROUND"] = "pusingan";
Blockly.Msg["MATH_ROUND_OPERATOR_ROUNDDOWN"] = "Pusingan ke bawah";
Blockly.Msg["MATH_ROUND_OPERATOR_ROUNDUP"] = "pusingan ke atas";
Blockly.Msg["MATH_ROUND_TOOLTIP"] = "Bulat nombor yang naik atau turun.";
Blockly.Msg["MATH_SINGLE_HELPURL"] = "https://ms.wikipedia.org/wiki/Punca_kuasa_dua";
Blockly.Msg["MATH_SINGLE_OP_ABSOLUTE"] = "mutlak";
Blockly.Msg["MATH_SINGLE_OP_ROOT"] = "Punca kuasa dua";
Blockly.Msg["MATH_SINGLE_TOOLTIP_ABS"] = "Kembalikan nilai mutlak suatu nombor.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_EXP"] = "Kembalikan e kepada kuasa nombor.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_LN"] = "Kembali dalam logaritma nombor asli.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_LOG10"] = "Kembali logarithm 10 asas nombor.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_NEG"] = "Kembalikan nombor yang songsang.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_POW10"] = "Kembalikan 10 kepada kuasa nombor.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_ROOT"] = "Kembalikan punca kuasa nombor.";
Blockly.Msg["MATH_SUBTRACTION_SYMBOL"] = "-";
Blockly.Msg["MATH_TRIG_ACOS"] = "acos";
Blockly.Msg["MATH_TRIG_ASIN"] = "asin";
Blockly.Msg["MATH_TRIG_ATAN"] = "atan";
Blockly.Msg["MATH_TRIG_COS"] = "cos";
Blockly.Msg["MATH_TRIG_HELPURL"] = "https://ms.wikipedia.org/wiki/Fungsi_trigonometri";
Blockly.Msg["MATH_TRIG_SIN"] = "sin";
Blockly.Msg["MATH_TRIG_TAN"] = "tan";
Blockly.Msg["MATH_TRIG_TOOLTIP_ACOS"] = "Kembali arccosine beberapa nombor.";
Blockly.Msg["MATH_TRIG_TOOLTIP_ASIN"] = "Kembalikan arcsince beberapa nombor.";
Blockly.Msg["MATH_TRIG_TOOLTIP_ATAN"] = "Kembalikan beberapa nombor arctangent.";
Blockly.Msg["MATH_TRIG_TOOLTIP_COS"] = "Kembalikan darjah kosinus (bukan radian).";
Blockly.Msg["MATH_TRIG_TOOLTIP_SIN"] = "Kembalikan darjah sine (bukan radian).";
Blockly.Msg["MATH_TRIG_TOOLTIP_TAN"] = "Kembalikan darjah tangen (bukan radian).";
Blockly.Msg["NEW_COLOUR_VARIABLE"] = "Create colour variable...";  // untranslated
Blockly.Msg["NEW_NUMBER_VARIABLE"] = "Create number variable...";  // untranslated
Blockly.Msg["NEW_STRING_VARIABLE"] = "Create string variable...";  // untranslated
Blockly.Msg["NEW_VARIABLE"] = "Pembolehubah baru...";
Blockly.Msg["NEW_VARIABLE_TITLE"] = "Nama pembolehubah baru:";
Blockly.Msg["NEW_VARIABLE_TYPE_TITLE"] = "New variable type:";  // untranslated
Blockly.Msg["ORDINAL_NUMBER_SUFFIX"] = "";  // untranslated
Blockly.Msg["PROCEDURES_ALLOW_STATEMENTS"] = "bolehkan kenyataan";
Blockly.Msg["PROCEDURES_BEFORE_PARAMS"] = "dengan:";
Blockly.Msg["PROCEDURES_CALLNORETURN_HELPURL"] = "https://ms.wikipedia.org/wiki/Fungsi";
Blockly.Msg["PROCEDURES_CALLNORETURN_TOOLTIP"] = "Run the user-defined function '%1'.";
Blockly.Msg["PROCEDURES_CALLRETURN_HELPURL"] = "https://ms.wikipedia.org/wiki/Fungsi";
Blockly.Msg["PROCEDURES_CALLRETURN_TOOLTIP"] = "Run the user-defined function '%1' and use its output.";
Blockly.Msg["PROCEDURES_CALL_BEFORE_PARAMS"] = "dengan:";
Blockly.Msg["PROCEDURES_CREATE_DO"] = "Hasilkan '%1'";
Blockly.Msg["PROCEDURES_DEFNORETURN_COMMENT"] = "Terangkan fungsi ini...";
Blockly.Msg["PROCEDURES_DEFNORETURN_DO"] = "";  // untranslated
Blockly.Msg["PROCEDURES_DEFNORETURN_HELPURL"] = "https://en.wikipedia.org/wiki/Subroutine";  // untranslated
Blockly.Msg["PROCEDURES_DEFNORETURN_PROCEDURE"] = "Buat sesuatu";
Blockly.Msg["PROCEDURES_DEFNORETURN_TITLE"] = "Untuk";
Blockly.Msg["PROCEDURES_DEFNORETURN_TOOLTIP"] = "Menghasilkan suatu fungsi tanpa output.";
Blockly.Msg["PROCEDURES_DEFRETURN_HELPURL"] = "https://en.wikipedia.org/wiki/Subroutine";  // untranslated
Blockly.Msg["PROCEDURES_DEFRETURN_RETURN"] = "kembali";
Blockly.Msg["PROCEDURES_DEFRETURN_TOOLTIP"] = "Mencipta satu fungsi dengan pengeluaran.";
Blockly.Msg["PROCEDURES_DEF_DUPLICATE_WARNING"] = "Amaran: Fungsi ini mempunyai parameter yang berganda.";
Blockly.Msg["PROCEDURES_HIGHLIGHT_DEF"] = "Serlahkan definisi fungsi";
Blockly.Msg["PROCEDURES_IFRETURN_HELPURL"] = "http://c2.com/cgi/wiki?GuardClause";
Blockly.Msg["PROCEDURES_IFRETURN_TOOLTIP"] = "If a value is true, then return a second value.";
Blockly.Msg["PROCEDURES_IFRETURN_WARNING"] = "Amaran: Blok ini hanya boleh digunakan dalam fungsi definisi.";
Blockly.Msg["PROCEDURES_MUTATORARG_TITLE"] = "Nama input:";
Blockly.Msg["PROCEDURES_MUTATORARG_TOOLTIP"] = "Tambah satu input pada fungsi.";
Blockly.Msg["PROCEDURES_MUTATORCONTAINER_TITLE"] = "Input-input";
Blockly.Msg["PROCEDURES_MUTATORCONTAINER_TOOLTIP"] = "Tambah, alih keluar atau susun semula input pada fungsi ini.";
Blockly.Msg["REDO"] = "Redo";  // untranslated
Blockly.Msg["REMOVE_COMMENT"] = "Padamkan Komen";
Blockly.Msg["RENAME_VARIABLE"] = "Tukar nama pembolehubah...";
Blockly.Msg["RENAME_VARIABLE_TITLE"] = "Tukar nama semua pembolehubah '%1' kepada:";
Blockly.Msg["TEXT_APPEND_HELPURL"] = "https://github.com/google/blockly/wiki/Text#text-modification";  // untranslated
Blockly.Msg["TEXT_APPEND_TITLE"] = "to %1 append text %2";  // untranslated
Blockly.Msg["TEXT_APPEND_TOOLTIP"] = "Append some text to variable '%1'.";
Blockly.Msg["TEXT_CHANGECASE_HELPURL"] = "https://github.com/google/blockly/wiki/Text#adjusting-text-case";  // untranslated
Blockly.Msg["TEXT_CHANGECASE_OPERATOR_LOWERCASE"] = "Kepada huruf kecil";
Blockly.Msg["TEXT_CHANGECASE_OPERATOR_TITLECASE"] = "to Title Case";
Blockly.Msg["TEXT_CHANGECASE_OPERATOR_UPPERCASE"] = "Kepada HURUF BESAR";
Blockly.Msg["TEXT_CHANGECASE_TOOLTIP"] = "Return a copy of the text in a different case.";
Blockly.Msg["TEXT_CHARAT_FIRST"] = "get first letter";
Blockly.Msg["TEXT_CHARAT_FROM_END"] = "get letter # from end";
Blockly.Msg["TEXT_CHARAT_FROM_START"] = "get letter #";
Blockly.Msg["TEXT_CHARAT_HELPURL"] = "https://github.com/google/blockly/wiki/Text#extracting-text";  // untranslated
Blockly.Msg["TEXT_CHARAT_LAST"] = "Dapatkan abjad terakhir";
Blockly.Msg["TEXT_CHARAT_RANDOM"] = "Dapatkan abjad rawak";
Blockly.Msg["TEXT_CHARAT_TAIL"] = "";  // untranslated
Blockly.Msg["TEXT_CHARAT_TITLE"] = "in text %1 %2";  // untranslated
Blockly.Msg["TEXT_CHARAT_TOOLTIP"] = "Returns the letter at the specified position.";
Blockly.Msg["TEXT_COUNT_HELPURL"] = "https://github.com/google/blockly/wiki/Text#counting-substrings";  // untranslated
Blockly.Msg["TEXT_COUNT_MESSAGE0"] = "count %1 in %2";  // untranslated
Blockly.Msg["TEXT_COUNT_TOOLTIP"] = "Count how many times some text occurs within some other text.";  // untranslated
Blockly.Msg["TEXT_CREATE_JOIN_ITEM_TOOLTIP"] = "Add an item to the text.";
Blockly.Msg["TEXT_CREATE_JOIN_TITLE_JOIN"] = "Sertai";
Blockly.Msg["TEXT_CREATE_JOIN_TOOLTIP"] = "Add, remove, or reorder sections to reconfigure this text block.";
Blockly.Msg["TEXT_GET_SUBSTRING_END_FROM_END"] = "to letter # from end";
Blockly.Msg["TEXT_GET_SUBSTRING_END_FROM_START"] = "to letter #";
Blockly.Msg["TEXT_GET_SUBSTRING_END_LAST"] = "untuk huruf terakhir";
Blockly.Msg["TEXT_GET_SUBSTRING_HELPURL"] = "https://github.com/google/blockly/wiki/Text#extracting-a-region-of-text";  // untranslated
Blockly.Msg["TEXT_GET_SUBSTRING_INPUT_IN_TEXT"] = "dalam teks";
Blockly.Msg["TEXT_GET_SUBSTRING_START_FIRST"] = "get substring from first letter";
Blockly.Msg["TEXT_GET_SUBSTRING_START_FROM_END"] = "get substring from letter # from end";
Blockly.Msg["TEXT_GET_SUBSTRING_START_FROM_START"] = "get substring from letter #";
Blockly.Msg["TEXT_GET_SUBSTRING_TAIL"] = "";  // untranslated
Blockly.Msg["TEXT_GET_SUBSTRING_TOOLTIP"] = "Returns a specified portion of the text.";
Blockly.Msg["TEXT_INDEXOF_HELPURL"] = "https://github.com/google/blockly/wiki/Text#finding-text";  // untranslated
Blockly.Msg["TEXT_INDEXOF_OPERATOR_FIRST"] = "mencari kejadian pertama teks";
Blockly.Msg["TEXT_INDEXOF_OPERATOR_LAST"] = "mencari kejadian terakhir teks";
Blockly.Msg["TEXT_INDEXOF_TITLE"] = "dalam teks %1 %2 %3";
Blockly.Msg["TEXT_INDEXOF_TOOLTIP"] = "Kembalikan Indeks kejadian pertama/terakhir dari teks pertama ke dalam teks kedua.  Kembalikan %1 Jika teks tidak ditemui.";
Blockly.Msg["TEXT_ISEMPTY_HELPURL"] = "https://github.com/google/blockly/wiki/Text#checking-for-empty-text";  // untranslated
Blockly.Msg["TEXT_ISEMPTY_TITLE"] = "%1 adalah kosong";
Blockly.Msg["TEXT_ISEMPTY_TOOLTIP"] = "Kembalikan benar jika teks yang disediakan adalah kosong.";
Blockly.Msg["TEXT_JOIN_HELPURL"] = "https://github.com/google/blockly/wiki/Text#text-creation";  // untranslated
Blockly.Msg["TEXT_JOIN_TITLE_CREATEWITH"] = "hasilkan teks dengan";
Blockly.Msg["TEXT_JOIN_TOOLTIP"] = "Hasilkan sebahagian teks dengan menghubungkan apa jua nombor item.";
Blockly.Msg["TEXT_LENGTH_HELPURL"] = "https://github.com/google/blockly/wiki/Text#text-modification";  // untranslated
Blockly.Msg["TEXT_LENGTH_TITLE"] = "panjang %1";
Blockly.Msg["TEXT_LENGTH_TOOLTIP"] = "Kembalikan jumlah huruf (termasuk ruang) dalam teks yang disediakan.";
Blockly.Msg["TEXT_PRINT_HELPURL"] = "https://github.com/google/blockly/wiki/Text#printing-text";  // untranslated
Blockly.Msg["TEXT_PRINT_TITLE"] = "cetak %1";
Blockly.Msg["TEXT_PRINT_TOOLTIP"] = "Cetak teks yang ditentukan, nombor atau nilai lain.";
Blockly.Msg["TEXT_PROMPT_HELPURL"] = "https://github.com/google/blockly/wiki/Text#getting-input-from-the-user";  // untranslated
Blockly.Msg["TEXT_PROMPT_TOOLTIP_NUMBER"] = "Peringatan kepada pengguna untuk nombor.";
Blockly.Msg["TEXT_PROMPT_TOOLTIP_TEXT"] = "Peringatkan pengguna untuk sebahagian teks.";
Blockly.Msg["TEXT_PROMPT_TYPE_NUMBER"] = "Prom untuk nombor dengan mesej";
Blockly.Msg["TEXT_PROMPT_TYPE_TEXT"] = "Prom untuk teks dengan mesej";
Blockly.Msg["TEXT_REPLACE_HELPURL"] = "https://github.com/google/blockly/wiki/Text#replacing-substrings";  // untranslated
Blockly.Msg["TEXT_REPLACE_MESSAGE0"] = "replace %1 with %2 in %3";  // untranslated
Blockly.Msg["TEXT_REPLACE_TOOLTIP"] = "Replace all occurances of some text within some other text.";  // untranslated
Blockly.Msg["TEXT_REVERSE_HELPURL"] = "https://github.com/google/blockly/wiki/Text#reversing-text";  // untranslated
Blockly.Msg["TEXT_REVERSE_MESSAGE0"] = "reverse %1";  // untranslated
Blockly.Msg["TEXT_REVERSE_TOOLTIP"] = "Reverses the order of the characters in the text.";  // untranslated
Blockly.Msg["TEXT_TEXT_HELPURL"] = "https://ms.wikipedia.org/wiki/Rentetan";
Blockly.Msg["TEXT_TEXT_TOOLTIP"] = "Huruf, perkataan, atau baris teks.";
Blockly.Msg["TEXT_TRIM_HELPURL"] = "https://github.com/google/blockly/wiki/Text#trimming-removing-spaces";  // untranslated
Blockly.Msg["TEXT_TRIM_OPERATOR_BOTH"] = "mengurangkan kawasan dari kedua-dua belah";
Blockly.Msg["TEXT_TRIM_OPERATOR_LEFT"] = "mengurangkan ruang dari sebelah kiri";
Blockly.Msg["TEXT_TRIM_OPERATOR_RIGHT"] = "mengurangkan kawasan dari sisi kanan";
Blockly.Msg["TEXT_TRIM_TOOLTIP"] = "Kembali salinan teks dengan ruang yang dikeluarkan daripada satu atau hujung kedua belah.";
Blockly.Msg["TODAY"] = "Hari ini";
Blockly.Msg["UNDO"] = "Undo";  // untranslated
Blockly.Msg["UNNAMED_KEY"] = "unnamed";  // untranslated
Blockly.Msg["VARIABLES_DEFAULT_NAME"] = "Perkara";
Blockly.Msg["VARIABLES_GET_CREATE_SET"] = "Hasilkan 'set %1'";
Blockly.Msg["VARIABLES_GET_HELPURL"] = "https://github.com/google/blockly/wiki/Variables#get";  // untranslated
Blockly.Msg["VARIABLES_GET_TOOLTIP"] = "Kembalikan nilai pemboleh ubah ini.";
Blockly.Msg["VARIABLES_SET"] = "set %1 ke %2";
Blockly.Msg["VARIABLES_SET_CREATE_GET"] = "Hasilkan 'set %1'";
Blockly.Msg["VARIABLES_SET_HELPURL"] = "https://github.com/google/blockly/wiki/Variables#set";  // untranslated
Blockly.Msg["VARIABLES_SET_TOOLTIP"] = "Set pembolehubah ini supaya sama dengan input.";
Blockly.Msg["VARIABLE_ALREADY_EXISTS"] = "A variable named '%1' already exists.";  // untranslated
Blockly.Msg["VARIABLE_ALREADY_EXISTS_FOR_ANOTHER_TYPE"] = "A variable named '%1' already exists for another type: '%2'.";  // untranslated
Blockly.Msg["WORKSPACE_ARIA_LABEL"] = "Blockly Workspace";  // untranslated
Blockly.Msg["WORKSPACE_COMMENT_DEFAULT_TEXT"] = "Say something...";  // untranslated
Blockly.Msg["CONTROLS_FOREACH_INPUT_DO"] = Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"];
Blockly.Msg["CONTROLS_FOR_INPUT_DO"] = Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"];
Blockly.Msg["CONTROLS_IF_ELSEIF_TITLE_ELSEIF"] = Blockly.Msg["CONTROLS_IF_MSG_ELSEIF"];
Blockly.Msg["CONTROLS_IF_ELSE_TITLE_ELSE"] = Blockly.Msg["CONTROLS_IF_MSG_ELSE"];
Blockly.Msg["CONTROLS_IF_IF_TITLE_IF"] = Blockly.Msg["CONTROLS_IF_MSG_IF"];
Blockly.Msg["CONTROLS_IF_MSG_THEN"] = Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"];
Blockly.Msg["CONTROLS_WHILEUNTIL_INPUT_DO"] = Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"];
Blockly.Msg["LISTS_CREATE_WITH_ITEM_TITLE"] = Blockly.Msg["VARIABLES_DEFAULT_NAME"];
Blockly.Msg["LISTS_GET_INDEX_HELPURL"] = Blockly.Msg["LISTS_INDEX_OF_HELPURL"];
Blockly.Msg["LISTS_GET_INDEX_INPUT_IN_LIST"] = Blockly.Msg["LISTS_INLIST"];
Blockly.Msg["LISTS_GET_SUBLIST_INPUT_IN_LIST"] = Blockly.Msg["LISTS_INLIST"];
Blockly.Msg["LISTS_INDEX_OF_INPUT_IN_LIST"] = Blockly.Msg["LISTS_INLIST"];
Blockly.Msg["LISTS_SET_INDEX_INPUT_IN_LIST"] = Blockly.Msg["LISTS_INLIST"];
Blockly.Msg["MATH_CHANGE_TITLE_ITEM"] = Blockly.Msg["VARIABLES_DEFAULT_NAME"];
Blockly.Msg["PROCEDURES_DEFRETURN_COMMENT"] = Blockly.Msg["PROCEDURES_DEFNORETURN_COMMENT"];
Blockly.Msg["PROCEDURES_DEFRETURN_DO"] = Blockly.Msg["PROCEDURES_DEFNORETURN_DO"];
Blockly.Msg["PROCEDURES_DEFRETURN_PROCEDURE"] = Blockly.Msg["PROCEDURES_DEFNORETURN_PROCEDURE"];
Blockly.Msg["PROCEDURES_DEFRETURN_TITLE"] = Blockly.Msg["PROCEDURES_DEFNORETURN_TITLE"];
Blockly.Msg["TEXT_APPEND_VARIABLE"] = Blockly.Msg["VARIABLES_DEFAULT_NAME"];
Blockly.Msg["TEXT_CREATE_JOIN_ITEM_TITLE_ITEM"] = Blockly.Msg["VARIABLES_DEFAULT_NAME"];

Blockly.Msg["MATH_HUE"] = "230";
Blockly.Msg["LOOPS_HUE"] = "120";
Blockly.Msg["LISTS_HUE"] = "260";
Blockly.Msg["LOGIC_HUE"] = "210";
Blockly.Msg["VARIABLES_HUE"] = "330";
Blockly.Msg["TEXTS_HUE"] = "160";
Blockly.Msg["PROCEDURES_HUE"] = "290";
Blockly.Msg["COLOUR_HUE"] = "20";
Blockly.Msg["VARIABLES_DYNAMIC_HUE"] = "310";