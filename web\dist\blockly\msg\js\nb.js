// This file was automatically generated.  Do not modify.

'use strict';

Blockly.Msg["ADD_COMMENT"] = "Legg til kommentar";
Blockly.Msg["CANNOT_DELETE_VARIABLE_PROCEDURE"] = "Kan ikke slette variabelen «%1» fordi den er del av definisjonen for funksjonen «%2»";
Blockly.Msg["CHANGE_VALUE_TITLE"] = "Bytt verdi:";
Blockly.Msg["CLEAN_UP"] = "Rydd opp Blocks";
Blockly.Msg["COLLAPSED_WARNINGS_WARNING"] = "Collapsed blocks contain warnings.";  // untranslated
Blockly.Msg["COLLAPSE_ALL"] = "Skjul blokker";
Blockly.Msg["COLLAPSE_BLOCK"] = "Skjul blokk";
Blockly.Msg["COLOUR_BLEND_COLOUR1"] = "farge 1";
Blockly.Msg["COLOUR_BLEND_COLOUR2"] = "farge 2";
Blockly.Msg["COLOUR_BLEND_HELPURL"] = "http://meyerweb.com/eric/tools/color-blend/";
Blockly.Msg["COLOUR_BLEND_RATIO"] = "forhold";
Blockly.Msg["COLOUR_BLEND_TITLE"] = "blande";
Blockly.Msg["COLOUR_BLEND_TOOLTIP"] = "Blander to farger sammen med et gitt forhold (0.0 - 1.0).";
Blockly.Msg["COLOUR_PICKER_HELPURL"] = "https://en.wikipedia.org/wiki/Color";
Blockly.Msg["COLOUR_PICKER_TOOLTIP"] = "Velg en farge fra paletten.";
Blockly.Msg["COLOUR_RANDOM_HELPURL"] = "http://randomcolour.com";  // untranslated
Blockly.Msg["COLOUR_RANDOM_TITLE"] = "tilfeldig farge";
Blockly.Msg["COLOUR_RANDOM_TOOLTIP"] = "Velg en tilfeldig farge.";
Blockly.Msg["COLOUR_RGB_BLUE"] = "blå";
Blockly.Msg["COLOUR_RGB_GREEN"] = "grønn";
Blockly.Msg["COLOUR_RGB_HELPURL"] = "http://www.december.com/html/spec/colorper.html";
Blockly.Msg["COLOUR_RGB_RED"] = "rød";
Blockly.Msg["COLOUR_RGB_TITLE"] = "farge med";
Blockly.Msg["COLOUR_RGB_TOOLTIP"] = "Lag en farge med angitt verdi av rød, grønn og blå. Alle verdier må være mellom 0 og 100.";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_HELPURL"] = "https://github.com/google/blockly/wiki/Loops#loop-termination-blocks";  // untranslated
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_OPERATOR_BREAK"] = "bryt ut av løkken";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_OPERATOR_CONTINUE"] = "fortsett med neste gjentakelse av løkken";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_TOOLTIP_BREAK"] = "Bryt ut av den gjeldende løkken.";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_TOOLTIP_CONTINUE"] = "Hopp over resten av denne løkken og fortsett med neste gjentakelse.";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_WARNING"] = "Advarsel: Denne blokken kan kun brukes innenfor en løkke.";
Blockly.Msg["CONTROLS_FOREACH_HELPURL"] = "https://github.com/google/blockly/wiki/Loops#for-each";  // untranslated
Blockly.Msg["CONTROLS_FOREACH_TITLE"] = "for hvert element %1 i listen %2";
Blockly.Msg["CONTROLS_FOREACH_TOOLTIP"] = "For hvert element i en liste, angi variabelen '%1' til elementet, og deretter lag noen setninger.";
Blockly.Msg["CONTROLS_FOR_HELPURL"] = "https://github.com/google/blockly/wiki/Loops#count-with";  // untranslated
Blockly.Msg["CONTROLS_FOR_TITLE"] = "tell med %1 fra %2 til %3 med %4";
Blockly.Msg["CONTROLS_FOR_TOOLTIP"] = "Ha variabel \"%1\" ta verdiene fra start nummer til slutt nummer, telle med spesifisert intervall og lag de spesifiserte blokkene.";
Blockly.Msg["CONTROLS_IF_ELSEIF_TOOLTIP"] = "Legg til en betingelse til hvis blokken.";
Blockly.Msg["CONTROLS_IF_ELSE_TOOLTIP"] = "Legg til hva som skal skje hvis de andre ikke slår til.";
Blockly.Msg["CONTROLS_IF_HELPURL"] = "https://github.com/google/blockly/wiki/IfElse";  // untranslated
Blockly.Msg["CONTROLS_IF_IF_TOOLTIP"] = "Legg til, fjern eller flytt seksjoner i denne hvis-blokken.";
Blockly.Msg["CONTROLS_IF_MSG_ELSE"] = "ellers";
Blockly.Msg["CONTROLS_IF_MSG_ELSEIF"] = "ellers hvis";
Blockly.Msg["CONTROLS_IF_MSG_IF"] = "hvis";
Blockly.Msg["CONTROLS_IF_TOOLTIP_1"] = "Hvis dette er sant, så gjør følgende.";
Blockly.Msg["CONTROLS_IF_TOOLTIP_2"] = "Hvis dette er sant, så utfør den første blokken av instruksjoner. Hvis ikke, utfør den andre blokken.";
Blockly.Msg["CONTROLS_IF_TOOLTIP_3"] = "Hvis det første stemmer, så utfør den første blokken av instruksjoner. Ellers, hvis det andre stemmer, utfør den andre blokken av instruksjoner.";
Blockly.Msg["CONTROLS_IF_TOOLTIP_4"] = "Hvis den første verdien er sann, så utfør den første blokken med setninger. Ellers, hvis den andre verdien er sann, så utfør den andre blokken med setninger. Hvis ingen av verdiene er sanne, så utfør den siste blokken med setninger.";
Blockly.Msg["CONTROLS_REPEAT_HELPURL"] = "https://en.wikipedia.org/wiki/For_loop";
Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"] = "gjør";
Blockly.Msg["CONTROLS_REPEAT_TITLE"] = "gjenta %1 ganger";
Blockly.Msg["CONTROLS_REPEAT_TOOLTIP"] = "Gjenta noen instruksjoner flere ganger.";
Blockly.Msg["CONTROLS_WHILEUNTIL_HELPURL"] = "https://github.com/google/blockly/wiki/Loops#repeat";  // untranslated
Blockly.Msg["CONTROLS_WHILEUNTIL_OPERATOR_UNTIL"] = "gjenta til";
Blockly.Msg["CONTROLS_WHILEUNTIL_OPERATOR_WHILE"] = "gjenta mens";
Blockly.Msg["CONTROLS_WHILEUNTIL_TOOLTIP_UNTIL"] = "Så lenge et utsagn ikke stemmer, gjør noen instruksjoner.";
Blockly.Msg["CONTROLS_WHILEUNTIL_TOOLTIP_WHILE"] = "Så lenge et utsagn stemmer, utfør noen instruksjoner.";
Blockly.Msg["DELETE_ALL_BLOCKS"] = "Slett alle %1 blokker?";
Blockly.Msg["DELETE_BLOCK"] = "Slett blokk";
Blockly.Msg["DELETE_VARIABLE"] = "Slett variabelen «%1»";
Blockly.Msg["DELETE_VARIABLE_CONFIRMATION"] = "Slett %1 bruk av variabelen «%2»?";
Blockly.Msg["DELETE_X_BLOCKS"] = "Slett %1 blokker";
Blockly.Msg["DIALOG_CANCEL"] = "Avbryt";
Blockly.Msg["DIALOG_OK"] = "OK";
Blockly.Msg["DISABLE_BLOCK"] = "Deaktiver blokk";
Blockly.Msg["DUPLICATE_BLOCK"] = "duplikat";
Blockly.Msg["DUPLICATE_COMMENT"] = "Dupliser kommentar";
Blockly.Msg["ENABLE_BLOCK"] = "Aktiver blokk";
Blockly.Msg["EXPAND_ALL"] = "Utvid blokker";
Blockly.Msg["EXPAND_BLOCK"] = "Utvid blokk";
Blockly.Msg["EXTERNAL_INPUTS"] = "Eksterne kilder";
Blockly.Msg["HELP"] = "Hjelp";
Blockly.Msg["INLINE_INPUTS"] = "Interne kilder";
Blockly.Msg["LISTS_CREATE_EMPTY_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#create-empty-list";
Blockly.Msg["LISTS_CREATE_EMPTY_TITLE"] = "opprett en tom liste";
Blockly.Msg["LISTS_CREATE_EMPTY_TOOLTIP"] = "Returnerer en tom liste, altså med lengde 0";
Blockly.Msg["LISTS_CREATE_WITH_CONTAINER_TITLE_ADD"] = "liste";
Blockly.Msg["LISTS_CREATE_WITH_CONTAINER_TOOLTIP"] = "Legg til, fjern eller endre rekkefølgen for å endre på denne delen av listen.";
Blockly.Msg["LISTS_CREATE_WITH_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#create-list-with";  // untranslated
Blockly.Msg["LISTS_CREATE_WITH_INPUT_WITH"] = "lag en liste med";
Blockly.Msg["LISTS_CREATE_WITH_ITEM_TOOLTIP"] = "Tilføy et element til listen.";
Blockly.Msg["LISTS_CREATE_WITH_TOOLTIP"] = "Lag en liste med et vilkårlig antall elementer.";
Blockly.Msg["LISTS_GET_INDEX_FIRST"] = "først";
Blockly.Msg["LISTS_GET_INDEX_FROM_END"] = "# fra slutten";
Blockly.Msg["LISTS_GET_INDEX_FROM_START"] = "#";
Blockly.Msg["LISTS_GET_INDEX_GET"] = "hent";
Blockly.Msg["LISTS_GET_INDEX_GET_REMOVE"] = "hent og fjern";
Blockly.Msg["LISTS_GET_INDEX_LAST"] = "siste";
Blockly.Msg["LISTS_GET_INDEX_RANDOM"] = "tilfeldig";
Blockly.Msg["LISTS_GET_INDEX_REMOVE"] = "fjern";
Blockly.Msg["LISTS_GET_INDEX_TAIL"] = "";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_FIRST"] = "Returnerer det første elementet i en liste.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_FROM"] = "Returner elementet på den angitte posisjonen i en liste.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_LAST"] = "Returnerer det siste elementet i en liste.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_RANDOM"] = "Returnerer et tilfeldig element i en liste.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_FIRST"] = "Fjerner og returnerer det første elementet i en liste.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_FROM"] = "Fjerner og returnerer elementet ved en gitt posisjon i en liste.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_LAST"] = "Fjerner og returnerer det siste elementet i en liste.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_RANDOM"] = "Fjerner og returnerer et tilfeldig element i en liste.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_REMOVE_FIRST"] = "Fjerner det første elementet i en liste.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_REMOVE_FROM"] = "Fjerner et element ved en gitt posisjon i en liste.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_REMOVE_LAST"] = "Fjerner det siste elementet i en liste.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_REMOVE_RANDOM"] = "Fjerner et tilfeldig element i en liste.";
Blockly.Msg["LISTS_GET_SUBLIST_END_FROM_END"] = "til # fra slutten";
Blockly.Msg["LISTS_GET_SUBLIST_END_FROM_START"] = "til #";
Blockly.Msg["LISTS_GET_SUBLIST_END_LAST"] = "til siste";
Blockly.Msg["LISTS_GET_SUBLIST_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#getting-a-sublist";  // untranslated
Blockly.Msg["LISTS_GET_SUBLIST_START_FIRST"] = "Hent en del av listen";
Blockly.Msg["LISTS_GET_SUBLIST_START_FROM_END"] = "Hent de siste # elementene";
Blockly.Msg["LISTS_GET_SUBLIST_START_FROM_START"] = "Hent del-listen fra #";
Blockly.Msg["LISTS_GET_SUBLIST_TAIL"] = "";
Blockly.Msg["LISTS_GET_SUBLIST_TOOLTIP"] = "Kopiérer en ønsket del av en liste.";
Blockly.Msg["LISTS_INDEX_FROM_END_TOOLTIP"] = "%1 er det siste elementet.";
Blockly.Msg["LISTS_INDEX_FROM_START_TOOLTIP"] = "%1 er det første elementet.";
Blockly.Msg["LISTS_INDEX_OF_FIRST"] = "finn første forekomst av elementet";
Blockly.Msg["LISTS_INDEX_OF_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#getting-items-from-a-list";  // untranslated
Blockly.Msg["LISTS_INDEX_OF_LAST"] = "finn siste forekomst av elementet";
Blockly.Msg["LISTS_INDEX_OF_TOOLTIP"] = "Returnerer indeksen av den første/siste forekomsten av elementet i lista. Returnerer %1 hvis ikke funnet.";
Blockly.Msg["LISTS_INLIST"] = "i listen";
Blockly.Msg["LISTS_ISEMPTY_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#is-empty";  // untranslated
Blockly.Msg["LISTS_ISEMPTY_TITLE"] = "%1 er tom";
Blockly.Msg["LISTS_ISEMPTY_TOOLTIP"] = "Returnerer sann hvis listen er tom.";
Blockly.Msg["LISTS_LENGTH_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#length-of";  // untranslated
Blockly.Msg["LISTS_LENGTH_TITLE"] = "lengden på %1";
Blockly.Msg["LISTS_LENGTH_TOOLTIP"] = "Returnerer lengden til en liste.";
Blockly.Msg["LISTS_REPEAT_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#create-list-with";  // untranslated
Blockly.Msg["LISTS_REPEAT_TITLE"] = "Lag en liste hvor elementet %1 forekommer %2 ganger";
Blockly.Msg["LISTS_REPEAT_TOOLTIP"] = "Lager en liste hvor den gitte verdien gjentas et antall ganger.";
Blockly.Msg["LISTS_REVERSE_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#reversing-a-list";
Blockly.Msg["LISTS_REVERSE_MESSAGE0"] = "reverser %1";
Blockly.Msg["LISTS_REVERSE_TOOLTIP"] = "Reverser en kopi av ei liste.";
Blockly.Msg["LISTS_SET_INDEX_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#in-list--set";  // untranslated
Blockly.Msg["LISTS_SET_INDEX_INPUT_TO"] = "som";
Blockly.Msg["LISTS_SET_INDEX_INSERT"] = "sett inn ved";
Blockly.Msg["LISTS_SET_INDEX_SET"] = "sett";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_INSERT_FIRST"] = "Setter inn elementet i starten av en liste.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_INSERT_FROM"] = "Setter inn elementet ved den angitte posisjonen i en liste.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_INSERT_LAST"] = "Tilføy elementet til slutten av en liste.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_INSERT_RANDOM"] = "Setter inn elementet ved en tilfeldig posisjon i en liste.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_SET_FIRST"] = "Angir det første elementet i en liste.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_SET_FROM"] = "Setter inn elementet ved den angitte posisjonen i en liste.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_SET_LAST"] = "Angir det siste elementet i en liste.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_SET_RANDOM"] = "Angir et tilfeldig element i en liste.";
Blockly.Msg["LISTS_SORT_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#sorting-a-list";
Blockly.Msg["LISTS_SORT_ORDER_ASCENDING"] = "stigende";
Blockly.Msg["LISTS_SORT_ORDER_DESCENDING"] = "synkende";
Blockly.Msg["LISTS_SORT_TITLE"] = "sorter %1 %2 %3";
Blockly.Msg["LISTS_SORT_TOOLTIP"] = "Sorter en kopi av en liste.";
Blockly.Msg["LISTS_SORT_TYPE_IGNORECASE"] = "alfabetisk, ignorert store/små bokstaver";
Blockly.Msg["LISTS_SORT_TYPE_NUMERIC"] = "numerisk";
Blockly.Msg["LISTS_SORT_TYPE_TEXT"] = "alfabetisk";
Blockly.Msg["LISTS_SPLIT_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#splitting-strings-and-joining-lists";  // untranslated
Blockly.Msg["LISTS_SPLIT_LIST_FROM_TEXT"] = "lag liste av tekst";
Blockly.Msg["LISTS_SPLIT_TEXT_FROM_LIST"] = "lag tekst av liste";
Blockly.Msg["LISTS_SPLIT_TOOLTIP_JOIN"] = "Føy sammen en liste tekster til én tekst, avskilt av en avgrenser.";
Blockly.Msg["LISTS_SPLIT_TOOLTIP_SPLIT"] = "Splitt teksten til en liste med tekster, brutt ved hver avgrenser.";
Blockly.Msg["LISTS_SPLIT_WITH_DELIMITER"] = "med avgrenser";
Blockly.Msg["LOGIC_BOOLEAN_FALSE"] = "usann";
Blockly.Msg["LOGIC_BOOLEAN_HELPURL"] = "https://github.com/google/blockly/wiki/Logic#values";  // untranslated
Blockly.Msg["LOGIC_BOOLEAN_TOOLTIP"] = "Returnerer enten sann eller usann.";
Blockly.Msg["LOGIC_BOOLEAN_TRUE"] = "sann";
Blockly.Msg["LOGIC_COMPARE_HELPURL"] = "https://en.wikipedia.org/wiki/Inequality_(mathematics)";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_EQ"] = "Returnerer sann hvis begge inputene er like hverandre.";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_GT"] = "Returnerer sant hvis det første argumentet er større enn den andre argumentet.";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_GTE"] = "Returnerer sant hvis det første argumentet er større enn eller likt det andre argumentet.";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_LT"] = "Returnerer sant hvis det første argumentet er mindre enn det andre argumentet.";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_LTE"] = "Returnerer sant hvis det første argumentet er mindre enn eller likt det andre argumentet.";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_NEQ"] = "Returnerer sant hvis begge argumentene er ulike hverandre.";
Blockly.Msg["LOGIC_NEGATE_HELPURL"] = "https://github.com/google/blockly/wiki/Logic#not";  // untranslated
Blockly.Msg["LOGIC_NEGATE_TITLE"] = "ikke %1";
Blockly.Msg["LOGIC_NEGATE_TOOLTIP"] = "Returnerer sant hvis argumentet er usant. Returnerer usant hvis argumentet er sant.";
Blockly.Msg["LOGIC_NULL"] = "null";
Blockly.Msg["LOGIC_NULL_HELPURL"] = "https://en.wikipedia.org/wiki/Nullable_type";
Blockly.Msg["LOGIC_NULL_TOOLTIP"] = "Returnerer null.";
Blockly.Msg["LOGIC_OPERATION_AND"] = "og";
Blockly.Msg["LOGIC_OPERATION_HELPURL"] = "https://github.com/google/blockly/wiki/Logic#logical-operations";  // untranslated
Blockly.Msg["LOGIC_OPERATION_OR"] = "eller";
Blockly.Msg["LOGIC_OPERATION_TOOLTIP_AND"] = "Returnerer sant hvis begge argumentene er sanne.";
Blockly.Msg["LOGIC_OPERATION_TOOLTIP_OR"] = "Returnerer sant hvis minst ett av argumentene er sant.";
Blockly.Msg["LOGIC_TERNARY_CONDITION"] = "test";
Blockly.Msg["LOGIC_TERNARY_HELPURL"] = "https://en.wikipedia.org/wiki/%3F:";
Blockly.Msg["LOGIC_TERNARY_IF_FALSE"] = "hvis usant";
Blockly.Msg["LOGIC_TERNARY_IF_TRUE"] = "hvis sant";
Blockly.Msg["LOGIC_TERNARY_TOOLTIP"] = "Sjekk betingelsen i 'test'. Hvis betingelsen er sann, da returneres 'hvis sant' verdien. Hvis ikke returneres 'hvis usant' verdien.";
Blockly.Msg["MATH_ADDITION_SYMBOL"] = "+";
Blockly.Msg["MATH_ARITHMETIC_HELPURL"] = "https://no.wikipedia.org/wiki/Aritmetikk";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_ADD"] = "Returnerer summen av to tall.";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_DIVIDE"] = "Returner kvotienten av to tall.";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_MINUS"] = "Returner differansen mellom to tall.";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_MULTIPLY"] = "Returner produktet av to tall.";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_POWER"] = "Returner det første tallet opphøyd i den andre tallet.";
Blockly.Msg["MATH_ATAN2_HELPURL"] = "https://en.wikipedia.org/wiki/Atan2";
Blockly.Msg["MATH_ATAN2_TITLE"] = "atan2 av X:%1 Y:%2";
Blockly.Msg["MATH_ATAN2_TOOLTIP"] = "Return the arctangent of point (X, Y) in degrees from -180 to 180.";  // untranslated
Blockly.Msg["MATH_CHANGE_HELPURL"] = "https://en.wikipedia.org/wiki/Programming_idiom#Incrementing_a_counter";
Blockly.Msg["MATH_CHANGE_TITLE"] = "endre %1 ved %2";
Blockly.Msg["MATH_CHANGE_TOOLTIP"] = "Addere et tall til variabelen '%1'.";
Blockly.Msg["MATH_CONSTANT_HELPURL"] = "https://en.wikipedia.org/wiki/Mathematical_constant";
Blockly.Msg["MATH_CONSTANT_TOOLTIP"] = "Returner en av felleskonstantene π (3.141…), e (2.718…), φ (1.618…), sqrt(2) (1.414…), sqrt(½) (0.707…), eller ∞ (uendelig).";
Blockly.Msg["MATH_CONSTRAIN_HELPURL"] = "https://en.wikipedia.org/wiki/Clamping_(graphics)";  // untranslated
Blockly.Msg["MATH_CONSTRAIN_TITLE"] = "begrense %1 lav %2 høy %3";
Blockly.Msg["MATH_CONSTRAIN_TOOLTIP"] = "Begrens et tall til å være mellom de angitte grenseverdiene (inklusiv).";
Blockly.Msg["MATH_DIVISION_SYMBOL"] = "÷";
Blockly.Msg["MATH_IS_DIVISIBLE_BY"] = "er delelig med";
Blockly.Msg["MATH_IS_EVEN"] = "er et partall";
Blockly.Msg["MATH_IS_NEGATIVE"] = "er negativer negativt";
Blockly.Msg["MATH_IS_ODD"] = "er et oddetall";
Blockly.Msg["MATH_IS_POSITIVE"] = "er positivt";
Blockly.Msg["MATH_IS_PRIME"] = "er et primtall";
Blockly.Msg["MATH_IS_TOOLTIP"] = "Sjekk om et tall er et partall, oddetall, primtall, heltall, positivt, negativt, eller om det er delelig med et annet tall. Returnerer sant eller usant.";
Blockly.Msg["MATH_IS_WHOLE"] = "er et heltall";
Blockly.Msg["MATH_MODULO_HELPURL"] = "https://en.wikipedia.org/wiki/Modulo_operation";
Blockly.Msg["MATH_MODULO_TITLE"] = "resten av %1 ÷ %2";
Blockly.Msg["MATH_MODULO_TOOLTIP"] = "Returner resten fra delingen av to tall.";
Blockly.Msg["MATH_MULTIPLICATION_SYMBOL"] = "x";
Blockly.Msg["MATH_NUMBER_HELPURL"] = "https://en.wikipedia.org/wiki/Number";
Blockly.Msg["MATH_NUMBER_TOOLTIP"] = "Et tall.";
Blockly.Msg["MATH_ONLIST_HELPURL"] = "";  // untranslated
Blockly.Msg["MATH_ONLIST_OPERATOR_AVERAGE"] = "gjennomsnittet av listen";
Blockly.Msg["MATH_ONLIST_OPERATOR_MAX"] = "maksimum av liste";
Blockly.Msg["MATH_ONLIST_OPERATOR_MEDIAN"] = "medianen til listen";
Blockly.Msg["MATH_ONLIST_OPERATOR_MIN"] = "minimum av listen";
Blockly.Msg["MATH_ONLIST_OPERATOR_MODE"] = "Listens typetall";
Blockly.Msg["MATH_ONLIST_OPERATOR_RANDOM"] = "tilfeldig element i listen";
Blockly.Msg["MATH_ONLIST_OPERATOR_STD_DEV"] = "standardavviket til listen";
Blockly.Msg["MATH_ONLIST_OPERATOR_SUM"] = "summen av listen";
Blockly.Msg["MATH_ONLIST_TOOLTIP_AVERAGE"] = "Returner det aritmetiske gjennomsnittet av tallene i listen.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_MAX"] = "Returner det største tallet i listen.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_MEDIAN"] = "Returner listens median.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_MIN"] = "Returner det minste tallet i listen.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_MODE"] = "Returner en liste av de vanligste elementene i listen.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_RANDOM"] = "Returner et tilfeldig element fra listen.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_STD_DEV"] = "Returner listens standardavvik.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_SUM"] = "Returner summen av alle tallene i listen.";
Blockly.Msg["MATH_POWER_SYMBOL"] = "^";
Blockly.Msg["MATH_RANDOM_FLOAT_HELPURL"] = "https://en.wikipedia.org/wiki/Random_number_generation";
Blockly.Msg["MATH_RANDOM_FLOAT_TITLE_RANDOM"] = "tilfeldig flyttall";
Blockly.Msg["MATH_RANDOM_FLOAT_TOOLTIP"] = "Returner et tilfeldig flyttall mellom 0.0 (inkludert) og 1.0 (ikke inkludert).";
Blockly.Msg["MATH_RANDOM_INT_HELPURL"] = "https://en.wikipedia.org/wiki/Random_number_generation";
Blockly.Msg["MATH_RANDOM_INT_TITLE"] = "Et tilfeldig heltall mellom %1 og %2";
Blockly.Msg["MATH_RANDOM_INT_TOOLTIP"] = "Returner et tilfeldig tall mellom de to spesifiserte grensene, inkludert de to.";
Blockly.Msg["MATH_ROUND_HELPURL"] = "https://en.wikipedia.org/wiki/Rounding";
Blockly.Msg["MATH_ROUND_OPERATOR_ROUND"] = "avrunding";
Blockly.Msg["MATH_ROUND_OPERATOR_ROUNDDOWN"] = "rund ned";
Blockly.Msg["MATH_ROUND_OPERATOR_ROUNDUP"] = "rund opp";
Blockly.Msg["MATH_ROUND_TOOLTIP"] = "Avrund et tall ned eller opp.";
Blockly.Msg["MATH_SINGLE_HELPURL"] = "https://en.wikipedia.org/wiki/Square_root";
Blockly.Msg["MATH_SINGLE_OP_ABSOLUTE"] = "absoluttverdi";
Blockly.Msg["MATH_SINGLE_OP_ROOT"] = "kvadratrot";
Blockly.Msg["MATH_SINGLE_TOOLTIP_ABS"] = "Returner absoluttverdien av et tall.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_EXP"] = "Returner e opphøyd i et tall.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_LN"] = "Returner den naturlige logaritmen til et tall.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_LOG10"] = "Returner base-10 logaritmen til et tall.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_NEG"] = "Returner det negative tallet.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_POW10"] = "Returner 10 opphøyd i et tall.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_ROOT"] = "Returner kvadratroten av et tall.";
Blockly.Msg["MATH_SUBTRACTION_SYMBOL"] = "-";
Blockly.Msg["MATH_TRIG_ACOS"] = "acos";
Blockly.Msg["MATH_TRIG_ASIN"] = "asin";
Blockly.Msg["MATH_TRIG_ATAN"] = "atan";
Blockly.Msg["MATH_TRIG_COS"] = "cos";
Blockly.Msg["MATH_TRIG_HELPURL"] = "https://en.wikipedia.org/wiki/Trigonometric_functions";
Blockly.Msg["MATH_TRIG_SIN"] = "sin";
Blockly.Msg["MATH_TRIG_TAN"] = "tan";
Blockly.Msg["MATH_TRIG_TOOLTIP_ACOS"] = "Returner arccosinus til et tall.";
Blockly.Msg["MATH_TRIG_TOOLTIP_ASIN"] = "Returner arcsinus til et tall.";
Blockly.Msg["MATH_TRIG_TOOLTIP_ATAN"] = "Returner arctangens til et tall.";
Blockly.Msg["MATH_TRIG_TOOLTIP_COS"] = "Returner cosinus av en vinkel (ikke radian).";
Blockly.Msg["MATH_TRIG_TOOLTIP_SIN"] = "Returner sinus av en vinkel (ikke radian).";
Blockly.Msg["MATH_TRIG_TOOLTIP_TAN"] = "Returner tangenten av en vinkel (ikke radian).";
Blockly.Msg["NEW_COLOUR_VARIABLE"] = "Oppretter fargevariabel …";
Blockly.Msg["NEW_NUMBER_VARIABLE"] = "Oppretter tallvariabel …";
Blockly.Msg["NEW_STRING_VARIABLE"] = "Oppretter strengvariabel …";
Blockly.Msg["NEW_VARIABLE"] = "Opprett variabel…";
Blockly.Msg["NEW_VARIABLE_TITLE"] = "Nytt variabelnavn:";
Blockly.Msg["NEW_VARIABLE_TYPE_TITLE"] = "Ny variabeltype:";
Blockly.Msg["ORDINAL_NUMBER_SUFFIX"] = "";  // untranslated
Blockly.Msg["PROCEDURES_ALLOW_STATEMENTS"] = "tillat uttalelser";
Blockly.Msg["PROCEDURES_BEFORE_PARAMS"] = "med:";
Blockly.Msg["PROCEDURES_CALLNORETURN_HELPURL"] = "https://en.wikipedia.org/wiki/Subroutine";
Blockly.Msg["PROCEDURES_CALLNORETURN_TOOLTIP"] = "Kjør den brukerdefinerte funksjonen '%1'.";
Blockly.Msg["PROCEDURES_CALLRETURN_HELPURL"] = "https://en.wikipedia.org/wiki/Subroutine";
Blockly.Msg["PROCEDURES_CALLRETURN_TOOLTIP"] = "Kjør den brukerdefinerte funksjonen'%1' og bruk resultatet av den.";
Blockly.Msg["PROCEDURES_CALL_BEFORE_PARAMS"] = "med:";
Blockly.Msg["PROCEDURES_CREATE_DO"] = "Opprett '%1'";
Blockly.Msg["PROCEDURES_DEFNORETURN_COMMENT"] = "Beskriv denne funksjonen…";
Blockly.Msg["PROCEDURES_DEFNORETURN_DO"] = "";
Blockly.Msg["PROCEDURES_DEFNORETURN_HELPURL"] = "https://en.wikipedia.org/wiki/Procedure_%28computer_science%29";
Blockly.Msg["PROCEDURES_DEFNORETURN_PROCEDURE"] = "gjør noe";
Blockly.Msg["PROCEDURES_DEFNORETURN_TITLE"] = "til";
Blockly.Msg["PROCEDURES_DEFNORETURN_TOOLTIP"] = "Opprett en funksjon som ikke har noe resultat.";
Blockly.Msg["PROCEDURES_DEFRETURN_HELPURL"] = "https://en.wikipedia.org/wiki/Procedure_%28computer_science%29";
Blockly.Msg["PROCEDURES_DEFRETURN_RETURN"] = "returner";
Blockly.Msg["PROCEDURES_DEFRETURN_TOOLTIP"] = "Oppretter en funksjon som har et resultat.";
Blockly.Msg["PROCEDURES_DEF_DUPLICATE_WARNING"] = "Advarsel: Denne funksjonen har duplikate parametere.";
Blockly.Msg["PROCEDURES_HIGHLIGHT_DEF"] = "Marker funksjonsdefinisjonen";
Blockly.Msg["PROCEDURES_IFRETURN_HELPURL"] = "http://c2.com/cgi/wiki?GuardClause";  // untranslated
Blockly.Msg["PROCEDURES_IFRETURN_TOOLTIP"] = "Hvis en verdi er sann, returner da en annen verdi.";
Blockly.Msg["PROCEDURES_IFRETURN_WARNING"] = "Advarsel: Denne blokken kan bare benyttes innenfor en funksjonsdefinisjon.";
Blockly.Msg["PROCEDURES_MUTATORARG_TITLE"] = "Navn på parameter:";
Blockly.Msg["PROCEDURES_MUTATORARG_TOOLTIP"] = "Legg til en input til funksjonen.";
Blockly.Msg["PROCEDURES_MUTATORCONTAINER_TITLE"] = "parametere";
Blockly.Msg["PROCEDURES_MUTATORCONTAINER_TOOLTIP"] = "Legg til, fjern eller endre rekkefølgen på input til denne funksjonen.";
Blockly.Msg["REDO"] = "Gjør om";
Blockly.Msg["REMOVE_COMMENT"] = "Fjern kommentar";
Blockly.Msg["RENAME_VARIABLE"] = "Gi nytt navn til variabel…";
Blockly.Msg["RENAME_VARIABLE_TITLE"] = "Endre navnet til alle '%1' variabler til:";
Blockly.Msg["TEXT_APPEND_HELPURL"] = "https://github.com/google/blockly/wiki/Text#text-modification";  // untranslated
Blockly.Msg["TEXT_APPEND_TITLE"] = "til %1, legg til teksten %2";
Blockly.Msg["TEXT_APPEND_TOOLTIP"] = "Tilføy tekst til variabelen '%1'.";
Blockly.Msg["TEXT_CHANGECASE_HELPURL"] = "https://github.com/google/blockly/wiki/Text#adjusting-text-case";  // untranslated
Blockly.Msg["TEXT_CHANGECASE_OPERATOR_LOWERCASE"] = "til små bokstaver";
Blockly.Msg["TEXT_CHANGECASE_OPERATOR_TITLECASE"] = "til store forbokstaver";
Blockly.Msg["TEXT_CHANGECASE_OPERATOR_UPPERCASE"] = "til STORE BOKSTAVER";
Blockly.Msg["TEXT_CHANGECASE_TOOLTIP"] = "Returnerer en kopi av teksten der store og små bokstaver er byttet om.";
Blockly.Msg["TEXT_CHARAT_FIRST"] = "hent første bokstav";
Blockly.Msg["TEXT_CHARAT_FROM_END"] = "hent bokstav # fra slutten";
Blockly.Msg["TEXT_CHARAT_FROM_START"] = "hent bokstav #";
Blockly.Msg["TEXT_CHARAT_HELPURL"] = "https://github.com/google/blockly/wiki/Text#extracting-text";  // untranslated
Blockly.Msg["TEXT_CHARAT_LAST"] = "hent den siste bokstaven";
Blockly.Msg["TEXT_CHARAT_RANDOM"] = "hent en tilfeldig bokstav";
Blockly.Msg["TEXT_CHARAT_TAIL"] = "";
Blockly.Msg["TEXT_CHARAT_TITLE"] = "i teksten %1, %2";
Blockly.Msg["TEXT_CHARAT_TOOLTIP"] = "Returnerer bokstaven på angitt plassering.";
Blockly.Msg["TEXT_COUNT_HELPURL"] = "https://github.com/google/blockly/wiki/Text#counting-substrings";
Blockly.Msg["TEXT_COUNT_MESSAGE0"] = "tell %1 i %2";
Blockly.Msg["TEXT_COUNT_TOOLTIP"] = "Tell hvor mange ganger noe tekst dukker opp i annen tekst.";
Blockly.Msg["TEXT_CREATE_JOIN_ITEM_TOOLTIP"] = "Legg til et element til teksten.";
Blockly.Msg["TEXT_CREATE_JOIN_TITLE_JOIN"] = "føy sammen";
Blockly.Msg["TEXT_CREATE_JOIN_TOOLTIP"] = "Legg til, fjern eller forandre rekkefølgen for å forandre på denne tekstblokken.";
Blockly.Msg["TEXT_GET_SUBSTRING_END_FROM_END"] = "til bokstav # fra slutten";
Blockly.Msg["TEXT_GET_SUBSTRING_END_FROM_START"] = "til bokstav #";
Blockly.Msg["TEXT_GET_SUBSTRING_END_LAST"] = "til siste bokstav";
Blockly.Msg["TEXT_GET_SUBSTRING_HELPURL"] = "https://github.com/google/blockly/wiki/Text#extracting-a-region-of-text";  // untranslated
Blockly.Msg["TEXT_GET_SUBSTRING_INPUT_IN_TEXT"] = "i tekst";
Blockly.Msg["TEXT_GET_SUBSTRING_START_FIRST"] = "hent delstreng fra første bokstav";
Blockly.Msg["TEXT_GET_SUBSTRING_START_FROM_END"] = "hent delstreng fra bokstav # fra slutten";
Blockly.Msg["TEXT_GET_SUBSTRING_START_FROM_START"] = "hent delstreng fra bokstav #";
Blockly.Msg["TEXT_GET_SUBSTRING_TAIL"] = "";
Blockly.Msg["TEXT_GET_SUBSTRING_TOOLTIP"] = "Returnerer den angitte delen av teksten.";
Blockly.Msg["TEXT_INDEXOF_HELPURL"] = "https://github.com/google/blockly/wiki/Text#finding-text";  // untranslated
Blockly.Msg["TEXT_INDEXOF_OPERATOR_FIRST"] = "finn første forekomst av tekst";
Blockly.Msg["TEXT_INDEXOF_OPERATOR_LAST"] = "finn siste forekomst av tekst";
Blockly.Msg["TEXT_INDEXOF_TITLE"] = "i teksten %1, %2 %3";
Blockly.Msg["TEXT_INDEXOF_TOOLTIP"] = "Returnerer posisjonen for første/siste forekomsten av den første tekst i den andre teksten.  Returnerer %1 hvis teksten ikke blir funnet.";
Blockly.Msg["TEXT_ISEMPTY_HELPURL"] = "https://github.com/google/blockly/wiki/Text#checking-for-empty-text";  // untranslated
Blockly.Msg["TEXT_ISEMPTY_TITLE"] = "%1 er tom";
Blockly.Msg["TEXT_ISEMPTY_TOOLTIP"] = "Returnerer sann hvis den angitte teksten er tom.";
Blockly.Msg["TEXT_JOIN_HELPURL"] = "https://github.com/google/blockly/wiki/Text#text-creation";  // untranslated
Blockly.Msg["TEXT_JOIN_TITLE_CREATEWITH"] = "lag tekst med";
Blockly.Msg["TEXT_JOIN_TOOLTIP"] = "Opprett en tekst ved å sette sammen et antall elementer.";
Blockly.Msg["TEXT_LENGTH_HELPURL"] = "https://github.com/google/blockly/wiki/Text#text-modification";  // untranslated
Blockly.Msg["TEXT_LENGTH_TITLE"] = "lengden av %1";
Blockly.Msg["TEXT_LENGTH_TOOLTIP"] = "Returnerer antall bokstaver (inkludert mellomrom) i den angitte teksten.";
Blockly.Msg["TEXT_PRINT_HELPURL"] = "https://github.com/google/blockly/wiki/Text#printing-text";  // untranslated
Blockly.Msg["TEXT_PRINT_TITLE"] = "skriv ut %1";
Blockly.Msg["TEXT_PRINT_TOOLTIP"] = "Skriv ut angitt tekst, tall eller annet innhold.";
Blockly.Msg["TEXT_PROMPT_HELPURL"] = "https://github.com/google/blockly/wiki/Text#getting-input-from-the-user";  // untranslated
Blockly.Msg["TEXT_PROMPT_TOOLTIP_NUMBER"] = "Be brukeren om et tall.";
Blockly.Msg["TEXT_PROMPT_TOOLTIP_TEXT"] = "Spør brukeren om tekst.";
Blockly.Msg["TEXT_PROMPT_TYPE_NUMBER"] = "spør om et tall med en melding";
Blockly.Msg["TEXT_PROMPT_TYPE_TEXT"] = "spør om tekst med en melding";
Blockly.Msg["TEXT_REPLACE_HELPURL"] = "https://github.com/google/blockly/wiki/Text#replacing-substrings";
Blockly.Msg["TEXT_REPLACE_MESSAGE0"] = "erstatt %1 med %2 i %3";
Blockly.Msg["TEXT_REPLACE_TOOLTIP"] = "Erstatter alle forekomster av noe tekst i en annen tekst.";
Blockly.Msg["TEXT_REVERSE_HELPURL"] = "https://github.com/google/blockly/wiki/Text#reversing-text";
Blockly.Msg["TEXT_REVERSE_MESSAGE0"] = "reverser %1";
Blockly.Msg["TEXT_REVERSE_TOOLTIP"] = "Reverserer rekkefølgen på tegnene i teksten.";
Blockly.Msg["TEXT_TEXT_HELPURL"] = "https://en.wikipedia.org/wiki/String_(computer_science)";
Blockly.Msg["TEXT_TEXT_TOOLTIP"] = "En bokstav, ett ord eller en linje med tekst.";
Blockly.Msg["TEXT_TRIM_HELPURL"] = "https://github.com/google/blockly/wiki/Text#trimming-removing-spaces";  // untranslated
Blockly.Msg["TEXT_TRIM_OPERATOR_BOTH"] = "fjern mellomrom fra begge sider av";
Blockly.Msg["TEXT_TRIM_OPERATOR_LEFT"] = "fjern mellomrom fra venstre side av";
Blockly.Msg["TEXT_TRIM_OPERATOR_RIGHT"] = "fjern mellomrom fra høyre side av";
Blockly.Msg["TEXT_TRIM_TOOLTIP"] = "Returner en kopi av teksten med mellomrom fjernet fra en eller begge sidene.";
Blockly.Msg["TODAY"] = "I dag";
Blockly.Msg["UNDO"] = "Angre";
Blockly.Msg["UNNAMED_KEY"] = "unnamed";  // untranslated
Blockly.Msg["VARIABLES_DEFAULT_NAME"] = "element";
Blockly.Msg["VARIABLES_GET_CREATE_SET"] = "Opprett 'sett %1'";
Blockly.Msg["VARIABLES_GET_HELPURL"] = "https://github.com/google/blockly/wiki/Variables#get";  // untranslated
Blockly.Msg["VARIABLES_GET_TOOLTIP"] = "Returnerer verdien av denne variabelen.";
Blockly.Msg["VARIABLES_SET"] = "sett %1 til %2";
Blockly.Msg["VARIABLES_SET_CREATE_GET"] = "Opprett 'hent %1'";
Blockly.Msg["VARIABLES_SET_HELPURL"] = "https://github.com/google/blockly/wiki/Variables#set";  // untranslated
Blockly.Msg["VARIABLES_SET_TOOLTIP"] = "Setter verdien av denne variablen lik parameteren.";
Blockly.Msg["VARIABLE_ALREADY_EXISTS"] = "En variabel med navn «%1» finnes allerede.";
Blockly.Msg["VARIABLE_ALREADY_EXISTS_FOR_ANOTHER_TYPE"] = "En variabel med navn «%1» finnes allerede for en annen type: «%2».";
Blockly.Msg["WORKSPACE_ARIA_LABEL"] = "Blockly Workspace";  // untranslated
Blockly.Msg["WORKSPACE_COMMENT_DEFAULT_TEXT"] = "Si noe …";
Blockly.Msg["CONTROLS_FOREACH_INPUT_DO"] = Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"];
Blockly.Msg["CONTROLS_FOR_INPUT_DO"] = Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"];
Blockly.Msg["CONTROLS_IF_ELSEIF_TITLE_ELSEIF"] = Blockly.Msg["CONTROLS_IF_MSG_ELSEIF"];
Blockly.Msg["CONTROLS_IF_ELSE_TITLE_ELSE"] = Blockly.Msg["CONTROLS_IF_MSG_ELSE"];
Blockly.Msg["CONTROLS_IF_IF_TITLE_IF"] = Blockly.Msg["CONTROLS_IF_MSG_IF"];
Blockly.Msg["CONTROLS_IF_MSG_THEN"] = Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"];
Blockly.Msg["CONTROLS_WHILEUNTIL_INPUT_DO"] = Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"];
Blockly.Msg["LISTS_CREATE_WITH_ITEM_TITLE"] = Blockly.Msg["VARIABLES_DEFAULT_NAME"];
Blockly.Msg["LISTS_GET_INDEX_HELPURL"] = Blockly.Msg["LISTS_INDEX_OF_HELPURL"];
Blockly.Msg["LISTS_GET_INDEX_INPUT_IN_LIST"] = Blockly.Msg["LISTS_INLIST"];
Blockly.Msg["LISTS_GET_SUBLIST_INPUT_IN_LIST"] = Blockly.Msg["LISTS_INLIST"];
Blockly.Msg["LISTS_INDEX_OF_INPUT_IN_LIST"] = Blockly.Msg["LISTS_INLIST"];
Blockly.Msg["LISTS_SET_INDEX_INPUT_IN_LIST"] = Blockly.Msg["LISTS_INLIST"];
Blockly.Msg["MATH_CHANGE_TITLE_ITEM"] = Blockly.Msg["VARIABLES_DEFAULT_NAME"];
Blockly.Msg["PROCEDURES_DEFRETURN_COMMENT"] = Blockly.Msg["PROCEDURES_DEFNORETURN_COMMENT"];
Blockly.Msg["PROCEDURES_DEFRETURN_DO"] = Blockly.Msg["PROCEDURES_DEFNORETURN_DO"];
Blockly.Msg["PROCEDURES_DEFRETURN_PROCEDURE"] = Blockly.Msg["PROCEDURES_DEFNORETURN_PROCEDURE"];
Blockly.Msg["PROCEDURES_DEFRETURN_TITLE"] = Blockly.Msg["PROCEDURES_DEFNORETURN_TITLE"];
Blockly.Msg["TEXT_APPEND_VARIABLE"] = Blockly.Msg["VARIABLES_DEFAULT_NAME"];
Blockly.Msg["TEXT_CREATE_JOIN_ITEM_TITLE_ITEM"] = Blockly.Msg["VARIABLES_DEFAULT_NAME"];

Blockly.Msg["MATH_HUE"] = "230";
Blockly.Msg["LOOPS_HUE"] = "120";
Blockly.Msg["LISTS_HUE"] = "260";
Blockly.Msg["LOGIC_HUE"] = "210";
Blockly.Msg["VARIABLES_HUE"] = "330";
Blockly.Msg["TEXTS_HUE"] = "160";
Blockly.Msg["PROCEDURES_HUE"] = "290";
Blockly.Msg["COLOUR_HUE"] = "20";
Blockly.Msg["VARIABLES_DYNAMIC_HUE"] = "310";