// This file was automatically generated.  Do not modify.

'use strict';

Blockly.Msg["ADD_COMMENT"] = "Opmerking toevoegen";
Blockly.Msg["CANNOT_DELETE_VARIABLE_PROCEDURE"] = "De variabele \"%1\" kan niet verwijderd worden omdat die onderdeel uitmaakt van de definitie van de functie \"%2\"";
Blockly.Msg["CHANGE_VALUE_TITLE"] = "Waarde wijzigen:";
Blockly.Msg["CLEAN_UP"] = "Blokken opschonen";
Blockly.Msg["COLLAPSED_WARNINGS_WARNING"] = "Samengevouwen blokken bevatten waarschuwingen.";
Blockly.Msg["COLLAPSE_ALL"] = "Blokken samenvouwen";
Blockly.Msg["COLLAPSE_BLOCK"] = "Blok samenvouwen";
Blockly.Msg["COLOUR_BLEND_COLOUR1"] = "kleur 1";
Blockly.Msg["COLOUR_BLEND_COLOUR2"] = "kleur 2";
Blockly.Msg["COLOUR_BLEND_HELPURL"] = "https://meyerweb.com/eric/tools/color-blend/#:::rgbp";
Blockly.Msg["COLOUR_BLEND_RATIO"] = "verhouding";
Blockly.Msg["COLOUR_BLEND_TITLE"] = "mengen";
Blockly.Msg["COLOUR_BLEND_TOOLTIP"] = "Mengt twee kleuren samen met een bepaalde verhouding (0.0 - 1.0).";
Blockly.Msg["COLOUR_PICKER_HELPURL"] = "https://nl.wikipedia.org/wiki/Kleur";
Blockly.Msg["COLOUR_PICKER_TOOLTIP"] = "Kies een kleur in het palet.";
Blockly.Msg["COLOUR_RANDOM_HELPURL"] = "http://randomcolour.com";  // untranslated
Blockly.Msg["COLOUR_RANDOM_TITLE"] = "willekeurige kleur";
Blockly.Msg["COLOUR_RANDOM_TOOLTIP"] = "Kies een willekeurige kleur.";
Blockly.Msg["COLOUR_RGB_BLUE"] = "blauw";
Blockly.Msg["COLOUR_RGB_GREEN"] = "groen";
Blockly.Msg["COLOUR_RGB_HELPURL"] = "https://www.december.com/html/spec/colorpercompact.html";
Blockly.Msg["COLOUR_RGB_RED"] = "rood";
Blockly.Msg["COLOUR_RGB_TITLE"] = "kleuren met";
Blockly.Msg["COLOUR_RGB_TOOLTIP"] = "Maak een kleur met de opgegeven hoeveelheid rood, groen en blauw.  Alle waarden moeten tussen 0 en 100 liggen.";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_HELPURL"] = "https://github.com/google/blockly/wiki/Loops#loop-termination-blocks";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_OPERATOR_BREAK"] = "uit lus breken";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_OPERATOR_CONTINUE"] = "doorgaan met de volgende iteratie van de lus";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_TOOLTIP_BREAK"] = "Uit de bovenliggende lus breken.";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_TOOLTIP_CONTINUE"] = "De rest van deze lus overslaan en doorgaan met de volgende herhaling.";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_WARNING"] = "Waarschuwing: dit blok mag alleen gebruikt worden in een lus.";
Blockly.Msg["CONTROLS_FOREACH_HELPURL"] = "https://github.com/google/blockly/wiki/Loops#for-each";  // untranslated
Blockly.Msg["CONTROLS_FOREACH_TITLE"] = "voor ieder item %1 in lijst %2";
Blockly.Msg["CONTROLS_FOREACH_TOOLTIP"] = "Voor ieder item in een lijst, stel de variabele \"%1\" in op het item en voer daarna opdrachten uit.";
Blockly.Msg["CONTROLS_FOR_HELPURL"] = "https://github.com/google/blockly/wiki/Loops#count-with";
Blockly.Msg["CONTROLS_FOR_TITLE"] = "rekenen met %1 van %2 tot %3 in stappen van %4";
Blockly.Msg["CONTROLS_FOR_TOOLTIP"] = "Laat de variabele \"%1\" de waarden aannemen van het beginnummer tot het laatste nummer, tellende met het opgegeven interval, en met uitvoering van de opgegeven blokken.";
Blockly.Msg["CONTROLS_IF_ELSEIF_TOOLTIP"] = "Voeg een voorwaarde toe aan het als-blok.";
Blockly.Msg["CONTROLS_IF_ELSE_TOOLTIP"] = "Voeg een laatste, vang-alles conditie toe aan het als-statement.";
Blockly.Msg["CONTROLS_IF_HELPURL"] = "https://github.com/google/blockly/wiki/IfElse";
Blockly.Msg["CONTROLS_IF_IF_TOOLTIP"] = "Voeg stukken toe, verwijder of wijzig de volgorde om dit \"als\"-blok te wijzigen.";
Blockly.Msg["CONTROLS_IF_MSG_ELSE"] = "anders";
Blockly.Msg["CONTROLS_IF_MSG_ELSEIF"] = "anders als";
Blockly.Msg["CONTROLS_IF_MSG_IF"] = "als";
Blockly.Msg["CONTROLS_IF_TOOLTIP_1"] = "Als een waarde waar is, voer dan opdrachten uit.";
Blockly.Msg["CONTROLS_IF_TOOLTIP_2"] = "Als een waarde waar is, voert dan het eerste blok met opdrachten uit. Voer andere het tweede blok met opdrachten uit.";
Blockly.Msg["CONTROLS_IF_TOOLTIP_3"] = "Als de eerste waarde waar is, voer dan het eerste blok met opdrachten uit. Voer anders, als de tweede waarde waar is, het tweede blok met opdrachten uit.";
Blockly.Msg["CONTROLS_IF_TOOLTIP_4"] = "Als de eerste waarde \"waar\" is, voer dan het eerste blok uit. Voer anders wanneer de tweede waarde \"waar\" is, het tweede blok uit. Als geen van beide waarden waar zijn, voer dan het laatste blok uit.";
Blockly.Msg["CONTROLS_REPEAT_HELPURL"] = "https://nl.wikipedia.org/wiki/Repetitie_(informatica)#For_en_Foreach";
Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"] = "voer uit";
Blockly.Msg["CONTROLS_REPEAT_TITLE"] = "%1 keer herhalen";
Blockly.Msg["CONTROLS_REPEAT_TOOLTIP"] = "Voer een aantal opdrachten meerdere keren uit.";
Blockly.Msg["CONTROLS_WHILEUNTIL_HELPURL"] = "https://github.com/google/blockly/wiki/Loops#repeat";
Blockly.Msg["CONTROLS_WHILEUNTIL_OPERATOR_UNTIL"] = "herhalen totdat";
Blockly.Msg["CONTROLS_WHILEUNTIL_OPERATOR_WHILE"] = "herhalen zolang";
Blockly.Msg["CONTROLS_WHILEUNTIL_TOOLTIP_UNTIL"] = "Terwijl een waarde onwaar is de volgende opdrachten uitvoeren.";
Blockly.Msg["CONTROLS_WHILEUNTIL_TOOLTIP_WHILE"] = "Terwijl een waarde waar is de volgende opdrachten uitvoeren.";
Blockly.Msg["DELETE_ALL_BLOCKS"] = "Alle %1 blokken verwijderen?";
Blockly.Msg["DELETE_BLOCK"] = "Blok verwijderen";
Blockly.Msg["DELETE_VARIABLE"] = "Verwijder de variabele \"%1\"";
Blockly.Msg["DELETE_VARIABLE_CONFIRMATION"] = "%1 gebruiken van de variabele \"%2\" verwijderen?";
Blockly.Msg["DELETE_X_BLOCKS"] = "%1 blokken verwijderen";
Blockly.Msg["DIALOG_CANCEL"] = "Annuleren";
Blockly.Msg["DIALOG_OK"] = "OK";
Blockly.Msg["DISABLE_BLOCK"] = "Blok uitschakelen";
Blockly.Msg["DUPLICATE_BLOCK"] = "Dupliceren";
Blockly.Msg["DUPLICATE_COMMENT"] = "Opmerking dupliceren";
Blockly.Msg["ENABLE_BLOCK"] = "Blok inschakelen";
Blockly.Msg["EXPAND_ALL"] = "Blokken uitvouwen";
Blockly.Msg["EXPAND_BLOCK"] = "Blok uitvouwen";
Blockly.Msg["EXTERNAL_INPUTS"] = "Externe invoer";
Blockly.Msg["HELP"] = "Hulp";
Blockly.Msg["INLINE_INPUTS"] = "Inline invoer";
Blockly.Msg["LISTS_CREATE_EMPTY_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#create-empty-list";
Blockly.Msg["LISTS_CREATE_EMPTY_TITLE"] = "maak een lege lijst";
Blockly.Msg["LISTS_CREATE_EMPTY_TOOLTIP"] = "Geeft een lijst terug met lengte 0, zonder items";
Blockly.Msg["LISTS_CREATE_WITH_CONTAINER_TITLE_ADD"] = "lijst";
Blockly.Msg["LISTS_CREATE_WITH_CONTAINER_TOOLTIP"] = "Voeg stukken toe, verwijder ze of wijzig de volgorde om dit lijstblok aan te passen.";
Blockly.Msg["LISTS_CREATE_WITH_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#create-list-with";  // untranslated
Blockly.Msg["LISTS_CREATE_WITH_INPUT_WITH"] = "maak een lijst met";
Blockly.Msg["LISTS_CREATE_WITH_ITEM_TOOLTIP"] = "Voeg iets toe aan de lijst.";
Blockly.Msg["LISTS_CREATE_WITH_TOOLTIP"] = "Maak een lijst met een willekeurig aantal items.";
Blockly.Msg["LISTS_GET_INDEX_FIRST"] = "eerste";
Blockly.Msg["LISTS_GET_INDEX_FROM_END"] = "# van einde";
Blockly.Msg["LISTS_GET_INDEX_FROM_START"] = "#";
Blockly.Msg["LISTS_GET_INDEX_GET"] = "haal op";
Blockly.Msg["LISTS_GET_INDEX_GET_REMOVE"] = "haal op en verwijder";
Blockly.Msg["LISTS_GET_INDEX_LAST"] = "laatste";
Blockly.Msg["LISTS_GET_INDEX_RANDOM"] = "willekeurig";
Blockly.Msg["LISTS_GET_INDEX_REMOVE"] = "verwijder";
Blockly.Msg["LISTS_GET_INDEX_TAIL"] = "";  // untranslated
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_FIRST"] = "Geeft het eerste item in een lijst terug.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_FROM"] = "Geeft het item op de opgegeven positie in een lijst.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_LAST"] = "Geeft het laatste item in een lijst terug.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_RANDOM"] = "Geeft een willekeurig item uit een lijst.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_FIRST"] = "Geeft het laatste item in een lijst terug en verwijdert het.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_FROM"] = "Geeft het item op de opgegeven positie in een lijst terug en verwijdert het.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_LAST"] = "Geeft het laatste item uit een lijst terug en verwijdert het.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_RANDOM"] = "Geeft een willekeurig item in een lijst terug en verwijdert het.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_REMOVE_FIRST"] = "Verwijdert het eerste item in een lijst.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_REMOVE_FROM"] = "Verwijdert het item op de opgegeven positie in een lijst.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_REMOVE_LAST"] = "Verwijdert het laatste item uit een lijst.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_REMOVE_RANDOM"] = "Verwijdert een willekeurig item uit een lijst.";
Blockly.Msg["LISTS_GET_SUBLIST_END_FROM_END"] = "naar # vanaf einde";
Blockly.Msg["LISTS_GET_SUBLIST_END_FROM_START"] = "naar item";
Blockly.Msg["LISTS_GET_SUBLIST_END_LAST"] = "naar laatste";
Blockly.Msg["LISTS_GET_SUBLIST_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#getting-a-sublist";
Blockly.Msg["LISTS_GET_SUBLIST_START_FIRST"] = "haal sublijst op vanaf eerste";
Blockly.Msg["LISTS_GET_SUBLIST_START_FROM_END"] = "haal sublijst op van positie vanaf einde";
Blockly.Msg["LISTS_GET_SUBLIST_START_FROM_START"] = "haal sublijst op vanaf positie";
Blockly.Msg["LISTS_GET_SUBLIST_TAIL"] = "";  // untranslated
Blockly.Msg["LISTS_GET_SUBLIST_TOOLTIP"] = "Maakt een kopie van het opgegeven deel van de lijst.";
Blockly.Msg["LISTS_INDEX_FROM_END_TOOLTIP"] = "Item %1 is het laatste item.";
Blockly.Msg["LISTS_INDEX_FROM_START_TOOLTIP"] = "Item %1 is het eerste item.";
Blockly.Msg["LISTS_INDEX_OF_FIRST"] = "zoek eerste voorkomen van item";
Blockly.Msg["LISTS_INDEX_OF_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#getting-items-from-a-list";
Blockly.Msg["LISTS_INDEX_OF_LAST"] = "zoek laatste voorkomen van item";
Blockly.Msg["LISTS_INDEX_OF_TOOLTIP"] = "Geeft de index terug van het eerste of laatste voorkomen van een item in de lijst. Geeft %1 terug als het item niet is gevonden.";
Blockly.Msg["LISTS_INLIST"] = "in lijst";
Blockly.Msg["LISTS_ISEMPTY_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#is-empty";  // untranslated
Blockly.Msg["LISTS_ISEMPTY_TITLE"] = "%1 is leeg";
Blockly.Msg["LISTS_ISEMPTY_TOOLTIP"] = "Geeft waar terug als de lijst leeg is.";
Blockly.Msg["LISTS_LENGTH_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#length-of";
Blockly.Msg["LISTS_LENGTH_TITLE"] = "lengte van %1";
Blockly.Msg["LISTS_LENGTH_TOOLTIP"] = "Geeft de lengte van een lijst terug.";
Blockly.Msg["LISTS_REPEAT_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#create-list-with";
Blockly.Msg["LISTS_REPEAT_TITLE"] = "Maak lijst met item %1, %2 keer herhaald";
Blockly.Msg["LISTS_REPEAT_TOOLTIP"] = "Maakt een lijst die bestaat uit de opgegeven waarde, het opgegeven aantal keer herhaald.";
Blockly.Msg["LISTS_REVERSE_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#reversing-a-list";  // untranslated
Blockly.Msg["LISTS_REVERSE_MESSAGE0"] = "%1 omkeren";
Blockly.Msg["LISTS_REVERSE_TOOLTIP"] = "Keert een kopie van een lijst om.";
Blockly.Msg["LISTS_SET_INDEX_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#in-list--set";
Blockly.Msg["LISTS_SET_INDEX_INPUT_TO"] = "als";
Blockly.Msg["LISTS_SET_INDEX_INSERT"] = "tussenvoegen op";
Blockly.Msg["LISTS_SET_INDEX_SET"] = "stel in";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_INSERT_FIRST"] = "Voegt het item toe aan het begin van de lijst.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_INSERT_FROM"] = "Voegt het item op een opgegeven positie in een lijst in.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_INSERT_LAST"] = "Voeg het item aan het einde van een lijst toe.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_INSERT_RANDOM"] = "Voegt het item op een willekeurige positie in de lijst in.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_SET_FIRST"] = "Stelt het eerste item in een lijst in.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_SET_FROM"] = "Zet het item op de opgegeven positie in de lijst.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_SET_LAST"] = "Stelt het laatste item van een lijst in.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_SET_RANDOM"] = "Stelt een willekeurig item uit de lijst in.";
Blockly.Msg["LISTS_SORT_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#sorting-a-list";
Blockly.Msg["LISTS_SORT_ORDER_ASCENDING"] = "oplopend";
Blockly.Msg["LISTS_SORT_ORDER_DESCENDING"] = "aflopend";
Blockly.Msg["LISTS_SORT_TITLE"] = "sorteer %1 %2 %3";
Blockly.Msg["LISTS_SORT_TOOLTIP"] = "Sorteer een kopie van een lijst.";
Blockly.Msg["LISTS_SORT_TYPE_IGNORECASE"] = "alfabetisch, negeer hoofd-/kleine letters";
Blockly.Msg["LISTS_SORT_TYPE_NUMERIC"] = "numeriek";
Blockly.Msg["LISTS_SORT_TYPE_TEXT"] = "alfabetisch";
Blockly.Msg["LISTS_SPLIT_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#splitting-strings-and-joining-lists";  // untranslated
Blockly.Msg["LISTS_SPLIT_LIST_FROM_TEXT"] = "lijst maken van tekst";
Blockly.Msg["LISTS_SPLIT_TEXT_FROM_LIST"] = "tekst maken van lijst";
Blockly.Msg["LISTS_SPLIT_TOOLTIP_JOIN"] = "Lijst van tekstdelen samenvoegen in één stuk tekst, waarbij de tekstdelen gescheiden zijn door een scheidingsteken.";
Blockly.Msg["LISTS_SPLIT_TOOLTIP_SPLIT"] = "Tekst splitsen in een lijst van teksten op basis van een scheidingsteken.";
Blockly.Msg["LISTS_SPLIT_WITH_DELIMITER"] = "met scheidingsteken";
Blockly.Msg["LOGIC_BOOLEAN_FALSE"] = "onwaar";
Blockly.Msg["LOGIC_BOOLEAN_HELPURL"] = "https://github.com/google/blockly/wiki/Logic#values";
Blockly.Msg["LOGIC_BOOLEAN_TOOLTIP"] = "Geeft \"waar\" of \"onwaar\" terug.";
Blockly.Msg["LOGIC_BOOLEAN_TRUE"] = "waar";
Blockly.Msg["LOGIC_COMPARE_HELPURL"] = "https://nl.wikipedia.org/wiki/Ongelijkheid_(wiskunde)";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_EQ"] = "Geeft \"waar\", als beide waarden gelijk aan elkaar zijn.";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_GT"] = "Geeft \"waar\" terug als de eerste invoer meer is dan de tweede invoer.";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_GTE"] = "Geeft \"waar\" terug als de eerste invoer groter is of gelijk aan de tweede invoer.";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_LT"] = "Geeft \"waar\" als de eerste invoer kleiner is dan de tweede invoer.";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_LTE"] = "Geeft \"waar\" terug als de eerste invoer kleiner of gelijk is aan de tweede invoer.";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_NEQ"] = "Geeft \"waar\" terug als de waarden niet gelijk zijn aan elkaar.";
Blockly.Msg["LOGIC_NEGATE_HELPURL"] = "https://github.com/google/blockly/wiki/Logic#not";
Blockly.Msg["LOGIC_NEGATE_TITLE"] = "niet %1";
Blockly.Msg["LOGIC_NEGATE_TOOLTIP"] = "Geeft \"waar\" terug als de invoer \"onwaar\" is. Geeft \"onwaar\" als de invoer \"waar\" is.";
Blockly.Msg["LOGIC_NULL"] = "niets";
Blockly.Msg["LOGIC_NULL_HELPURL"] = "https://en.wikipedia.org/wiki/Nullable_type";
Blockly.Msg["LOGIC_NULL_TOOLTIP"] = "Geeft niets terug.";
Blockly.Msg["LOGIC_OPERATION_AND"] = "en";
Blockly.Msg["LOGIC_OPERATION_HELPURL"] = "https://github.com/google/blockly/wiki/Logic#logical-operations";
Blockly.Msg["LOGIC_OPERATION_OR"] = "of";
Blockly.Msg["LOGIC_OPERATION_TOOLTIP_AND"] = "Geeft waar als beide waarden waar zijn.";
Blockly.Msg["LOGIC_OPERATION_TOOLTIP_OR"] = "Geeft \"waar\" terug als in ieder geval één van de waarden waar is.";
Blockly.Msg["LOGIC_TERNARY_CONDITION"] = "test";
Blockly.Msg["LOGIC_TERNARY_HELPURL"] = "https://en.wikipedia.org/wiki/%3F:";
Blockly.Msg["LOGIC_TERNARY_IF_FALSE"] = "als onwaar";
Blockly.Msg["LOGIC_TERNARY_IF_TRUE"] = "als waar";
Blockly.Msg["LOGIC_TERNARY_TOOLTIP"] = "Test de voorwaarde in \"test\". Als de voorwaarde \"waar\" is, geef de waarde van \"als waar\" terug; geef anders de waarde van \"als onwaar\" terug.";
Blockly.Msg["MATH_ADDITION_SYMBOL"] = "+";
Blockly.Msg["MATH_ARITHMETIC_HELPURL"] = "https://nl.wikipedia.org/wiki/Rekenen";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_ADD"] = "Geeft de som van 2 getallen.";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_DIVIDE"] = "Geeft de gedeelde waarde van twee getallen.";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_MINUS"] = "Geeft het verschil van de twee getallen.";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_MULTIPLY"] = "Geeft het product terug van de twee getallen.";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_POWER"] = "Geeft het eerste getal tot de macht van het tweede getal.";
Blockly.Msg["MATH_ATAN2_HELPURL"] = "https://en.wikipedia.org/wiki/Atan2";
Blockly.Msg["MATH_ATAN2_TITLE"] = "atan2 van X:%1 Y:%2";
Blockly.Msg["MATH_ATAN2_TOOLTIP"] = "Geef de boogtangens van punt (X, Y) terug in graden tussen -180 naar 180.";
Blockly.Msg["MATH_CHANGE_HELPURL"] = "https://en.wikipedia.org/wiki/Programming_idiom#Incrementing_a_counter";
Blockly.Msg["MATH_CHANGE_TITLE"] = "%1 wijzigen met %2";
Blockly.Msg["MATH_CHANGE_TOOLTIP"] = "Voegt een getal toe aan variabele \"%1\".";
Blockly.Msg["MATH_CONSTANT_HELPURL"] = "https://nl.wikipedia.org/wiki/Wiskundige_constante";
Blockly.Msg["MATH_CONSTANT_TOOLTIP"] = "Geeft een van de vaak voorkomende constante waardes:  π (3.141…), e (2.718…), φ (1.618…), √2 (1.414…), √½ (0.707…), of ∞ (oneindig).";
Blockly.Msg["MATH_CONSTRAIN_HELPURL"] = "https://en.wikipedia.org/wiki/Clamping_(graphics)";  // untranslated
Blockly.Msg["MATH_CONSTRAIN_TITLE"] = "beperk %1 van minimaal %2 tot maximaal %3";
Blockly.Msg["MATH_CONSTRAIN_TOOLTIP"] = "Beperk een getal tussen de twee opgegeven limieten (inclusief).";
Blockly.Msg["MATH_DIVISION_SYMBOL"] = "÷";
Blockly.Msg["MATH_IS_DIVISIBLE_BY"] = "is deelbaar door";
Blockly.Msg["MATH_IS_EVEN"] = "is even";
Blockly.Msg["MATH_IS_NEGATIVE"] = "is negatief";
Blockly.Msg["MATH_IS_ODD"] = "is oneven";
Blockly.Msg["MATH_IS_POSITIVE"] = "is positief";
Blockly.Msg["MATH_IS_PRIME"] = "is priemgetal";
Blockly.Msg["MATH_IS_TOOLTIP"] = "Test of een getal even, oneven, een priemgetal, geheel, positief of negatief is, of deelbaar is door een bepaald getal. Geeft \"waar\" of \"onwaar\".";
Blockly.Msg["MATH_IS_WHOLE"] = "is geheel getal";
Blockly.Msg["MATH_MODULO_HELPURL"] = "https://nl.wikipedia.org/wiki/Modulair_rekenen";
Blockly.Msg["MATH_MODULO_TITLE"] = "restgetal van %1 ÷ %2";
Blockly.Msg["MATH_MODULO_TOOLTIP"] = "Geeft het restgetal van het resultaat van de deling van de twee getallen.";
Blockly.Msg["MATH_MULTIPLICATION_SYMBOL"] = "×";
Blockly.Msg["MATH_NUMBER_HELPURL"] = "https://nl.wikipedia.org/wiki/Getal_%28wiskunde%29";
Blockly.Msg["MATH_NUMBER_TOOLTIP"] = "Een getal.";
Blockly.Msg["MATH_ONLIST_HELPURL"] = "";  // untranslated
Blockly.Msg["MATH_ONLIST_OPERATOR_AVERAGE"] = "gemiddelde van lijst";
Blockly.Msg["MATH_ONLIST_OPERATOR_MAX"] = "hoogste uit lijst";
Blockly.Msg["MATH_ONLIST_OPERATOR_MEDIAN"] = "mediaan van lijst";
Blockly.Msg["MATH_ONLIST_OPERATOR_MIN"] = "laagste uit lijst";
Blockly.Msg["MATH_ONLIST_OPERATOR_MODE"] = "modi van lijst";
Blockly.Msg["MATH_ONLIST_OPERATOR_RANDOM"] = "willekeurige item van lijst";
Blockly.Msg["MATH_ONLIST_OPERATOR_STD_DEV"] = "standaarddeviatie van lijst";
Blockly.Msg["MATH_ONLIST_OPERATOR_SUM"] = "som van lijst";
Blockly.Msg["MATH_ONLIST_TOOLTIP_AVERAGE"] = "Geeft het gemiddelde terug van de numerieke waardes in een lijst.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_MAX"] = "Geeft het grootste getal in een lijst.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_MEDIAN"] = "Geeft de mediaan in de lijst.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_MIN"] = "Geeft het kleinste getal uit een lijst.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_MODE"] = "Geeft een lijst van de meest voorkomende onderdelen in de lijst.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_RANDOM"] = "Geeft een willekeurig item uit de lijst terug.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_STD_DEV"] = "Geeft de standaardafwijking van de lijst.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_SUM"] = "Geeft de som van alle getallen in de lijst.";
Blockly.Msg["MATH_POWER_SYMBOL"] = "^";
Blockly.Msg["MATH_RANDOM_FLOAT_HELPURL"] = "https://nl.wikipedia.org/wiki/Toevalsgenerator";
Blockly.Msg["MATH_RANDOM_FLOAT_TITLE_RANDOM"] = "willekeurige fractie";
Blockly.Msg["MATH_RANDOM_FLOAT_TOOLTIP"] = "Geeft een willekeurige fractie tussen 0.0 (inclusief) en 1.0 (exclusief).";
Blockly.Msg["MATH_RANDOM_INT_HELPURL"] = "https://nl.wikipedia.org/wiki/Toevalsgenerator";
Blockly.Msg["MATH_RANDOM_INT_TITLE"] = "willekeurig geheel getal van %1 tot %2";
Blockly.Msg["MATH_RANDOM_INT_TOOLTIP"] = "Geeft een willekeurig getal tussen de 2 opgegeven limieten in, inclusief.";
Blockly.Msg["MATH_ROUND_HELPURL"] = "https://nl.wikipedia.org/wiki/Afronden";
Blockly.Msg["MATH_ROUND_OPERATOR_ROUND"] = "afronden";
Blockly.Msg["MATH_ROUND_OPERATOR_ROUNDDOWN"] = "naar beneden afronden";
Blockly.Msg["MATH_ROUND_OPERATOR_ROUNDUP"] = "omhoog afronden";
Blockly.Msg["MATH_ROUND_TOOLTIP"] = "Rondt een getal af omhoog of naar beneden.";
Blockly.Msg["MATH_SINGLE_HELPURL"] = "https://nl.wikipedia.org/wiki/Vierkantswortel";
Blockly.Msg["MATH_SINGLE_OP_ABSOLUTE"] = "absoluut";
Blockly.Msg["MATH_SINGLE_OP_ROOT"] = "wortel";
Blockly.Msg["MATH_SINGLE_TOOLTIP_ABS"] = "Geeft de absolute waarde van een getal.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_EXP"] = "Geeft e tot de macht van een getal.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_LN"] = "Geeft het natuurlijk logaritme van een getal.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_LOG10"] = "Geeft het logaritme basis 10 van een getal.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_NEG"] = "Geeft de negatief van een getal.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_POW10"] = "Geeft 10 tot de macht van een getal.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_ROOT"] = "Geeft de wortel van een getal.";
Blockly.Msg["MATH_SUBTRACTION_SYMBOL"] = "-";
Blockly.Msg["MATH_TRIG_ACOS"] = "acos";
Blockly.Msg["MATH_TRIG_ASIN"] = "asin";
Blockly.Msg["MATH_TRIG_ATAN"] = "arctan";
Blockly.Msg["MATH_TRIG_COS"] = "cos";
Blockly.Msg["MATH_TRIG_HELPURL"] = "https://nl.wikipedia.org/wiki/Goniometrische_functie";
Blockly.Msg["MATH_TRIG_SIN"] = "sin";
Blockly.Msg["MATH_TRIG_TAN"] = "tan";
Blockly.Msg["MATH_TRIG_TOOLTIP_ACOS"] = "Geeft de arccosinus van een getal.";
Blockly.Msg["MATH_TRIG_TOOLTIP_ASIN"] = "Geeft de arcsinus van een getal.";
Blockly.Msg["MATH_TRIG_TOOLTIP_ATAN"] = "Geeft de arctangens van een getal.";
Blockly.Msg["MATH_TRIG_TOOLTIP_COS"] = "Geeft de cosinus van een graad (geen radialen).";
Blockly.Msg["MATH_TRIG_TOOLTIP_SIN"] = "Geeft de sinus van een graad (geen radialen).";
Blockly.Msg["MATH_TRIG_TOOLTIP_TAN"] = "Geeft de tangens van een graad (geen radialen).";
Blockly.Msg["NEW_COLOUR_VARIABLE"] = "Creëer kleurvariabele";
Blockly.Msg["NEW_NUMBER_VARIABLE"] = "Creëer numeriek variabele";
Blockly.Msg["NEW_STRING_VARIABLE"] = "Creëer tekstvariabele";
Blockly.Msg["NEW_VARIABLE"] = "Variabele maken...";
Blockly.Msg["NEW_VARIABLE_TITLE"] = "Nieuwe variabelenaam:";
Blockly.Msg["NEW_VARIABLE_TYPE_TITLE"] = "Nieuw soort variabele";
Blockly.Msg["ORDINAL_NUMBER_SUFFIX"] = "";  // untranslated
Blockly.Msg["PROCEDURES_ALLOW_STATEMENTS"] = "statements toestaan";
Blockly.Msg["PROCEDURES_BEFORE_PARAMS"] = "met:";
Blockly.Msg["PROCEDURES_CALLNORETURN_HELPURL"] = "https://nl.wikipedia.org/wiki/Subprogramma";
Blockly.Msg["PROCEDURES_CALLNORETURN_TOOLTIP"] = "Voer de door de gebruiker gedefinieerde functie \"%1\" uit.";
Blockly.Msg["PROCEDURES_CALLRETURN_HELPURL"] = "https://nl.wikipedia.org/wiki/Subprogramma";
Blockly.Msg["PROCEDURES_CALLRETURN_TOOLTIP"] = "Voer de door de gebruiker gedefinieerde functie \"%1\" uit en gebruik de uitvoer.";
Blockly.Msg["PROCEDURES_CALL_BEFORE_PARAMS"] = "met:";
Blockly.Msg["PROCEDURES_CREATE_DO"] = "Maak \"%1\"";
Blockly.Msg["PROCEDURES_DEFNORETURN_COMMENT"] = "Deze functie beschrijven...";
Blockly.Msg["PROCEDURES_DEFNORETURN_DO"] = "";  // untranslated
Blockly.Msg["PROCEDURES_DEFNORETURN_HELPURL"] = "https://nl.wikipedia.org/wiki/Subprogramma";
Blockly.Msg["PROCEDURES_DEFNORETURN_PROCEDURE"] = "doe iets";
Blockly.Msg["PROCEDURES_DEFNORETURN_TITLE"] = "om";
Blockly.Msg["PROCEDURES_DEFNORETURN_TOOLTIP"] = "Maakt een functie zonder uitvoer.";
Blockly.Msg["PROCEDURES_DEFRETURN_HELPURL"] = "https://nl.wikipedia.org/wiki/Subprogramma";
Blockly.Msg["PROCEDURES_DEFRETURN_RETURN"] = "uitvoeren";
Blockly.Msg["PROCEDURES_DEFRETURN_TOOLTIP"] = "Maakt een functie met een uitvoer.";
Blockly.Msg["PROCEDURES_DEF_DUPLICATE_WARNING"] = "Waarschuwing: deze functie heeft parameters met dezelfde naam.";
Blockly.Msg["PROCEDURES_HIGHLIGHT_DEF"] = "Accentueer functiedefinitie";
Blockly.Msg["PROCEDURES_IFRETURN_HELPURL"] = "http://c2.com/cgi/wiki?GuardClause";
Blockly.Msg["PROCEDURES_IFRETURN_TOOLTIP"] = "Als de eerste waarde \"waar\" is, geef dan de tweede waarde terug.";
Blockly.Msg["PROCEDURES_IFRETURN_WARNING"] = "Waarschuwing: dit blok mag alleen gebruikt worden binnen de definitie van een functie.";
Blockly.Msg["PROCEDURES_MUTATORARG_TITLE"] = "invoernaam:";
Blockly.Msg["PROCEDURES_MUTATORARG_TOOLTIP"] = "Een invoer aan de functie toevoegen.";
Blockly.Msg["PROCEDURES_MUTATORCONTAINER_TITLE"] = "ingangen";
Blockly.Msg["PROCEDURES_MUTATORCONTAINER_TOOLTIP"] = "Invoer van deze functie toevoegen, verwijderen of herordenen.";
Blockly.Msg["REDO"] = "Opnieuw";
Blockly.Msg["REMOVE_COMMENT"] = "Opmerking verwijderen";
Blockly.Msg["RENAME_VARIABLE"] = "Variabele hernoemen...";
Blockly.Msg["RENAME_VARIABLE_TITLE"] = "Alle variabelen \"%1\" hernoemen naar:";
Blockly.Msg["TEXT_APPEND_HELPURL"] = "https://github.com/google/blockly/wiki/Text#text-modification";
Blockly.Msg["TEXT_APPEND_TITLE"] = "voor%1 voeg tekst toe van %2";
Blockly.Msg["TEXT_APPEND_TOOLTIP"] = "Voeg tekst toe aan de variabele \"%1\".";
Blockly.Msg["TEXT_CHANGECASE_HELPURL"] = "https://github.com/google/blockly/wiki/Text#adjusting-text-case";
Blockly.Msg["TEXT_CHANGECASE_OPERATOR_LOWERCASE"] = "naar kleine letters";
Blockly.Msg["TEXT_CHANGECASE_OPERATOR_TITLECASE"] = "naar Hoofdletter Per Woord";
Blockly.Msg["TEXT_CHANGECASE_OPERATOR_UPPERCASE"] = "naar HOOFDLETTERS";
Blockly.Msg["TEXT_CHANGECASE_TOOLTIP"] = "Geef een kopie van de tekst met veranderde hoofdletters terug.";
Blockly.Msg["TEXT_CHARAT_FIRST"] = "haal eerste letter op";
Blockly.Msg["TEXT_CHARAT_FROM_END"] = "haal letter # op vanaf einde";
Blockly.Msg["TEXT_CHARAT_FROM_START"] = "haal letter # op";
Blockly.Msg["TEXT_CHARAT_HELPURL"] = "https://github.com/google/blockly/wiki/Text#extracting-text";
Blockly.Msg["TEXT_CHARAT_LAST"] = "haal laatste letter op";
Blockly.Msg["TEXT_CHARAT_RANDOM"] = "haal willekeurige letter op";
Blockly.Msg["TEXT_CHARAT_TAIL"] = "";  // untranslated
Blockly.Msg["TEXT_CHARAT_TITLE"] = "in tekst %1 %2";
Blockly.Msg["TEXT_CHARAT_TOOLTIP"] = "Geeft de letter op de opgegeven positie terug.";
Blockly.Msg["TEXT_COUNT_HELPURL"] = "https://github.com/google/blockly/wiki/Text#counting-substrings";  // untranslated
Blockly.Msg["TEXT_COUNT_MESSAGE0"] = "%1 in %2 tellen";
Blockly.Msg["TEXT_COUNT_TOOLTIP"] = "Tel hoe vaak bepaalde tekst voorkomt in andere tekst.";
Blockly.Msg["TEXT_CREATE_JOIN_ITEM_TOOLTIP"] = "Voegt een item aan de tekst toe.";
Blockly.Msg["TEXT_CREATE_JOIN_TITLE_JOIN"] = "samenvoegen";
Blockly.Msg["TEXT_CREATE_JOIN_TOOLTIP"] = "Toevoegen, verwijderen of volgorde wijzigen van secties om dit tekstblok opnieuw in te stellen.";
Blockly.Msg["TEXT_GET_SUBSTRING_END_FROM_END"] = "van letter # tot einde";
Blockly.Msg["TEXT_GET_SUBSTRING_END_FROM_START"] = "naar letter #";
Blockly.Msg["TEXT_GET_SUBSTRING_END_LAST"] = "naar laatste letter";
Blockly.Msg["TEXT_GET_SUBSTRING_HELPURL"] = "https://github.com/google/blockly/wiki/Text#extracting-a-region-of-text";
Blockly.Msg["TEXT_GET_SUBSTRING_INPUT_IN_TEXT"] = "in tekst";
Blockly.Msg["TEXT_GET_SUBSTRING_START_FIRST"] = "haal subtekst op van eerste letter";
Blockly.Msg["TEXT_GET_SUBSTRING_START_FROM_END"] = "haal subtekst op vanaf letter # vanaf einde";
Blockly.Msg["TEXT_GET_SUBSTRING_START_FROM_START"] = "haal subtekst op vanaf letter #";
Blockly.Msg["TEXT_GET_SUBSTRING_TAIL"] = "";  // untranslated
Blockly.Msg["TEXT_GET_SUBSTRING_TOOLTIP"] = "Geeft het opgegeven onderdeel van de tekst terug.";
Blockly.Msg["TEXT_INDEXOF_HELPURL"] = "https://github.com/google/blockly/wiki/Text#finding-text";
Blockly.Msg["TEXT_INDEXOF_OPERATOR_FIRST"] = "zoek eerste voorkomen van tekst";
Blockly.Msg["TEXT_INDEXOF_OPERATOR_LAST"] = "zoek het laatste voorkomen van tekst";
Blockly.Msg["TEXT_INDEXOF_TITLE"] = "in tekst %1 %2 %3";
Blockly.Msg["TEXT_INDEXOF_TOOLTIP"] = "Geeft de index terug van het eerste of laatste voorkomen van de eerste tekst in de tweede tekst. Geeft %1 terug als de tekst niet gevonden is.";
Blockly.Msg["TEXT_ISEMPTY_HELPURL"] = "https://github.com/google/blockly/wiki/Text#checking-for-empty-text";
Blockly.Msg["TEXT_ISEMPTY_TITLE"] = "%1 is leeg";
Blockly.Msg["TEXT_ISEMPTY_TOOLTIP"] = "Geeft \"waar\" terug, als de opgegeven tekst leeg is.";
Blockly.Msg["TEXT_JOIN_HELPURL"] = "https://github.com/google/blockly/wiki/Text#text-creation";
Blockly.Msg["TEXT_JOIN_TITLE_CREATEWITH"] = "maak tekst met";
Blockly.Msg["TEXT_JOIN_TOOLTIP"] = "Maakt een stuk tekst door één of meer items samen te voegen.";
Blockly.Msg["TEXT_LENGTH_HELPURL"] = "https://github.com/google/blockly/wiki/Text#text-modification";
Blockly.Msg["TEXT_LENGTH_TITLE"] = "lengte van %1";
Blockly.Msg["TEXT_LENGTH_TOOLTIP"] = "Geeft het aantal tekens terug (inclusief spaties) in de opgegeven tekst.";
Blockly.Msg["TEXT_PRINT_HELPURL"] = "https://github.com/google/blockly/wiki/Text#printing-text";
Blockly.Msg["TEXT_PRINT_TITLE"] = "tekst weergeven: %1";
Blockly.Msg["TEXT_PRINT_TOOLTIP"] = "Drukt de opgegeven tekst, getal of een andere waarde af.";
Blockly.Msg["TEXT_PROMPT_HELPURL"] = "https://github.com/google/blockly/wiki/Text#getting-input-from-the-user";
Blockly.Msg["TEXT_PROMPT_TOOLTIP_NUMBER"] = "Vraagt de gebruiker om een getal in te voeren.";
Blockly.Msg["TEXT_PROMPT_TOOLTIP_TEXT"] = "Vraagt de gebruiker om invoer.";
Blockly.Msg["TEXT_PROMPT_TYPE_NUMBER"] = "vraagt de gebruiker om een getal met de tekst";
Blockly.Msg["TEXT_PROMPT_TYPE_TEXT"] = "vraagt om invoer met bericht";
Blockly.Msg["TEXT_REPLACE_HELPURL"] = "https://github.com/google/blockly/wiki/Text#replacing-substrings";  // untranslated
Blockly.Msg["TEXT_REPLACE_MESSAGE0"] = "vervang %1 door %2 in %3";
Blockly.Msg["TEXT_REPLACE_TOOLTIP"] = "Vervang alle voorkomens van tekst in een andere tekst.";
Blockly.Msg["TEXT_REVERSE_HELPURL"] = "https://github.com/google/blockly/wiki/Text#reversing-text";  // untranslated
Blockly.Msg["TEXT_REVERSE_MESSAGE0"] = "%1 omkeren";
Blockly.Msg["TEXT_REVERSE_TOOLTIP"] = "Keert de volgorde van de tekens in de tekst om.";
Blockly.Msg["TEXT_TEXT_HELPURL"] = "https://nl.wikipedia.org/wiki/String_%28informatica%29";
Blockly.Msg["TEXT_TEXT_TOOLTIP"] = "Een letter, woord of een regel tekst.";
Blockly.Msg["TEXT_TRIM_HELPURL"] = "https://github.com/google/blockly/wiki/Text#trimming-removing-spaces";
Blockly.Msg["TEXT_TRIM_OPERATOR_BOTH"] = "spaties van beide kanten afhalen van";
Blockly.Msg["TEXT_TRIM_OPERATOR_LEFT"] = "spaties van de linkerkant verwijderen van";
Blockly.Msg["TEXT_TRIM_OPERATOR_RIGHT"] = "spaties van de rechterkant verwijderen van";
Blockly.Msg["TEXT_TRIM_TOOLTIP"] = "Geeft een kopie van de tekst met verwijderde spaties van één of beide kanten.";
Blockly.Msg["TODAY"] = "Vandaag";
Blockly.Msg["UNDO"] = "Ongedaan maken";
Blockly.Msg["UNNAMED_KEY"] = "zonder naam";
Blockly.Msg["VARIABLES_DEFAULT_NAME"] = "item";
Blockly.Msg["VARIABLES_GET_CREATE_SET"] = "Maak \"verander %1\"";
Blockly.Msg["VARIABLES_GET_HELPURL"] = "https://github.com/google/blockly/wiki/Variables#get";
Blockly.Msg["VARIABLES_GET_TOOLTIP"] = "Geeft de waarde van deze variabele.";
Blockly.Msg["VARIABLES_SET"] = "stel %1 in op %2";
Blockly.Msg["VARIABLES_SET_CREATE_GET"] = "Maak 'opvragen van %1'";
Blockly.Msg["VARIABLES_SET_HELPURL"] = "https://github.com/google/blockly/wiki/Variables#set";
Blockly.Msg["VARIABLES_SET_TOOLTIP"] = "Verandert de waarde van de variabele naar de waarde van de invoer.";
Blockly.Msg["VARIABLE_ALREADY_EXISTS"] = "Er bestaat al een variabele met de naam \"%1\".";
Blockly.Msg["VARIABLE_ALREADY_EXISTS_FOR_ANOTHER_TYPE"] = "Een variabele met de naam '%1' bestaat al voor een ander soort variabele: '%2'.";
Blockly.Msg["WORKSPACE_ARIA_LABEL"] = "Blockly werkruimte";
Blockly.Msg["WORKSPACE_COMMENT_DEFAULT_TEXT"] = "Zeg iets...";
Blockly.Msg["CONTROLS_FOREACH_INPUT_DO"] = Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"];
Blockly.Msg["CONTROLS_FOR_INPUT_DO"] = Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"];
Blockly.Msg["CONTROLS_IF_ELSEIF_TITLE_ELSEIF"] = Blockly.Msg["CONTROLS_IF_MSG_ELSEIF"];
Blockly.Msg["CONTROLS_IF_ELSE_TITLE_ELSE"] = Blockly.Msg["CONTROLS_IF_MSG_ELSE"];
Blockly.Msg["CONTROLS_IF_IF_TITLE_IF"] = Blockly.Msg["CONTROLS_IF_MSG_IF"];
Blockly.Msg["CONTROLS_IF_MSG_THEN"] = Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"];
Blockly.Msg["CONTROLS_WHILEUNTIL_INPUT_DO"] = Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"];
Blockly.Msg["LISTS_CREATE_WITH_ITEM_TITLE"] = Blockly.Msg["VARIABLES_DEFAULT_NAME"];
Blockly.Msg["LISTS_GET_INDEX_HELPURL"] = Blockly.Msg["LISTS_INDEX_OF_HELPURL"];
Blockly.Msg["LISTS_GET_INDEX_INPUT_IN_LIST"] = Blockly.Msg["LISTS_INLIST"];
Blockly.Msg["LISTS_GET_SUBLIST_INPUT_IN_LIST"] = Blockly.Msg["LISTS_INLIST"];
Blockly.Msg["LISTS_INDEX_OF_INPUT_IN_LIST"] = Blockly.Msg["LISTS_INLIST"];
Blockly.Msg["LISTS_SET_INDEX_INPUT_IN_LIST"] = Blockly.Msg["LISTS_INLIST"];
Blockly.Msg["MATH_CHANGE_TITLE_ITEM"] = Blockly.Msg["VARIABLES_DEFAULT_NAME"];
Blockly.Msg["PROCEDURES_DEFRETURN_COMMENT"] = Blockly.Msg["PROCEDURES_DEFNORETURN_COMMENT"];
Blockly.Msg["PROCEDURES_DEFRETURN_DO"] = Blockly.Msg["PROCEDURES_DEFNORETURN_DO"];
Blockly.Msg["PROCEDURES_DEFRETURN_PROCEDURE"] = Blockly.Msg["PROCEDURES_DEFNORETURN_PROCEDURE"];
Blockly.Msg["PROCEDURES_DEFRETURN_TITLE"] = Blockly.Msg["PROCEDURES_DEFNORETURN_TITLE"];
Blockly.Msg["TEXT_APPEND_VARIABLE"] = Blockly.Msg["VARIABLES_DEFAULT_NAME"];
Blockly.Msg["TEXT_CREATE_JOIN_ITEM_TITLE_ITEM"] = Blockly.Msg["VARIABLES_DEFAULT_NAME"];

Blockly.Msg["MATH_HUE"] = "230";
Blockly.Msg["LOOPS_HUE"] = "120";
Blockly.Msg["LISTS_HUE"] = "260";
Blockly.Msg["LOGIC_HUE"] = "210";
Blockly.Msg["VARIABLES_HUE"] = "330";
Blockly.Msg["TEXTS_HUE"] = "160";
Blockly.Msg["PROCEDURES_HUE"] = "290";
Blockly.Msg["COLOUR_HUE"] = "20";
Blockly.Msg["VARIABLES_DYNAMIC_HUE"] = "310";