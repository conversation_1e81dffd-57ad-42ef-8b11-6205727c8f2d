// This file was automatically generated.  Do not modify.

'use strict';

Blockly.Msg["ADD_COMMENT"] = "Dodaj Ko<PERSON>arz";
Blockly.Msg["CANNOT_DELETE_VARIABLE_PROCEDURE"] = "Nie można usunąć zmiennej '%1', poni<PERSON><PERSON><PERSON> jest częś<PERSON>ą definicji funkcji '%2'";
Blockly.Msg["CHANGE_VALUE_TITLE"] = "Zmień wartość:";
Blockly.Msg["CLEAN_UP"] = "Uporządkuj Bloki";
Blockly.Msg["COLLAPSED_WARNINGS_WARNING"] = "Zwinięte bloki zawierają ostrzeżenia.";
Blockly.Msg["COLLAPSE_ALL"] = "Zwiń Bloki";
Blockly.Msg["COLLAPSE_BLOCK"] = "Zwiń Klocek";
Blockly.Msg["COLOUR_BLEND_COLOUR1"] = "kolor 1";
Blockly.Msg["COLOUR_BLEND_COLOUR2"] = "kolor 2";
Blockly.Msg["COLOUR_BLEND_HELPURL"] = "http://meyerweb.com/eric/tools/color-blend/";
Blockly.Msg["COLOUR_BLEND_RATIO"] = "proporcja";
Blockly.Msg["COLOUR_BLEND_TITLE"] = "wymieszaj";
Blockly.Msg["COLOUR_BLEND_TOOLTIP"] = "Miesza dwa kolory w danej proporcji (0.0 - 1.0).";
Blockly.Msg["COLOUR_PICKER_HELPURL"] = "https://en.wikipedia.org/wiki/Color";
Blockly.Msg["COLOUR_PICKER_TOOLTIP"] = "Wybierz kolor z palety.";
Blockly.Msg["COLOUR_RANDOM_HELPURL"] = "http://randomcolour.com";  // untranslated
Blockly.Msg["COLOUR_RANDOM_TITLE"] = "losowy kolor";
Blockly.Msg["COLOUR_RANDOM_TOOLTIP"] = "Wybierz kolor w sposób losowy.";
Blockly.Msg["COLOUR_RGB_BLUE"] = "niebieski";
Blockly.Msg["COLOUR_RGB_GREEN"] = "zielony";
Blockly.Msg["COLOUR_RGB_HELPURL"] = "http://www.december.com/html/spec/colorper.html";
Blockly.Msg["COLOUR_RGB_RED"] = "czerwony";
Blockly.Msg["COLOUR_RGB_TITLE"] = "kolor z";
Blockly.Msg["COLOUR_RGB_TOOLTIP"] = "Utwórz kolor składający sie z podanej ilości czerwieni, zieleni i błękitu. Zakres wartości: 0 do 100.";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_HELPURL"] = "https://github.com/google/blockly/wiki/Loops#loop-termination-blocks";  // untranslated
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_OPERATOR_BREAK"] = "przerwij pętlę";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_OPERATOR_CONTINUE"] = "przejdź do kolejnej iteracji pętli";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_TOOLTIP_BREAK"] = "Przerwij działanie pętli.";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_TOOLTIP_CONTINUE"] = "Pomiń resztę pętli i kontynuuj w kolejnej iteracji.";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_WARNING"] = "Uwaga: Ten klocek może być użyty tylko wewnątrz pętli.";
Blockly.Msg["CONTROLS_FOREACH_HELPURL"] = "https://github.com/google/blockly/wiki/Loops#for-each";  // untranslated
Blockly.Msg["CONTROLS_FOREACH_TITLE"] = "dla każdego elementu %1 listy %2";
Blockly.Msg["CONTROLS_FOREACH_TOOLTIP"] = "Przypisz zmiennej '%1' kolejno wartość każdego elementu listy, a następnie wykonaj kilka instrukcji.";
Blockly.Msg["CONTROLS_FOR_HELPURL"] = "https://github.com/google/blockly/wiki/Loops#count-with";  // untranslated
Blockly.Msg["CONTROLS_FOR_TITLE"] = "licz z %1 od %2 do %3 co %4 (wartość kroku)";
Blockly.Msg["CONTROLS_FOR_TOOLTIP"] = "Zmiennej '%1' przypisuje wartości z podanego zakresu z podanym interwałem i wykonuje zadane bloki.";
Blockly.Msg["CONTROLS_IF_ELSEIF_TOOLTIP"] = "Dodaj warunek do klocka „jeśli”.";
Blockly.Msg["CONTROLS_IF_ELSE_TOOLTIP"] = "Dodaj ostatni zawsze prawdziwy warunek do klocka „jeśli”.";
Blockly.Msg["CONTROLS_IF_HELPURL"] = "https://github.com/google/blockly/wiki/IfElse";  // untranslated
Blockly.Msg["CONTROLS_IF_IF_TOOLTIP"] = "Dodaj, usuń lub zmień kolejność czynności, żeby zmodyfikować ten klocek „jeśli”.";
Blockly.Msg["CONTROLS_IF_MSG_ELSE"] = "w przeciwnym razie";
Blockly.Msg["CONTROLS_IF_MSG_ELSEIF"] = "w przeciwnym razie, jeśli";
Blockly.Msg["CONTROLS_IF_MSG_IF"] = "jeśli";
Blockly.Msg["CONTROLS_IF_TOOLTIP_1"] = "Jeśli warunek jest spełniony, wykonaj zadane czynności.";
Blockly.Msg["CONTROLS_IF_TOOLTIP_2"] = "Jeśli warunek jest spełniony, wykonaj pierwszy blok instrukcji.  W przeciwnym razie, wykonaj drugi blok instrukcji.";
Blockly.Msg["CONTROLS_IF_TOOLTIP_3"] = "Jeśli pierwszy warunek jest spełniony, wykonaj pierwszy blok instrukcji.  W przeciwnym razie, jeśli drugi warunek jest spełniony, wykonaj drugi blok instrukcji.";
Blockly.Msg["CONTROLS_IF_TOOLTIP_4"] = "Jeśli pierwszy warunek jest spełniony, wykonaj pierwszy blok czynności.  W przeciwnym razie jeśli drugi warunek jest spełniony, wykonaj drugi blok czynności.  Jeżeli żaden z warunków nie zostanie spełniony, wykonaj ostatni blok czynności.";
Blockly.Msg["CONTROLS_REPEAT_HELPURL"] = "https://en.wikipedia.org/wiki/For_loop";
Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"] = "wykonaj";
Blockly.Msg["CONTROLS_REPEAT_TITLE"] = "powtórz %1 razy";
Blockly.Msg["CONTROLS_REPEAT_TOOLTIP"] = "Wykonaj niektóre instrukcje kilka razy.";
Blockly.Msg["CONTROLS_WHILEUNTIL_HELPURL"] = "https://github.com/google/blockly/wiki/Loops#repeat";  // untranslated
Blockly.Msg["CONTROLS_WHILEUNTIL_OPERATOR_UNTIL"] = "powtarzaj aż do";
Blockly.Msg["CONTROLS_WHILEUNTIL_OPERATOR_WHILE"] = "powtarzaj dopóki";
Blockly.Msg["CONTROLS_WHILEUNTIL_TOOLTIP_UNTIL"] = "Dopóki wyrażenie jest nieprawdziwe, wykonaj zadane czynności.";
Blockly.Msg["CONTROLS_WHILEUNTIL_TOOLTIP_WHILE"] = "Dopóki wyrażenie jest prawdziwe, wykonaj zadane czynności.";
Blockly.Msg["DELETE_ALL_BLOCKS"] = "Usunąć wszystkie klocki z %1?";
Blockly.Msg["DELETE_BLOCK"] = "Usuń Klocek";
Blockly.Msg["DELETE_VARIABLE"] = "Usuń zmienną '%1'";
Blockly.Msg["DELETE_VARIABLE_CONFIRMATION"] = "Usunąć %1 wystąpień zmiennej '%2'?";
Blockly.Msg["DELETE_X_BLOCKS"] = "Usuń %1 Bloki(ów)";
Blockly.Msg["DIALOG_CANCEL"] = "Anuluj";
Blockly.Msg["DIALOG_OK"] = "OK";
Blockly.Msg["DISABLE_BLOCK"] = "Wyłącz Klocek";
Blockly.Msg["DUPLICATE_BLOCK"] = "Powiel";
Blockly.Msg["DUPLICATE_COMMENT"] = "Zduplikowany komentarz";
Blockly.Msg["ENABLE_BLOCK"] = "Włącz Blok";
Blockly.Msg["EXPAND_ALL"] = "Rozwiń Bloki";
Blockly.Msg["EXPAND_BLOCK"] = "Rozwiń Klocek";
Blockly.Msg["EXTERNAL_INPUTS"] = "Zewnętrzne Wejścia";
Blockly.Msg["HELP"] = "Pomoc";
Blockly.Msg["INLINE_INPUTS"] = "Wbudowane Wejścia";
Blockly.Msg["LISTS_CREATE_EMPTY_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#create-empty-list";
Blockly.Msg["LISTS_CREATE_EMPTY_TITLE"] = "utwórz pustą listę";
Blockly.Msg["LISTS_CREATE_EMPTY_TOOLTIP"] = "Zwraca listę o długości 0, nie zawierającą danych";
Blockly.Msg["LISTS_CREATE_WITH_CONTAINER_TITLE_ADD"] = "lista";
Blockly.Msg["LISTS_CREATE_WITH_CONTAINER_TOOLTIP"] = "Dodaj, usuń lub zmień kolejność sekcji aby przekonfigurować blok tej listy.";
Blockly.Msg["LISTS_CREATE_WITH_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#create-list-with";  // untranslated
Blockly.Msg["LISTS_CREATE_WITH_INPUT_WITH"] = "utwórz listę z";
Blockly.Msg["LISTS_CREATE_WITH_ITEM_TOOLTIP"] = "Dodaj element do listy.";
Blockly.Msg["LISTS_CREATE_WITH_TOOLTIP"] = "Utwórz listę z dowolną ilością elementów.";
Blockly.Msg["LISTS_GET_INDEX_FIRST"] = "pierwszy";
Blockly.Msg["LISTS_GET_INDEX_FROM_END"] = "# od końca";
Blockly.Msg["LISTS_GET_INDEX_FROM_START"] = "#";
Blockly.Msg["LISTS_GET_INDEX_GET"] = "pobierz";
Blockly.Msg["LISTS_GET_INDEX_GET_REMOVE"] = "zabierz";
Blockly.Msg["LISTS_GET_INDEX_LAST"] = "ostatni";
Blockly.Msg["LISTS_GET_INDEX_RANDOM"] = "losowy";
Blockly.Msg["LISTS_GET_INDEX_REMOVE"] = "usuń";
Blockly.Msg["LISTS_GET_INDEX_TAIL"] = "";  // untranslated
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_FIRST"] = "Zwraca pierwszy element z listy.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_FROM"] = "Zwraca element z konkretnej pozycji na liście.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_LAST"] = "Zwraca ostatni element z listy.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_RANDOM"] = "Zwraca losowy element z listy.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_FIRST"] = "Usuwa i zwraca pierwszy element z listy.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_FROM"] = "Usuwa i zwraca element z określonej pozycji na liście.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_LAST"] = "Usuwa i zwraca ostatni element z listy.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_RANDOM"] = "Usuwa i zwraca losowy element z listy.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_REMOVE_FIRST"] = "Usuwa pierwszy element z listy.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_REMOVE_FROM"] = "Usuwa element z określonej pozycji na liście.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_REMOVE_LAST"] = "Usuwa ostatni element z listy.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_REMOVE_RANDOM"] = "Usuwa losowy element z listy.";
Blockly.Msg["LISTS_GET_SUBLIST_END_FROM_END"] = "do # od końca";
Blockly.Msg["LISTS_GET_SUBLIST_END_FROM_START"] = "do #";
Blockly.Msg["LISTS_GET_SUBLIST_END_LAST"] = "do ostatniego";
Blockly.Msg["LISTS_GET_SUBLIST_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#getting-a-sublist";  // untranslated
Blockly.Msg["LISTS_GET_SUBLIST_START_FIRST"] = "utwórz listę podrzędną od pierwszego";
Blockly.Msg["LISTS_GET_SUBLIST_START_FROM_END"] = "utwórz listę podrzędną z # od końca";
Blockly.Msg["LISTS_GET_SUBLIST_START_FROM_START"] = "utwórz listę podrzędną z #";
Blockly.Msg["LISTS_GET_SUBLIST_TAIL"] = "";
Blockly.Msg["LISTS_GET_SUBLIST_TOOLTIP"] = "Tworzy kopię żądanej części listy.";
Blockly.Msg["LISTS_INDEX_FROM_END_TOOLTIP"] = "%1 to ostatni element.";
Blockly.Msg["LISTS_INDEX_FROM_START_TOOLTIP"] = "%1 to pierwszy element.";
Blockly.Msg["LISTS_INDEX_OF_FIRST"] = "znajdź pierwsze wystąpienie elementu";
Blockly.Msg["LISTS_INDEX_OF_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#getting-items-from-a-list";  // untranslated
Blockly.Msg["LISTS_INDEX_OF_LAST"] = "znajdź ostatanie wystąpienie elementu";
Blockly.Msg["LISTS_INDEX_OF_TOOLTIP"] = "Zwraca indeks pierwszego/ostatniego wystąpienia elementu z listy. Zwraca %1, jeśli nie zostanie znaleziony.";
Blockly.Msg["LISTS_INLIST"] = "na liście";
Blockly.Msg["LISTS_ISEMPTY_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#is-empty";  // untranslated
Blockly.Msg["LISTS_ISEMPTY_TITLE"] = "%1 jest pusta";
Blockly.Msg["LISTS_ISEMPTY_TOOLTIP"] = "Zwraca \"prawda\" jeśli lista jest pusta.";
Blockly.Msg["LISTS_LENGTH_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#length-of";  // untranslated
Blockly.Msg["LISTS_LENGTH_TITLE"] = "długość %1";
Blockly.Msg["LISTS_LENGTH_TOOLTIP"] = "Zwraca długość listy.";
Blockly.Msg["LISTS_REPEAT_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#create-list-with";  // untranslated
Blockly.Msg["LISTS_REPEAT_TITLE"] = "utwórz listę powtarzając %1 %2 razy.";
Blockly.Msg["LISTS_REPEAT_TOOLTIP"] = "Tworzy listę zawierającą podaną wartość powtórzoną żądaną ilość razy.";
Blockly.Msg["LISTS_REVERSE_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#reversing-a-list";  // untranslated
Blockly.Msg["LISTS_REVERSE_MESSAGE0"] = "odwróć %1";
Blockly.Msg["LISTS_REVERSE_TOOLTIP"] = "Odwraca kolejność danych w kopii listy.";
Blockly.Msg["LISTS_SET_INDEX_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#in-list--set";  // untranslated
Blockly.Msg["LISTS_SET_INDEX_INPUT_TO"] = "jako";
Blockly.Msg["LISTS_SET_INDEX_INSERT"] = "wstaw w";
Blockly.Msg["LISTS_SET_INDEX_SET"] = "ustaw";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_INSERT_FIRST"] = "Wstawia element na początku listy.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_INSERT_FROM"] = "Wstawia element na żądanej pozycji listy.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_INSERT_LAST"] = "Dodaj element na koniec listy.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_INSERT_RANDOM"] = "Wstawia element w losowym miejscu na liście.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_SET_FIRST"] = "Ustawia pierwszy element na liście.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_SET_FROM"] = "Ustawia element w określonym miejscu na liście.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_SET_LAST"] = "Ustawia ostatni element na liście.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_SET_RANDOM"] = "Ustawia losowy element na liście.";
Blockly.Msg["LISTS_SORT_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#sorting-a-list";
Blockly.Msg["LISTS_SORT_ORDER_ASCENDING"] = "rosnąco";
Blockly.Msg["LISTS_SORT_ORDER_DESCENDING"] = "malejąco";
Blockly.Msg["LISTS_SORT_TITLE"] = "sortuj %1 %2 %3";
Blockly.Msg["LISTS_SORT_TOOLTIP"] = "Sortuj kopię listy.";
Blockly.Msg["LISTS_SORT_TYPE_IGNORECASE"] = "alfabetycznie, ignoruj wielkość liter";
Blockly.Msg["LISTS_SORT_TYPE_NUMERIC"] = "numerycznie";
Blockly.Msg["LISTS_SORT_TYPE_TEXT"] = "alfabetycznie";
Blockly.Msg["LISTS_SPLIT_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#splitting-strings-and-joining-lists";  // untranslated
Blockly.Msg["LISTS_SPLIT_LIST_FROM_TEXT"] = "utwórz listę z tekstu";
Blockly.Msg["LISTS_SPLIT_TEXT_FROM_LIST"] = "utwórz tekst z listy";
Blockly.Msg["LISTS_SPLIT_TOOLTIP_JOIN"] = "Łączy listę tekstów w jeden tekst, rozdzielany separatorem.";
Blockly.Msg["LISTS_SPLIT_TOOLTIP_SPLIT"] = "Rozdziela tekst zgodnie z separatorem tworząc listę z powstałych elementów.";
Blockly.Msg["LISTS_SPLIT_WITH_DELIMITER"] = "z separatorem";
Blockly.Msg["LOGIC_BOOLEAN_FALSE"] = "fałsz";
Blockly.Msg["LOGIC_BOOLEAN_HELPURL"] = "https://github.com/google/blockly/wiki/Logic#values";  // untranslated
Blockly.Msg["LOGIC_BOOLEAN_TOOLTIP"] = "Zwraca 'prawda' lub 'fałsz'.";
Blockly.Msg["LOGIC_BOOLEAN_TRUE"] = "prawda";
Blockly.Msg["LOGIC_COMPARE_HELPURL"] = "https://pl.wikipedia.org/wiki/Nierówność";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_EQ"] = "Zwraca \"prawda\", jeśli wejścia są identyczne.";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_GT"] = "Zwraca \"prawda\" jeśli pierwsze wejście jest większe od drugiego.";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_GTE"] = "Zwraca \"prawda\", jeśli pierwsze wejście jest większe lub równe drugiemu.";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_LT"] = "Zwraca \"prawda\" jeśli pierwsze wejście jest mniejsze od drugiego.";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_LTE"] = "Zwraca \"prawda\", jeśli pierwsze wejście jest mniejsze lub równe drugiemu.";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_NEQ"] = "Zwraca \"prawda\", jeśli wejścia nie są identyczne.";
Blockly.Msg["LOGIC_NEGATE_HELPURL"] = "https://github.com/google/blockly/wiki/Logic#not";  // untranslated
Blockly.Msg["LOGIC_NEGATE_TITLE"] = "nie %1";
Blockly.Msg["LOGIC_NEGATE_TOOLTIP"] = "Zwraca \"prawda\", jeśli wejściu jest \"fałsz\". Zwraca \"fałsz\", jeśli na wejściu jest \"prawda\".";
Blockly.Msg["LOGIC_NULL"] = "nic";
Blockly.Msg["LOGIC_NULL_HELPURL"] = "https://en.wikipedia.org/wiki/Nullable_type";
Blockly.Msg["LOGIC_NULL_TOOLTIP"] = "Zwraca nic.";
Blockly.Msg["LOGIC_OPERATION_AND"] = "i";
Blockly.Msg["LOGIC_OPERATION_HELPURL"] = "https://github.com/google/blockly/wiki/Logic#logical-operations";  // untranslated
Blockly.Msg["LOGIC_OPERATION_OR"] = "lub";
Blockly.Msg["LOGIC_OPERATION_TOOLTIP_AND"] = "Zwraca \"prawda\" jeśli na obydwóch wejściach jest \"prawda\".";
Blockly.Msg["LOGIC_OPERATION_TOOLTIP_OR"] = "Zwraca \"prawda\" jeśli co najmniej na jednyk wejściu jest \"prawda\".";
Blockly.Msg["LOGIC_TERNARY_CONDITION"] = "test";
Blockly.Msg["LOGIC_TERNARY_HELPURL"] = "https://en.wikipedia.org/wiki/%3F:";
Blockly.Msg["LOGIC_TERNARY_IF_FALSE"] = "jeśli fałsz";
Blockly.Msg["LOGIC_TERNARY_IF_TRUE"] = "jeśli prawda";
Blockly.Msg["LOGIC_TERNARY_TOOLTIP"] = "Sprawdź warunek w „test”. Jeśli warunek jest prawdziwy, to zwróci „jeśli prawda”; jeśli nie jest prawdziwy to zwróci „jeśli fałsz”.";
Blockly.Msg["MATH_ADDITION_SYMBOL"] = "+";
Blockly.Msg["MATH_ARITHMETIC_HELPURL"] = "https://pl.wikipedia.org/wiki/Arytmetyka";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_ADD"] = "Zwróć sumę dwóch liczb.";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_DIVIDE"] = "Zwróć iloraz dwóch liczb.";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_MINUS"] = "Zwróć różnicę dwóch liczb.";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_MULTIPLY"] = "Zwróć iloczyn dwóch liczb.";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_POWER"] = "Zwróć pierwszą liczbę podniesioną do potęgi o wykładniku drugiej liczby.";
Blockly.Msg["MATH_ATAN2_HELPURL"] = "https://en.wikipedia.org/wiki/Atan2";
Blockly.Msg["MATH_ATAN2_TITLE"] = "atan2 of X:%1 Y:%2";  // untranslated
Blockly.Msg["MATH_ATAN2_TOOLTIP"] = "Zwraca arcus tangens punktu (X, Y) w stopniach od -180 do 180.";
Blockly.Msg["MATH_CHANGE_HELPURL"] = "https://en.wikipedia.org/wiki/Programming_idiom#Incrementing_a_counter";
Blockly.Msg["MATH_CHANGE_TITLE"] = "zmień %1 o %2";
Blockly.Msg["MATH_CHANGE_TOOLTIP"] = "Dodaj liczbę do zmiennej '%1'.";
Blockly.Msg["MATH_CONSTANT_HELPURL"] = "https://pl.wikipedia.org/wiki/Stała_(matematyka)";
Blockly.Msg["MATH_CONSTANT_TOOLTIP"] = "Zwróć jedną wspólną stałą: π (3.141), e (2.718...), φ (1.618...), sqrt(2) (1.414...), sqrt(½) (0.707...) lub ∞ (nieskończoność).";
Blockly.Msg["MATH_CONSTRAIN_HELPURL"] = "https://en.wikipedia.org/wiki/Clamping_(graphics)";  // untranslated
Blockly.Msg["MATH_CONSTRAIN_TITLE"] = "ogranicz %1 z dołu %2 z góry %3";
Blockly.Msg["MATH_CONSTRAIN_TOOLTIP"] = "Ogranicz liczbę, aby była w określonych granicach (włącznie).";
Blockly.Msg["MATH_DIVISION_SYMBOL"] = "/";
Blockly.Msg["MATH_IS_DIVISIBLE_BY"] = "jest podzielna przez";
Blockly.Msg["MATH_IS_EVEN"] = "jest parzysta";
Blockly.Msg["MATH_IS_NEGATIVE"] = "jest ujemna";
Blockly.Msg["MATH_IS_ODD"] = "jest nieparzysta";
Blockly.Msg["MATH_IS_POSITIVE"] = "jest dodatnia";
Blockly.Msg["MATH_IS_PRIME"] = "jest liczbą pierwszą";
Blockly.Msg["MATH_IS_TOOLTIP"] = "Sprawdź, czy liczba jest parzysta, nieparzysta, pierwsza, całkowita, dodatnia, ujemna, lub czy jest podzielna przez podaną liczbę. Zwraca wartość \"prawda\" lub \"fałsz\".";
Blockly.Msg["MATH_IS_WHOLE"] = "jest liczbą całkowitą";
Blockly.Msg["MATH_MODULO_HELPURL"] = "https://pl.wikipedia.org/wiki/Modulo";
Blockly.Msg["MATH_MODULO_TITLE"] = "reszta z dzielenia %1 przez %2";
Blockly.Msg["MATH_MODULO_TOOLTIP"] = "Zwróć resztę z dzielenia dwóch liczb przez siebie.";
Blockly.Msg["MATH_MULTIPLICATION_SYMBOL"] = "×";
Blockly.Msg["MATH_NUMBER_HELPURL"] = "https://en.wikipedia.org/wiki/Number";
Blockly.Msg["MATH_NUMBER_TOOLTIP"] = "Liczba.";
Blockly.Msg["MATH_ONLIST_HELPURL"] = "";
Blockly.Msg["MATH_ONLIST_OPERATOR_AVERAGE"] = "średnia elementów listy";
Blockly.Msg["MATH_ONLIST_OPERATOR_MAX"] = "maksymalna wartość z listy";
Blockly.Msg["MATH_ONLIST_OPERATOR_MEDIAN"] = "mediana listy";
Blockly.Msg["MATH_ONLIST_OPERATOR_MIN"] = "minimalna wartość z listy";
Blockly.Msg["MATH_ONLIST_OPERATOR_MODE"] = "dominanty listy";
Blockly.Msg["MATH_ONLIST_OPERATOR_RANDOM"] = "losowy element z listy";
Blockly.Msg["MATH_ONLIST_OPERATOR_STD_DEV"] = "odchylenie standardowe listy";
Blockly.Msg["MATH_ONLIST_OPERATOR_SUM"] = "suma elementów listy";
Blockly.Msg["MATH_ONLIST_TOOLTIP_AVERAGE"] = "Zwróć średnią (średnią arytmetyczną) wartości liczbowych z listy.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_MAX"] = "Zwróć największą liczbę w liście.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_MEDIAN"] = "Zwróć medianę listy.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_MIN"] = "Zwróć najmniejszą liczbę w liście.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_MODE"] = "Zwróć listę najczęściej występujących elementów w liście.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_RANDOM"] = "Zwróć losowy element z listy.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_STD_DEV"] = "Zwróć odchylenie standardowe listy.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_SUM"] = "Zwróć sumę wszystkich liczb z listy.";
Blockly.Msg["MATH_POWER_SYMBOL"] = "^";
Blockly.Msg["MATH_RANDOM_FLOAT_HELPURL"] = "https://en.wikipedia.org/wiki/Random_number_generation";
Blockly.Msg["MATH_RANDOM_FLOAT_TITLE_RANDOM"] = "losowy ułamek";
Blockly.Msg["MATH_RANDOM_FLOAT_TOOLTIP"] = "Zwróć losowy ułamek między 0.0 (włącznie), a 1.0 (wyłącznie).";
Blockly.Msg["MATH_RANDOM_INT_HELPURL"] = "https://en.wikipedia.org/wiki/Random_number_generation";
Blockly.Msg["MATH_RANDOM_INT_TITLE"] = "losowa liczba całkowita od %1 do %2";
Blockly.Msg["MATH_RANDOM_INT_TOOLTIP"] = "Zwróć losową liczbę całkowitą w ramach dwóch wyznaczonych granic, włącznie.";
Blockly.Msg["MATH_ROUND_HELPURL"] = "https://pl.wikipedia.org/wiki/Zaokrąglanie";
Blockly.Msg["MATH_ROUND_OPERATOR_ROUND"] = "zaokrąglij";
Blockly.Msg["MATH_ROUND_OPERATOR_ROUNDDOWN"] = "zaokrąglij w dół";
Blockly.Msg["MATH_ROUND_OPERATOR_ROUNDUP"] = "zaokrąglij w górę";
Blockly.Msg["MATH_ROUND_TOOLTIP"] = "Zaokrąglij w górę lub w dół.";
Blockly.Msg["MATH_SINGLE_HELPURL"] = "https://pl.wikipedia.org/wiki/Pierwiastek_kwadratowy";
Blockly.Msg["MATH_SINGLE_OP_ABSOLUTE"] = "wartość bezwzględna";
Blockly.Msg["MATH_SINGLE_OP_ROOT"] = "pierwiastek kwadratowy";
Blockly.Msg["MATH_SINGLE_TOOLTIP_ABS"] = "Zwróć wartość bezwzględną danej liczby.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_EXP"] = "Zwróć e do potęgi danej liczby.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_LN"] = "Zwróć logarytm naturalny danej liczby.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_LOG10"] = "Zwraca logarytm dziesiętny danej liczby.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_NEG"] = "Zwróć negację danej liczby.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_POW10"] = "Zwróć 10 do potęgi danej liczby.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_ROOT"] = "Zwróć pierwiastek kwadratowy danej liczby.";
Blockly.Msg["MATH_SUBTRACTION_SYMBOL"] = "-";
Blockly.Msg["MATH_TRIG_ACOS"] = "arccos";
Blockly.Msg["MATH_TRIG_ASIN"] = "arcsin";
Blockly.Msg["MATH_TRIG_ATAN"] = "arctg";
Blockly.Msg["MATH_TRIG_COS"] = "cos";
Blockly.Msg["MATH_TRIG_HELPURL"] = "https://pl.wikipedia.org/wiki/Funkcje_trygonometryczne";
Blockly.Msg["MATH_TRIG_SIN"] = "sin";
Blockly.Msg["MATH_TRIG_TAN"] = "tg";
Blockly.Msg["MATH_TRIG_TOOLTIP_ACOS"] = "Zwróć arcus cosinus danej liczby.";
Blockly.Msg["MATH_TRIG_TOOLTIP_ASIN"] = "Zwróć arcus sinus danej liczby.";
Blockly.Msg["MATH_TRIG_TOOLTIP_ATAN"] = "Zwróć arcus tangens danej liczby.";
Blockly.Msg["MATH_TRIG_TOOLTIP_COS"] = "Zwróć wartość cosinusa o stopniu (nie w radianach).";
Blockly.Msg["MATH_TRIG_TOOLTIP_SIN"] = "Zwróć wartość sinusa o stopniu (nie w radianach).";
Blockly.Msg["MATH_TRIG_TOOLTIP_TAN"] = "Zwróć tangens o stopniu (nie w radianach).";
Blockly.Msg["NEW_COLOUR_VARIABLE"] = "Utwórz zmienną colour";
Blockly.Msg["NEW_NUMBER_VARIABLE"] = "Utwórz zmienną typu number";
Blockly.Msg["NEW_STRING_VARIABLE"] = "Utwórz zmienną typu string";
Blockly.Msg["NEW_VARIABLE"] = "Utwórz zmienną...";
Blockly.Msg["NEW_VARIABLE_TITLE"] = "Nowa nazwa zmiennej:";
Blockly.Msg["NEW_VARIABLE_TYPE_TITLE"] = "Nowy typ zmiennej:";
Blockly.Msg["ORDINAL_NUMBER_SUFFIX"] = "";
Blockly.Msg["PROCEDURES_ALLOW_STATEMENTS"] = "zezwól na czynności";
Blockly.Msg["PROCEDURES_BEFORE_PARAMS"] = "z:";
Blockly.Msg["PROCEDURES_CALLNORETURN_HELPURL"] = "https://pl.wikipedia.org/wiki/Podprogram";
Blockly.Msg["PROCEDURES_CALLNORETURN_TOOLTIP"] = "Uruchom zdefiniowaną przez użytkownika funkcję '%1'.";
Blockly.Msg["PROCEDURES_CALLRETURN_HELPURL"] = "https://pl.wikipedia.org/wiki/Podprogram";
Blockly.Msg["PROCEDURES_CALLRETURN_TOOLTIP"] = "Uruchom zdefiniowaną przez użytkownika funkcję '%1' i użyj jej wyjścia.";
Blockly.Msg["PROCEDURES_CALL_BEFORE_PARAMS"] = "z:";
Blockly.Msg["PROCEDURES_CREATE_DO"] = "Utwórz '%1'";
Blockly.Msg["PROCEDURES_DEFNORETURN_COMMENT"] = "Opisz tę funkcję...";
Blockly.Msg["PROCEDURES_DEFNORETURN_DO"] = "";
Blockly.Msg["PROCEDURES_DEFNORETURN_HELPURL"] = "https://en.wikipedia.org/wiki/Procedure_%28computer_science%29";
Blockly.Msg["PROCEDURES_DEFNORETURN_PROCEDURE"] = "zrób coś";
Blockly.Msg["PROCEDURES_DEFNORETURN_TITLE"] = "do";
Blockly.Msg["PROCEDURES_DEFNORETURN_TOOLTIP"] = "Tworzy funkcję nie posiadającą wyjścia.";
Blockly.Msg["PROCEDURES_DEFRETURN_HELPURL"] = "https://en.wikipedia.org/wiki/Procedure_%28computer_science%29";
Blockly.Msg["PROCEDURES_DEFRETURN_RETURN"] = "zwróć";
Blockly.Msg["PROCEDURES_DEFRETURN_TOOLTIP"] = "Tworzy funkcję posiadającą wyjście.";
Blockly.Msg["PROCEDURES_DEF_DUPLICATE_WARNING"] = "Uwaga: Ta funkcja ma powtórzone parametry.";
Blockly.Msg["PROCEDURES_HIGHLIGHT_DEF"] = "Podświetl definicję funkcji";
Blockly.Msg["PROCEDURES_IFRETURN_HELPURL"] = "http://c2.com/cgi/wiki?GuardClause";  // untranslated
Blockly.Msg["PROCEDURES_IFRETURN_TOOLTIP"] = "Jeśli warunek jest spełniony zwróć drugą wartość.";
Blockly.Msg["PROCEDURES_IFRETURN_WARNING"] = "Uwaga: Ten klocek może być używany tylko w definicji funkcji.";
Blockly.Msg["PROCEDURES_MUTATORARG_TITLE"] = "nazwa wejścia:";
Blockly.Msg["PROCEDURES_MUTATORARG_TOOLTIP"] = "Dodaj wejście do funkcji.";
Blockly.Msg["PROCEDURES_MUTATORCONTAINER_TITLE"] = "wejścia";
Blockly.Msg["PROCEDURES_MUTATORCONTAINER_TOOLTIP"] = "Dodaj, usuń lub zmień kolejność wejść tej funkcji.";
Blockly.Msg["REDO"] = "Ponów";
Blockly.Msg["REMOVE_COMMENT"] = "Usuń komentarz";
Blockly.Msg["RENAME_VARIABLE"] = "Zmień nazwę zmiennej...";
Blockly.Msg["RENAME_VARIABLE_TITLE"] = "Zmień nazwy wszystkich '%1' zmiennych na:";
Blockly.Msg["TEXT_APPEND_HELPURL"] = "https://github.com/google/blockly/wiki/Text#text-modification";  // untranslated
Blockly.Msg["TEXT_APPEND_TITLE"] = "dodaj tekst %2 do %1";
Blockly.Msg["TEXT_APPEND_TOOLTIP"] = "Dołącz tekst do zmiennej '%1'.";
Blockly.Msg["TEXT_CHANGECASE_HELPURL"] = "https://github.com/google/blockly/wiki/Text#adjusting-text-case";  // untranslated
Blockly.Msg["TEXT_CHANGECASE_OPERATOR_LOWERCASE"] = "na małe litery";
Blockly.Msg["TEXT_CHANGECASE_OPERATOR_TITLECASE"] = "na Pierwsza Duża";
Blockly.Msg["TEXT_CHANGECASE_OPERATOR_UPPERCASE"] = "na WIELKIE LITERY";
Blockly.Msg["TEXT_CHANGECASE_TOOLTIP"] = "Zwraca kopię tekstu z odwruconą wielkością liter.";
Blockly.Msg["TEXT_CHARAT_FIRST"] = "pobierz pierwszą literę";
Blockly.Msg["TEXT_CHARAT_FROM_END"] = "pobierz literę # od końca";
Blockly.Msg["TEXT_CHARAT_FROM_START"] = "pobierz literę #";
Blockly.Msg["TEXT_CHARAT_HELPURL"] = "https://github.com/google/blockly/wiki/Text#extracting-text";  // untranslated
Blockly.Msg["TEXT_CHARAT_LAST"] = "pobierz ostatnią literę";
Blockly.Msg["TEXT_CHARAT_RANDOM"] = "pobierz losową literę";
Blockly.Msg["TEXT_CHARAT_TAIL"] = "";
Blockly.Msg["TEXT_CHARAT_TITLE"] = "w tekście %1 %2";
Blockly.Msg["TEXT_CHARAT_TOOLTIP"] = "Zwraca literę z określonej pozycji.";
Blockly.Msg["TEXT_COUNT_HELPURL"] = "https://github.com/google/blockly/wiki/Text#counting-substrings";
Blockly.Msg["TEXT_COUNT_MESSAGE0"] = "policz %1 w %2";
Blockly.Msg["TEXT_COUNT_TOOLTIP"] = "Liczy ile razy dany tekst występuje w innym tekście.";
Blockly.Msg["TEXT_CREATE_JOIN_ITEM_TOOLTIP"] = "Dodaj element do tekstu.";
Blockly.Msg["TEXT_CREATE_JOIN_TITLE_JOIN"] = "połącz";
Blockly.Msg["TEXT_CREATE_JOIN_TOOLTIP"] = "Dodaj, usuń lub zmień kolejność sekcji, aby zmodyfikować klocek tekstowy.";
Blockly.Msg["TEXT_GET_SUBSTRING_END_FROM_END"] = "do # litery od końca";
Blockly.Msg["TEXT_GET_SUBSTRING_END_FROM_START"] = "do # litery";
Blockly.Msg["TEXT_GET_SUBSTRING_END_LAST"] = "do ostatniej litery";
Blockly.Msg["TEXT_GET_SUBSTRING_HELPURL"] = "https://github.com/google/blockly/wiki/Text#extracting-a-region-of-text";  // untranslated
Blockly.Msg["TEXT_GET_SUBSTRING_INPUT_IN_TEXT"] = "w tekście";
Blockly.Msg["TEXT_GET_SUBSTRING_START_FIRST"] = "pobierz podciąg od pierwszej litery";
Blockly.Msg["TEXT_GET_SUBSTRING_START_FROM_END"] = "pobierz podciąg od # litery od końca";
Blockly.Msg["TEXT_GET_SUBSTRING_START_FROM_START"] = "pobierz podciąg od # litery";
Blockly.Msg["TEXT_GET_SUBSTRING_TAIL"] = "";
Blockly.Msg["TEXT_GET_SUBSTRING_TOOLTIP"] = "Zwraca określoną część tekstu.";
Blockly.Msg["TEXT_INDEXOF_HELPURL"] = "https://github.com/google/blockly/wiki/Text#finding-text";  // untranslated
Blockly.Msg["TEXT_INDEXOF_OPERATOR_FIRST"] = "znajdź pierwsze wystąpienie tekstu";
Blockly.Msg["TEXT_INDEXOF_OPERATOR_LAST"] = "znajdź ostatnie wystąpienie tekstu";
Blockly.Msg["TEXT_INDEXOF_TITLE"] = "w tekście %1 %2 %3";
Blockly.Msg["TEXT_INDEXOF_TOOLTIP"] = "Zwraca indeks pierwszego/ostatniego wystąpienia pierwszego tekstu w drugim tekście. Zwraca wartość %1, jeśli tekst nie został znaleziony.";
Blockly.Msg["TEXT_ISEMPTY_HELPURL"] = "https://github.com/google/blockly/wiki/Text#checking-for-empty-text";  // untranslated
Blockly.Msg["TEXT_ISEMPTY_TITLE"] = "%1 jest pusty";
Blockly.Msg["TEXT_ISEMPTY_TOOLTIP"] = "Zwraca prawda (true), jeśli podany tekst jest pusty.";
Blockly.Msg["TEXT_JOIN_HELPURL"] = "https://github.com/google/blockly/wiki/Text#text-creation";  // untranslated
Blockly.Msg["TEXT_JOIN_TITLE_CREATEWITH"] = "utwórz tekst z";
Blockly.Msg["TEXT_JOIN_TOOLTIP"] = "Tworzy fragment tekstu, łącząc ze sobą dowolną liczbę tekstów.";
Blockly.Msg["TEXT_LENGTH_HELPURL"] = "https://github.com/google/blockly/wiki/Text#text-modification";  // untranslated
Blockly.Msg["TEXT_LENGTH_TITLE"] = "długość %1";
Blockly.Msg["TEXT_LENGTH_TOOLTIP"] = "Zwraca liczbę liter (łącznie ze spacjami) w podanym tekście.";
Blockly.Msg["TEXT_PRINT_HELPURL"] = "https://github.com/google/blockly/wiki/Text#printing-text";  // untranslated
Blockly.Msg["TEXT_PRINT_TITLE"] = "wydrukuj %1";
Blockly.Msg["TEXT_PRINT_TOOLTIP"] = "Wyświetl określony tekst, liczbę lub inną wartość.";
Blockly.Msg["TEXT_PROMPT_HELPURL"] = "https://github.com/google/blockly/wiki/Text#getting-input-from-the-user";  // untranslated
Blockly.Msg["TEXT_PROMPT_TOOLTIP_NUMBER"] = "Zapytaj użytkownika  o liczbę.";
Blockly.Msg["TEXT_PROMPT_TOOLTIP_TEXT"] = "Zapytaj użytkownika o jakiś tekst.";
Blockly.Msg["TEXT_PROMPT_TYPE_NUMBER"] = "poproś o liczbę z tą wiadomością";
Blockly.Msg["TEXT_PROMPT_TYPE_TEXT"] = "poproś o tekst z tą wiadomością";
Blockly.Msg["TEXT_REPLACE_HELPURL"] = "https://github.com/google/blockly/wiki/Text#replacing-substrings";  // untranslated
Blockly.Msg["TEXT_REPLACE_MESSAGE0"] = "zamień %1 na %2 w %3";
Blockly.Msg["TEXT_REPLACE_TOOLTIP"] = "Zastąp wszystkie wystąpienia danego tekstu innym.";
Blockly.Msg["TEXT_REVERSE_HELPURL"] = "https://github.com/google/blockly/wiki/Text#reversing-text";
Blockly.Msg["TEXT_REVERSE_MESSAGE0"] = "odwróć %1";
Blockly.Msg["TEXT_REVERSE_TOOLTIP"] = "Odwraca kolejność znaków w tekście.";
Blockly.Msg["TEXT_TEXT_HELPURL"] = "https://pl.wikipedia.org/wiki/Tekstowy_typ_danych";
Blockly.Msg["TEXT_TEXT_TOOLTIP"] = "Litera, wyraz lub linia tekstu.";
Blockly.Msg["TEXT_TRIM_HELPURL"] = "https://github.com/google/blockly/wiki/Text#trimming-removing-spaces";  // untranslated
Blockly.Msg["TEXT_TRIM_OPERATOR_BOTH"] = "usuń spacje po obu stronach";
Blockly.Msg["TEXT_TRIM_OPERATOR_LEFT"] = "usuń spacje z lewej strony";
Blockly.Msg["TEXT_TRIM_OPERATOR_RIGHT"] = "usuń spacje z prawej strony";
Blockly.Msg["TEXT_TRIM_TOOLTIP"] = "Zwraca kopię tekstu z usuniętymi spacjami z jednego lub z obu końców tekstu.";
Blockly.Msg["TODAY"] = "Dzisiaj";
Blockly.Msg["UNDO"] = "Cofnij";
Blockly.Msg["UNNAMED_KEY"] = "bez nazwy";
Blockly.Msg["VARIABLES_DEFAULT_NAME"] = "element";
Blockly.Msg["VARIABLES_GET_CREATE_SET"] = "Utwórz klocek 'ustaw %1'";
Blockly.Msg["VARIABLES_GET_HELPURL"] = "https://github.com/google/blockly/wiki/Variables#get";  // untranslated
Blockly.Msg["VARIABLES_GET_TOOLTIP"] = "Zwraca wartość tej zmiennej.";
Blockly.Msg["VARIABLES_SET"] = "przypisz %1 wartość %2";
Blockly.Msg["VARIABLES_SET_CREATE_GET"] = "Utwórz klocek 'pobierz %1'";
Blockly.Msg["VARIABLES_SET_HELPURL"] = "https://github.com/google/blockly/wiki/Variables#set";  // untranslated
Blockly.Msg["VARIABLES_SET_TOOLTIP"] = "Wartości zmiennej i  wejście będą identyczne.";
Blockly.Msg["VARIABLE_ALREADY_EXISTS"] = "Zmienna o nazwie '%1' już istnieje.";
Blockly.Msg["VARIABLE_ALREADY_EXISTS_FOR_ANOTHER_TYPE"] = "Zmienna o nazwie '%1' już istnieje i jest typu '%2'.";
Blockly.Msg["WORKSPACE_ARIA_LABEL"] = "Blockly Workspace";  // untranslated
Blockly.Msg["WORKSPACE_COMMENT_DEFAULT_TEXT"] = "Powiedz coś...";
Blockly.Msg["CONTROLS_FOREACH_INPUT_DO"] = Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"];
Blockly.Msg["CONTROLS_FOR_INPUT_DO"] = Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"];
Blockly.Msg["CONTROLS_IF_ELSEIF_TITLE_ELSEIF"] = Blockly.Msg["CONTROLS_IF_MSG_ELSEIF"];
Blockly.Msg["CONTROLS_IF_ELSE_TITLE_ELSE"] = Blockly.Msg["CONTROLS_IF_MSG_ELSE"];
Blockly.Msg["CONTROLS_IF_IF_TITLE_IF"] = Blockly.Msg["CONTROLS_IF_MSG_IF"];
Blockly.Msg["CONTROLS_IF_MSG_THEN"] = Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"];
Blockly.Msg["CONTROLS_WHILEUNTIL_INPUT_DO"] = Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"];
Blockly.Msg["LISTS_CREATE_WITH_ITEM_TITLE"] = Blockly.Msg["VARIABLES_DEFAULT_NAME"];
Blockly.Msg["LISTS_GET_INDEX_HELPURL"] = Blockly.Msg["LISTS_INDEX_OF_HELPURL"];
Blockly.Msg["LISTS_GET_INDEX_INPUT_IN_LIST"] = Blockly.Msg["LISTS_INLIST"];
Blockly.Msg["LISTS_GET_SUBLIST_INPUT_IN_LIST"] = Blockly.Msg["LISTS_INLIST"];
Blockly.Msg["LISTS_INDEX_OF_INPUT_IN_LIST"] = Blockly.Msg["LISTS_INLIST"];
Blockly.Msg["LISTS_SET_INDEX_INPUT_IN_LIST"] = Blockly.Msg["LISTS_INLIST"];
Blockly.Msg["MATH_CHANGE_TITLE_ITEM"] = Blockly.Msg["VARIABLES_DEFAULT_NAME"];
Blockly.Msg["PROCEDURES_DEFRETURN_COMMENT"] = Blockly.Msg["PROCEDURES_DEFNORETURN_COMMENT"];
Blockly.Msg["PROCEDURES_DEFRETURN_DO"] = Blockly.Msg["PROCEDURES_DEFNORETURN_DO"];
Blockly.Msg["PROCEDURES_DEFRETURN_PROCEDURE"] = Blockly.Msg["PROCEDURES_DEFNORETURN_PROCEDURE"];
Blockly.Msg["PROCEDURES_DEFRETURN_TITLE"] = Blockly.Msg["PROCEDURES_DEFNORETURN_TITLE"];
Blockly.Msg["TEXT_APPEND_VARIABLE"] = Blockly.Msg["VARIABLES_DEFAULT_NAME"];
Blockly.Msg["TEXT_CREATE_JOIN_ITEM_TITLE_ITEM"] = Blockly.Msg["VARIABLES_DEFAULT_NAME"];

Blockly.Msg["MATH_HUE"] = "230";
Blockly.Msg["LOOPS_HUE"] = "120";
Blockly.Msg["LISTS_HUE"] = "260";
Blockly.Msg["LOGIC_HUE"] = "210";
Blockly.Msg["VARIABLES_HUE"] = "330";
Blockly.Msg["TEXTS_HUE"] = "160";
Blockly.Msg["PROCEDURES_HUE"] = "290";
Blockly.Msg["COLOUR_HUE"] = "20";
Blockly.Msg["VARIABLES_DYNAMIC_HUE"] = "310";