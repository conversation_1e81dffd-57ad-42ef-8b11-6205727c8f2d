// This file was automatically generated.  Do not modify.

'use strict';

Blockly.Msg["ADD_COMMENT"] = "Gionté un coment";
Blockly.Msg["CANNOT_DELETE_VARIABLE_PROCEDURE"] = "As peul nen eliminesse la variàbil '%1' përchè a l'é part ëd la definission dla fonsion '%2'";
Blockly.Msg["CHANGE_VALUE_TITLE"] = "Modifiché ël valor:";
Blockly.Msg["CLEAN_UP"] = "Dëscancelé ij blòch";
Blockly.Msg["COLLAPSED_WARNINGS_WARNING"] = "Ij blòch sarà a conten-o dj'avertense.";
Blockly.Msg["COLLAPSE_ALL"] = "Arduve ij blòch";
Blockly.Msg["COLLAPSE_BLOCK"] = "Arduve ël blòch";
Blockly.Msg["COLOUR_BLEND_COLOUR1"] = "color 1";
Blockly.Msg["COLOUR_BLEND_COLOUR2"] = "color 2";
Blockly.Msg["COLOUR_BLEND_HELPURL"] = "https://meyerweb.com/eric/tools/color-blend/#:::rgbp";  // untranslated
Blockly.Msg["COLOUR_BLEND_RATIO"] = "rapòrt";
Blockly.Msg["COLOUR_BLEND_TITLE"] = "mës-cé";
Blockly.Msg["COLOUR_BLEND_TOOLTIP"] = "A mës-cia doi color ansema con un rapòrt dàit (0,0 - 1,0).";
Blockly.Msg["COLOUR_PICKER_HELPURL"] = "https://en.wikipedia.org/wiki/Color";
Blockly.Msg["COLOUR_PICKER_TOOLTIP"] = "Serne un color ant la taulòssa.";
Blockly.Msg["COLOUR_RANDOM_HELPURL"] = "http://randomcolour.com";  // untranslated
Blockly.Msg["COLOUR_RANDOM_TITLE"] = "color a asar";
Blockly.Msg["COLOUR_RANDOM_TOOLTIP"] = "Serne un color a asar.";
Blockly.Msg["COLOUR_RGB_BLUE"] = "bleu";
Blockly.Msg["COLOUR_RGB_GREEN"] = "verd";
Blockly.Msg["COLOUR_RGB_HELPURL"] = "https://www.december.com/html/spec/colorpercompact.html";  // untranslated
Blockly.Msg["COLOUR_RGB_RED"] = "ross";
Blockly.Msg["COLOUR_RGB_TITLE"] = "coloré con";
Blockly.Msg["COLOUR_RGB_TOOLTIP"] = "Creé un color con la quantità spessificà ëd ross, verd e bleu. Tuti ij valor a devo esse antra 0 e 100.";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_HELPURL"] = "https://github.com/google/blockly/wiki/Loops#loop-termination-blocks";  // untranslated
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_OPERATOR_BREAK"] = "seurte da la liassa";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_OPERATOR_CONTINUE"] = "continué con l'iterassion sucessiva dla liassa";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_TOOLTIP_BREAK"] = "Seurte da la liassa anglobanta.";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_TOOLTIP_CONTINUE"] = "Sauté ël rest ëd sa liassa, e continué con l'iterassion apress.";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_WARNING"] = "Atension: Ës blòch a peul mach esse dovrà andrinta a na liassa.";
Blockly.Msg["CONTROLS_FOREACH_HELPURL"] = "https://github.com/google/blockly/wiki/Loops#for-each";  // untranslated
Blockly.Msg["CONTROLS_FOREACH_TITLE"] = "për minca n'element %1 ant la lista %2";
Blockly.Msg["CONTROLS_FOREACH_TOOLTIP"] = "Për minca element an na lista, dé ël valor ëd l'element a la variàbil '%1', peui eseguì chèiche anstrussion.";
Blockly.Msg["CONTROLS_FOR_HELPURL"] = "https://github.com/google/blockly/wiki/Loops#count-with";  // untranslated
Blockly.Msg["CONTROLS_FOR_TITLE"] = "conté con %1 da %2 a %3 për %4";
Blockly.Msg["CONTROLS_FOR_TOOLTIP"] = "Fé an manera che la variàbil \"%1\" a pija ij valor dal nùmer inissial fin-a al nùmer final, an contand për l'antërval ëspessificà, e eseguì ij bloch ëspessificà.";
Blockly.Msg["CONTROLS_IF_ELSEIF_TOOLTIP"] = "Gionté na condission al blòch si.";
Blockly.Msg["CONTROLS_IF_ELSE_TOOLTIP"] = "Gionté na condission final ch'a cheuj tut al blòch si.";
Blockly.Msg["CONTROLS_IF_HELPURL"] = "https://github.com/google/blockly/wiki/IfElse";  // untranslated
Blockly.Msg["CONTROLS_IF_IF_TOOLTIP"] = "Gionté, gavé o riordiné le session për cinfiguré torna ës blòch si.";
Blockly.Msg["CONTROLS_IF_MSG_ELSE"] = "dësnò";
Blockly.Msg["CONTROLS_IF_MSG_ELSEIF"] = "dësnò si";
Blockly.Msg["CONTROLS_IF_MSG_IF"] = "si";
Blockly.Msg["CONTROLS_IF_TOOLTIP_1"] = "Si un valor a l'é ver, antlora eseguì chèiche anstrussion.";
Blockly.Msg["CONTROLS_IF_TOOLTIP_2"] = "Si un valor a l'é ver, antlora eseguì ël prim blòch d'anstrussion. Dësnò, eseguì ël second blòch d'anstrussion.";
Blockly.Msg["CONTROLS_IF_TOOLTIP_3"] = "Si ël prim valor a l'é ver, antlora fé andé ël prim blòch d'anstrussion. Dësnò, si ël second valor a l'é ver, fé andé ël second blòch d'anstrussion.";
Blockly.Msg["CONTROLS_IF_TOOLTIP_4"] = "Si ël prim valor a l'é ver, antlora fé andé ël prim blòch d'anstrussion. Dësnò, si ël second valor a l'é ver, fé andé ël second blòcj d'anstrussion. Si gnun dij valor a l'é ver, fé andé l'ùltim blòch d'anstrussion.";
Blockly.Msg["CONTROLS_REPEAT_HELPURL"] = "https://en.wikipedia.org/wiki/For_loop";
Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"] = "fé";
Blockly.Msg["CONTROLS_REPEAT_TITLE"] = "arpete %1 vire";
Blockly.Msg["CONTROLS_REPEAT_TOOLTIP"] = "Eseguì chèiche anstrussion vàire vire.";
Blockly.Msg["CONTROLS_WHILEUNTIL_HELPURL"] = "https://github.com/google/blockly/wiki/Loops#repeat";  // untranslated
Blockly.Msg["CONTROLS_WHILEUNTIL_OPERATOR_UNTIL"] = "arpete fin-a a";
Blockly.Msg["CONTROLS_WHILEUNTIL_OPERATOR_WHILE"] = "arpete antramentre che";
Blockly.Msg["CONTROLS_WHILEUNTIL_TOOLTIP_UNTIL"] = "Cand un valor a l'é fàuss, eseguì chèiche anstrussion.";
Blockly.Msg["CONTROLS_WHILEUNTIL_TOOLTIP_WHILE"] = "Cand un valor a l'é ver, eseguì chèiche anstrussion.";
Blockly.Msg["DELETE_ALL_BLOCKS"] = "Scancelé tuti ij %1 blòch?";
Blockly.Msg["DELETE_BLOCK"] = "Scancelé ël blòch";
Blockly.Msg["DELETE_VARIABLE"] = "Eliminé la variàbil '%1'";
Blockly.Msg["DELETE_VARIABLE_CONFIRMATION"] = "Eliminé %1 utilisassion ëd la variàbil '%2'?";
Blockly.Msg["DELETE_X_BLOCKS"] = "Scancelé %1 blòch";
Blockly.Msg["DIALOG_CANCEL"] = "Anulé";
Blockly.Msg["DIALOG_OK"] = "Va bin";
Blockly.Msg["DISABLE_BLOCK"] = "Disativé ël blòch";
Blockly.Msg["DUPLICATE_BLOCK"] = "Dupliché";
Blockly.Msg["DUPLICATE_COMMENT"] = "Dupliché ël coment";
Blockly.Msg["ENABLE_BLOCK"] = "Ativé ël blòch";
Blockly.Msg["EXPAND_ALL"] = "Dësvlupé ij blòch";
Blockly.Msg["EXPAND_BLOCK"] = "Dësvlupé ël blòch";
Blockly.Msg["EXTERNAL_INPUTS"] = "Imission esterne";
Blockly.Msg["HELP"] = "Agiut";
Blockly.Msg["INLINE_INPUTS"] = "Imission an linia";
Blockly.Msg["LISTS_CREATE_EMPTY_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#create-empty-list";  // untranslated
Blockly.Msg["LISTS_CREATE_EMPTY_TITLE"] = "creé na lista veuida";
Blockly.Msg["LISTS_CREATE_EMPTY_TOOLTIP"] = "Smon-e na lista, ëd longheur 0, ch'a conten gnun-a argistrassion";
Blockly.Msg["LISTS_CREATE_WITH_CONTAINER_TITLE_ADD"] = "lista";
Blockly.Msg["LISTS_CREATE_WITH_CONTAINER_TOOLTIP"] = "Gionté, gavé o riordiné le session për configuré torna cost blòch ëd lista.";
Blockly.Msg["LISTS_CREATE_WITH_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#create-list-with";
Blockly.Msg["LISTS_CREATE_WITH_INPUT_WITH"] = "creé na lista con";
Blockly.Msg["LISTS_CREATE_WITH_ITEM_TOOLTIP"] = "Gionté n'element a la lista.";
Blockly.Msg["LISTS_CREATE_WITH_TOOLTIP"] = "Creé na lista con un nùmer qualsëssìa d'element.";
Blockly.Msg["LISTS_GET_INDEX_FIRST"] = "prim";
Blockly.Msg["LISTS_GET_INDEX_FROM_END"] = "# da la fin";
Blockly.Msg["LISTS_GET_INDEX_FROM_START"] = "#";  // untranslated
Blockly.Msg["LISTS_GET_INDEX_GET"] = "oten-e";
Blockly.Msg["LISTS_GET_INDEX_GET_REMOVE"] = "oten-e e eliminé";
Blockly.Msg["LISTS_GET_INDEX_LAST"] = "ùltim";
Blockly.Msg["LISTS_GET_INDEX_RANDOM"] = "a l'ancàpit";
Blockly.Msg["LISTS_GET_INDEX_REMOVE"] = "eliminé";
Blockly.Msg["LISTS_GET_INDEX_TAIL"] = "";  // untranslated
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_FIRST"] = "A smon ël prim element an na lista.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_FROM"] = "A smon l'element a la posission ëspessificà an na lista.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_LAST"] = "A smon l'ùltim element an na lista.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_RANDOM"] = "A smon n'element a l'ancàpit an na lista.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_FIRST"] = "A gava e a smon ël prim element an na lista.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_FROM"] = "A gava e a smon l'element a la posission ëspessificà an na lista.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_LAST"] = "A gava e a smon l'ùltim element an na lista.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_RANDOM"] = "A gava e a smon n'element a l'ancàpit an na lista.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_REMOVE_FIRST"] = "A gava ël prim element an na lista.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_REMOVE_FROM"] = "A gava l'element a la posission ëspessificà an na lista.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_REMOVE_LAST"] = "A gava l'ùltim element an na lista.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_REMOVE_RANDOM"] = "A gava n'element a l'ancàpit da na lista.";
Blockly.Msg["LISTS_GET_SUBLIST_END_FROM_END"] = "fin-a a # da la fin";
Blockly.Msg["LISTS_GET_SUBLIST_END_FROM_START"] = "fin-a a #";
Blockly.Msg["LISTS_GET_SUBLIST_END_LAST"] = "fin-a a l'ùltim";
Blockly.Msg["LISTS_GET_SUBLIST_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#getting-a-sublist";  // untranslated
Blockly.Msg["LISTS_GET_SUBLIST_START_FIRST"] = "oten-e la sot-lista dal prim";
Blockly.Msg["LISTS_GET_SUBLIST_START_FROM_END"] = "oten-e la sot-lista da # da la fin";
Blockly.Msg["LISTS_GET_SUBLIST_START_FROM_START"] = "oten-e la sot-lista da #";
Blockly.Msg["LISTS_GET_SUBLIST_TAIL"] = "";  // untranslated
Blockly.Msg["LISTS_GET_SUBLIST_TOOLTIP"] = "A crea na còpia dël tòch ëspessificà ëd na lista.";
Blockly.Msg["LISTS_INDEX_FROM_END_TOOLTIP"] = "%1 a l'é l'ùltim element.";
Blockly.Msg["LISTS_INDEX_FROM_START_TOOLTIP"] = "%1 a l'é ël prim element.";
Blockly.Msg["LISTS_INDEX_OF_FIRST"] = "trové la prima ocorensa dl'element";
Blockly.Msg["LISTS_INDEX_OF_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#getting-items-from-a-list";  // untranslated
Blockly.Msg["LISTS_INDEX_OF_LAST"] = "trové l'ùltima ocorensa dl'element";
Blockly.Msg["LISTS_INDEX_OF_TOOLTIP"] = "A smon l'ìndes ëd la prima/ùltima ocorensa dl'element ant la lista. A smon %1 se l'element a l'é nen trovà.";
Blockly.Msg["LISTS_INLIST"] = "ant la lista";
Blockly.Msg["LISTS_ISEMPTY_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#is-empty";  // untranslated
Blockly.Msg["LISTS_ISEMPTY_TITLE"] = "%1 a l'é veuid";
Blockly.Msg["LISTS_ISEMPTY_TOOLTIP"] = "A smon ver se la lista a l'é veuida.";
Blockly.Msg["LISTS_LENGTH_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#length-of";  // untranslated
Blockly.Msg["LISTS_LENGTH_TITLE"] = "longheur ëd %1";
Blockly.Msg["LISTS_LENGTH_TOOLTIP"] = "A smon la longheur ¨d na lista.";
Blockly.Msg["LISTS_REPEAT_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#create-list-with";  // untranslated
Blockly.Msg["LISTS_REPEAT_TITLE"] = "creé na lista con l'element %1 arpetù %2 vire";
Blockly.Msg["LISTS_REPEAT_TOOLTIP"] = "A crea na lista ch'a consist dël valor dàit arpetù ël nùmer ëspessificà ëd vire.";
Blockly.Msg["LISTS_REVERSE_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#reversing-a-list";
Blockly.Msg["LISTS_REVERSE_MESSAGE0"] = "anversé %1";
Blockly.Msg["LISTS_REVERSE_TOOLTIP"] = "Anversé na còpia ëd na lista";
Blockly.Msg["LISTS_SET_INDEX_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#in-list--set";  // untranslated
Blockly.Msg["LISTS_SET_INDEX_INPUT_TO"] = "tanme";
Blockly.Msg["LISTS_SET_INDEX_INSERT"] = "anserì an";
Blockly.Msg["LISTS_SET_INDEX_SET"] = "buté";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_INSERT_FIRST"] = "A anseriss l'element al prinsipi ëd na lista.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_INSERT_FROM"] = "A anseriss l'element a la posission ëspessificà an na lista.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_INSERT_LAST"] = "Gionté l'element a la fin ëd na lista.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_INSERT_RANDOM"] = "A anseriss l'element a l'ancàpit an na lista.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_SET_FIRST"] = "A fissa ël prim element an na lista.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_SET_FROM"] = "A fissa l'element a la posission ëspessificà an na lista.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_SET_LAST"] = "A fissa l'ùltim element an na lista.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_SET_RANDOM"] = "A fissa n'element a l'ancàpit an na lista.";
Blockly.Msg["LISTS_SORT_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#sorting-a-list";
Blockly.Msg["LISTS_SORT_ORDER_ASCENDING"] = "chërsent";
Blockly.Msg["LISTS_SORT_ORDER_DESCENDING"] = "calant";
Blockly.Msg["LISTS_SORT_TITLE"] = "ordiné %1 %2 %3";
Blockly.Msg["LISTS_SORT_TOOLTIP"] = "Ordiné na còpia ëd na lista.";
Blockly.Msg["LISTS_SORT_TYPE_IGNORECASE"] = "alfabétich, ignorand ël caràter minùscol o majùscol";
Blockly.Msg["LISTS_SORT_TYPE_NUMERIC"] = "numérich";
Blockly.Msg["LISTS_SORT_TYPE_TEXT"] = "alfabétich";
Blockly.Msg["LISTS_SPLIT_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#splitting-strings-and-joining-lists";
Blockly.Msg["LISTS_SPLIT_LIST_FROM_TEXT"] = "fé na lista da 'n test";
Blockly.Msg["LISTS_SPLIT_TEXT_FROM_LIST"] = "fé 'n test da na lista";
Blockly.Msg["LISTS_SPLIT_TOOLTIP_JOIN"] = "Gionze na lista ëd test ant un test sol, separandje con un separator.";
Blockly.Msg["LISTS_SPLIT_TOOLTIP_SPLIT"] = "Divide un test an na lista ëd test, tajand a minca 'n separator.";
Blockly.Msg["LISTS_SPLIT_WITH_DELIMITER"] = "con ël separator";
Blockly.Msg["LOGIC_BOOLEAN_FALSE"] = "fàuss";
Blockly.Msg["LOGIC_BOOLEAN_HELPURL"] = "https://github.com/google/blockly/wiki/Logic#values";  // untranslated
Blockly.Msg["LOGIC_BOOLEAN_TOOLTIP"] = "A rëspond ver o fàuss.";
Blockly.Msg["LOGIC_BOOLEAN_TRUE"] = "ver";
Blockly.Msg["LOGIC_COMPARE_HELPURL"] = "https://en.wikipedia.org/wiki/Inequality_(mathematics)";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_EQ"] = "Rësponde ver si le doe imission a son uguaj.";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_GT"] = "Rësponde ver si la prima imission a l'é pi granda che la sconda.";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_GTE"] = "Rësponde ver si la prima imission a l'é pi granda o ugual a la sconda.";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_LT"] = "Rësponde ver si la prima imission a l'é pi cita dla sconda.";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_LTE"] = "Rësponde ver si la prima imission a l'é pi cita o ugual a la sconda.";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_NEQ"] = "Rësponde ver si le doe imission a son nen uguaj.";
Blockly.Msg["LOGIC_NEGATE_HELPURL"] = "https://github.com/google/blockly/wiki/Logic#not";  // untranslated
Blockly.Msg["LOGIC_NEGATE_TITLE"] = "nen %1";
Blockly.Msg["LOGIC_NEGATE_TOOLTIP"] = "A rëspond ver se l'imission a l'é fàussa. A rëspond fàuss se l'imission a l'é vera.";
Blockly.Msg["LOGIC_NULL"] = "gnente";
Blockly.Msg["LOGIC_NULL_HELPURL"] = "https://en.wikipedia.org/wiki/Nullable_type";  // untranslated
Blockly.Msg["LOGIC_NULL_TOOLTIP"] = "A rëspond gnente.";
Blockly.Msg["LOGIC_OPERATION_AND"] = "e";
Blockly.Msg["LOGIC_OPERATION_HELPURL"] = "https://github.com/google/blockly/wiki/Logic#logical-operations";  // untranslated
Blockly.Msg["LOGIC_OPERATION_OR"] = "o";
Blockly.Msg["LOGIC_OPERATION_TOOLTIP_AND"] = "Rësponde ver se tute doe j'imission a son vere.";
Blockly.Msg["LOGIC_OPERATION_TOOLTIP_OR"] = "Rësponde ver se almanch un-a d'imission a l'é vera.";
Blockly.Msg["LOGIC_TERNARY_CONDITION"] = "preuva";
Blockly.Msg["LOGIC_TERNARY_HELPURL"] = "https://en.wikipedia.org/wiki/%3F:";  // untranslated
Blockly.Msg["LOGIC_TERNARY_IF_FALSE"] = "se fàuss";
Blockly.Msg["LOGIC_TERNARY_IF_TRUE"] = "se ver";
Blockly.Msg["LOGIC_TERNARY_TOOLTIP"] = "Controlé la condission an 'preuva'. Se la condission a l'é vera, a rëspond con ël valor 'se ver'; dësnò a rëspond con ël valor 'se fàuss'.";
Blockly.Msg["MATH_ADDITION_SYMBOL"] = "+";  // untranslated
Blockly.Msg["MATH_ARITHMETIC_HELPURL"] = "https://en.wikipedia.org/wiki/Arithmetic";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_ADD"] = "A smon la soma ëd doi nùmer.";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_DIVIDE"] = "A smon ël cossient dij doi nùmer.";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_MINUS"] = "A smon la diferensa dij doi nùmer.";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_MULTIPLY"] = "A smon ël prodot dij doi nùmer.";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_POWER"] = "A smon ël prim nùmer alvà a la potensa dël second.";
Blockly.Msg["MATH_ATAN2_HELPURL"] = "https://en.wikipedia.org/wiki/Atan2";
Blockly.Msg["MATH_ATAN2_TITLE"] = "atan2 ëd X:%1 Y:%2";
Blockly.Msg["MATH_ATAN2_TOOLTIP"] = "A rëspond con l'arch-tangent dël pont (X, Y) an gre da -180 a 180.";
Blockly.Msg["MATH_CHANGE_HELPURL"] = "https://en.wikipedia.org/wiki/Programming_idiom#Incrementing_a_counter";
Blockly.Msg["MATH_CHANGE_TITLE"] = "ancrementé %1 për %2";
Blockly.Msg["MATH_CHANGE_TOOLTIP"] = "Gionté un nùmer a la variàbil '%1'.";
Blockly.Msg["MATH_CONSTANT_HELPURL"] = "https://en.wikipedia.org/wiki/Mathematical_constant";
Blockly.Msg["MATH_CONSTANT_TOOLTIP"] = "A smon un-a dle costante comun-e π (3.141…), e (2.718…), φ (1.618…), sqrt(2) (1.414…), sqrt(½) (0.707…) o ∞ (infinì).";
Blockly.Msg["MATH_CONSTRAIN_HELPURL"] = "https://en.wikipedia.org/wiki/Clamping_(graphics)";  // untranslated
Blockly.Msg["MATH_CONSTRAIN_TITLE"] = "limité %1 antra %2 e %3";
Blockly.Msg["MATH_CONSTRAIN_TOOLTIP"] = "Limité un nùmer a esse antra le limitassion ëspessificà (comprèise).";
Blockly.Msg["MATH_DIVISION_SYMBOL"] = "÷";  // untranslated
Blockly.Msg["MATH_IS_DIVISIBLE_BY"] = "a l'é divisìbil për";
Blockly.Msg["MATH_IS_EVEN"] = "a l'é cobi";
Blockly.Msg["MATH_IS_NEGATIVE"] = "a l'é negativ";
Blockly.Msg["MATH_IS_ODD"] = "a l'é dëscobi";
Blockly.Msg["MATH_IS_POSITIVE"] = "a l'é positiv";
Blockly.Msg["MATH_IS_PRIME"] = "a l'é prim";
Blockly.Msg["MATH_IS_TOOLTIP"] = "A contròla si un nùmer a l'é cobi, dëscobi, prim, antreghm positiv, negativ, o s'a l'é divisìbil për un nùmer dàit. A rëspond ver o fàuss.";
Blockly.Msg["MATH_IS_WHOLE"] = "a l'é antregh";
Blockly.Msg["MATH_MODULO_HELPURL"] = "https://en.wikipedia.org/wiki/Modulo_operation";
Blockly.Msg["MATH_MODULO_TITLE"] = "resta ëd %1:%2";
Blockly.Msg["MATH_MODULO_TOOLTIP"] = "A smon la resta ëd la division dij doi nùmer.";
Blockly.Msg["MATH_MULTIPLICATION_SYMBOL"] = "×";  // untranslated
Blockly.Msg["MATH_NUMBER_HELPURL"] = "https://en.wikipedia.org/wiki/Number";
Blockly.Msg["MATH_NUMBER_TOOLTIP"] = "Un nùmer.";
Blockly.Msg["MATH_ONLIST_HELPURL"] = "";  // untranslated
Blockly.Msg["MATH_ONLIST_OPERATOR_AVERAGE"] = "media dla lista";
Blockly.Msg["MATH_ONLIST_OPERATOR_MAX"] = "màssim ëd la lista";
Blockly.Msg["MATH_ONLIST_OPERATOR_MEDIAN"] = "mesan-a dla lista";
Blockly.Msg["MATH_ONLIST_OPERATOR_MIN"] = "mìnim ëd la lista";
Blockly.Msg["MATH_ONLIST_OPERATOR_MODE"] = "mòde dla lista";
Blockly.Msg["MATH_ONLIST_OPERATOR_RANDOM"] = "element a l'ancàpit ëd la lista";
Blockly.Msg["MATH_ONLIST_OPERATOR_STD_DEV"] = "deviassion ëstàndard ëd la lista";
Blockly.Msg["MATH_ONLIST_OPERATOR_SUM"] = "soma dla lista";
Blockly.Msg["MATH_ONLIST_TOOLTIP_AVERAGE"] = "A smon la media (aritmética) dij valor numérich ant la lista.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_MAX"] = "A smon ël pi gròss nùmer ëd la lista.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_MEDIAN"] = "A smon ël nùmer mesan ëd la lista.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_MIN"] = "A smon ël pi cit nùmer ëd la lista.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_MODE"] = "A smon na lista dj'element pi frequent ëd la lista.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_RANDOM"] = "A smon n'element a l'ancàpit da la lista.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_STD_DEV"] = "A smon la deviassion ëstàndard ëd la lista.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_SUM"] = "A smon la soma ëd tuti ij nùmer ant la lista.";
Blockly.Msg["MATH_POWER_SYMBOL"] = "^";  // untranslated
Blockly.Msg["MATH_RANDOM_FLOAT_HELPURL"] = "https://en.wikipedia.org/wiki/Random_number_generation";
Blockly.Msg["MATH_RANDOM_FLOAT_TITLE_RANDOM"] = "frassion aleatòria";
Blockly.Msg["MATH_RANDOM_FLOAT_TOOLTIP"] = "A smon na frassion aleatòria antra 0,0 (comprèis) e 1,0 (esclus).";
Blockly.Msg["MATH_RANDOM_INT_HELPURL"] = "https://en.wikipedia.org/wiki/Random_number_generation";
Blockly.Msg["MATH_RANDOM_INT_TITLE"] = "antregh aleatòri antra %1 e %2";
Blockly.Msg["MATH_RANDOM_INT_TOOLTIP"] = "A smon n'antregh aleatòri antra ij doi lìmit ëspessificà, comprèis.";
Blockly.Msg["MATH_ROUND_HELPURL"] = "https://en.wikipedia.org/wiki/Rounding";
Blockly.Msg["MATH_ROUND_OPERATOR_ROUND"] = "ariondé";
Blockly.Msg["MATH_ROUND_OPERATOR_ROUNDDOWN"] = "ariondé për difet";
Blockly.Msg["MATH_ROUND_OPERATOR_ROUNDUP"] = "ariondé për ecess";
Blockly.Msg["MATH_ROUND_TOOLTIP"] = "A arionda un nùmer për difet o ecess.";
Blockly.Msg["MATH_SINGLE_HELPURL"] = "https://en.wikipedia.org/wiki/Square_root";
Blockly.Msg["MATH_SINGLE_OP_ABSOLUTE"] = "assolù";
Blockly.Msg["MATH_SINGLE_OP_ROOT"] = "rèis quadra";
Blockly.Msg["MATH_SINGLE_TOOLTIP_ABS"] = "A smon ël valor assolù d'un nùmer.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_EXP"] = "A smon e a la potensa d'un nùmer.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_LN"] = "A smon ël logaritm natural d'un nùmer.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_LOG10"] = "A smon ël logaritm an base 10 d'un nùmer.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_NEG"] = "A smon l'opòst d'un nùmer.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_POW10"] = "A smon 10 a la potensa d'un nùmer.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_ROOT"] = "A smon la rèis quadra d'un nùmer.";
Blockly.Msg["MATH_SUBTRACTION_SYMBOL"] = "-";  // untranslated
Blockly.Msg["MATH_TRIG_ACOS"] = "acos";  // untranslated
Blockly.Msg["MATH_TRIG_ASIN"] = "asin";  // untranslated
Blockly.Msg["MATH_TRIG_ATAN"] = "atan";  // untranslated
Blockly.Msg["MATH_TRIG_COS"] = "cos";  // untranslated
Blockly.Msg["MATH_TRIG_HELPURL"] = "https://en.wikipedia.org/wiki/Trigonometric_functions";
Blockly.Msg["MATH_TRIG_SIN"] = "sin";  // untranslated
Blockly.Msg["MATH_TRIG_TAN"] = "tan";  // untranslated
Blockly.Msg["MATH_TRIG_TOOLTIP_ACOS"] = "A smon l'arch-cosen d'un nùmer.";
Blockly.Msg["MATH_TRIG_TOOLTIP_ASIN"] = "A smon l'arch-sen d'un nùmer.";
Blockly.Msg["MATH_TRIG_TOOLTIP_ATAN"] = "A smon l'arch-tangenta d'un nùmer.";
Blockly.Msg["MATH_TRIG_TOOLTIP_COS"] = "A smon ël cosen ëd n'àngol an gré (pa an radiant).";
Blockly.Msg["MATH_TRIG_TOOLTIP_SIN"] = "A smon ël sen ëd n'àngol an gré (pa an radiant).";
Blockly.Msg["MATH_TRIG_TOOLTIP_TAN"] = "A smon la tangenta ëd n'àngol an gré (pa an radiant).";
Blockly.Msg["NEW_COLOUR_VARIABLE"] = "Creé na variàbil ëd color...";
Blockly.Msg["NEW_NUMBER_VARIABLE"] = "Creé na variàbil numérica...";
Blockly.Msg["NEW_STRING_VARIABLE"] = "Creé na variàbil dë stringa...";
Blockly.Msg["NEW_VARIABLE"] = "Creé na variàbil...";
Blockly.Msg["NEW_VARIABLE_TITLE"] = "Nòm ëd la neuva variàbil:";
Blockly.Msg["NEW_VARIABLE_TYPE_TITLE"] = "Neuva sòrt ëd variàbil:";
Blockly.Msg["ORDINAL_NUMBER_SUFFIX"] = "";  // untranslated
Blockly.Msg["PROCEDURES_ALLOW_STATEMENTS"] = "përmëtte le diciairassion";
Blockly.Msg["PROCEDURES_BEFORE_PARAMS"] = "con:";
Blockly.Msg["PROCEDURES_CALLNORETURN_HELPURL"] = "https://en.wikipedia.org/wiki/Subroutine";
Blockly.Msg["PROCEDURES_CALLNORETURN_TOOLTIP"] = "Eseguì la fonsion '%1' definìa da l'utent.";
Blockly.Msg["PROCEDURES_CALLRETURN_HELPURL"] = "https://en.wikipedia.org/wiki/Subroutine";
Blockly.Msg["PROCEDURES_CALLRETURN_TOOLTIP"] = "Eseguì la fonsion '%1' definìa da l'utent e dovré sò arzultà.";
Blockly.Msg["PROCEDURES_CALL_BEFORE_PARAMS"] = "con:";
Blockly.Msg["PROCEDURES_CREATE_DO"] = "Creé '%1'";
Blockly.Msg["PROCEDURES_DEFNORETURN_COMMENT"] = "Descrive sa fonsion...";
Blockly.Msg["PROCEDURES_DEFNORETURN_DO"] = "";  // untranslated
Blockly.Msg["PROCEDURES_DEFNORETURN_HELPURL"] = "https://en.wikipedia.org/wiki/Subroutine";  // untranslated
Blockly.Msg["PROCEDURES_DEFNORETURN_PROCEDURE"] = "fé cheicòs";
Blockly.Msg["PROCEDURES_DEFNORETURN_TITLE"] = "a";
Blockly.Msg["PROCEDURES_DEFNORETURN_TOOLTIP"] = "A crea na fonsion sensa surtìa.";
Blockly.Msg["PROCEDURES_DEFRETURN_HELPURL"] = "https://en.wikipedia.org/wiki/Subroutine";  // untranslated
Blockly.Msg["PROCEDURES_DEFRETURN_RETURN"] = "artorn";
Blockly.Msg["PROCEDURES_DEFRETURN_TOOLTIP"] = "A crea na fonsion con na surtìa.";
Blockly.Msg["PROCEDURES_DEF_DUPLICATE_WARNING"] = "Atension: Costa fonsion a l'ha dij paràmeter duplicà.";
Blockly.Msg["PROCEDURES_HIGHLIGHT_DEF"] = "Sot-ligné la definission dla fonsion";
Blockly.Msg["PROCEDURES_IFRETURN_HELPURL"] = "http://c2.com/cgi/wiki?GuardClause";
Blockly.Msg["PROCEDURES_IFRETURN_TOOLTIP"] = "Se un valor a l'é ver, antlora smon-e un second valor.";
Blockly.Msg["PROCEDURES_IFRETURN_WARNING"] = "Atension: Ës blòch a podria esse dovrà mach an na definission ëd fonsion.";
Blockly.Msg["PROCEDURES_MUTATORARG_TITLE"] = "nòm ëd l'imission:";
Blockly.Msg["PROCEDURES_MUTATORARG_TOOLTIP"] = "Gionté n'imission a la fonsion.";
Blockly.Msg["PROCEDURES_MUTATORCONTAINER_TITLE"] = "imission";
Blockly.Msg["PROCEDURES_MUTATORCONTAINER_TOOLTIP"] = "Gionté, gavé o riordiné j'imission ëd sa fonsion.";
Blockly.Msg["REDO"] = "Fé torna";
Blockly.Msg["REMOVE_COMMENT"] = "Scancelé un coment";
Blockly.Msg["RENAME_VARIABLE"] = "Arnomé la variàbil...";
Blockly.Msg["RENAME_VARIABLE_TITLE"] = "Arnomé tute le variàbij '%1' 'me:";
Blockly.Msg["TEXT_APPEND_HELPURL"] = "https://github.com/google/blockly/wiki/Text#text-modification";  // untranslated
Blockly.Msg["TEXT_APPEND_TITLE"] = "a %1 taché ël test %2";
Blockly.Msg["TEXT_APPEND_TOOLTIP"] = "Taché dël test a la variàbil '%1'.";
Blockly.Msg["TEXT_CHANGECASE_HELPURL"] = "https://github.com/google/blockly/wiki/Text#adjusting-text-case";  // untranslated
Blockly.Msg["TEXT_CHANGECASE_OPERATOR_LOWERCASE"] = "an minùscul";
Blockly.Msg["TEXT_CHANGECASE_OPERATOR_TITLECASE"] = "an Majùscol A L'Ancamin Ëd Minca Paròla";
Blockly.Msg["TEXT_CHANGECASE_OPERATOR_UPPERCASE"] = "an MAJÙSCOL";
Blockly.Msg["TEXT_CHANGECASE_TOOLTIP"] = "A smon na còpia dël test ant un caràter diferent.";
Blockly.Msg["TEXT_CHARAT_FIRST"] = "oten-e la prima litra";
Blockly.Msg["TEXT_CHARAT_FROM_END"] = "oten-e la litra # da la fin";
Blockly.Msg["TEXT_CHARAT_FROM_START"] = "oten-e la litra #";
Blockly.Msg["TEXT_CHARAT_HELPURL"] = "https://github.com/google/blockly/wiki/Text#extracting-text";  // untranslated
Blockly.Msg["TEXT_CHARAT_LAST"] = "oten-e l'ùltima litra";
Blockly.Msg["TEXT_CHARAT_RANDOM"] = "oten-e na litra a l'ancàpit";
Blockly.Msg["TEXT_CHARAT_TAIL"] = "";  // untranslated
Blockly.Msg["TEXT_CHARAT_TITLE"] = "ant ël test %1 %2";
Blockly.Msg["TEXT_CHARAT_TOOLTIP"] = "A smon la litra ant la posission ëspessificà.";
Blockly.Msg["TEXT_COUNT_HELPURL"] = "https://github.com/google/blockly/wiki/Text#counting-substrings";
Blockly.Msg["TEXT_COUNT_MESSAGE0"] = "nùmer %1 su %2";
Blockly.Msg["TEXT_COUNT_TOOLTIP"] = "Conté vàire vire un test dàit a compariss an n'àutr test.";
Blockly.Msg["TEXT_CREATE_JOIN_ITEM_TOOLTIP"] = "Gionté n'element al test.";
Blockly.Msg["TEXT_CREATE_JOIN_TITLE_JOIN"] = "gionze";
Blockly.Msg["TEXT_CREATE_JOIN_TOOLTIP"] = "Gionté, gavé o riordiné le session për configuré torna ës blòch ëd test.";
Blockly.Msg["TEXT_GET_SUBSTRING_END_FROM_END"] = "fin-a a la litra # da la fin";
Blockly.Msg["TEXT_GET_SUBSTRING_END_FROM_START"] = "fin-a a la litra #";
Blockly.Msg["TEXT_GET_SUBSTRING_END_LAST"] = "fin-a a l'ùltima litra";
Blockly.Msg["TEXT_GET_SUBSTRING_HELPURL"] = "https://github.com/google/blockly/wiki/Text#extracting-a-region-of-text";  // untranslated
Blockly.Msg["TEXT_GET_SUBSTRING_INPUT_IN_TEXT"] = "ant ël test";
Blockly.Msg["TEXT_GET_SUBSTRING_START_FIRST"] = "oten-e la sota-stringa da la prima litra";
Blockly.Msg["TEXT_GET_SUBSTRING_START_FROM_END"] = "oten-e la sota-stringa da la litra # da la fin";
Blockly.Msg["TEXT_GET_SUBSTRING_START_FROM_START"] = "oten-e la sota-stringa da la litra #";
Blockly.Msg["TEXT_GET_SUBSTRING_TAIL"] = "";  // untranslated
Blockly.Msg["TEXT_GET_SUBSTRING_TOOLTIP"] = "A smon un tòch ëspessificà dël test.";
Blockly.Msg["TEXT_INDEXOF_HELPURL"] = "https://github.com/google/blockly/wiki/Text#finding-text";  // untranslated
Blockly.Msg["TEXT_INDEXOF_OPERATOR_FIRST"] = "trové la prima ocorensa dël test";
Blockly.Msg["TEXT_INDEXOF_OPERATOR_LAST"] = "trové l'ùltima ocorensa dël test";
Blockly.Msg["TEXT_INDEXOF_TITLE"] = "ant ël test %1 %2 %3";
Blockly.Msg["TEXT_INDEXOF_TOOLTIP"] = "A smon l'ìndes dla prima/ùltima ocorensa dël prim test ant ël second test. A smon %1 se ël test a l'é nen trovà.";
Blockly.Msg["TEXT_ISEMPTY_HELPURL"] = "https://github.com/google/blockly/wiki/Text#checking-for-empty-text";  // untranslated
Blockly.Msg["TEXT_ISEMPTY_TITLE"] = "%1 a l'é veuid";
Blockly.Msg["TEXT_ISEMPTY_TOOLTIP"] = "A smon ver se ël test fornì a l'é veuid.";
Blockly.Msg["TEXT_JOIN_HELPURL"] = "https://github.com/google/blockly/wiki/Text#text-creation";  // untranslated
Blockly.Msg["TEXT_JOIN_TITLE_CREATEWITH"] = "creé ël test con";
Blockly.Msg["TEXT_JOIN_TOOLTIP"] = "Creé un tòch ëd test an gionzend un nùmer qualsëssìa d'element.";
Blockly.Msg["TEXT_LENGTH_HELPURL"] = "https://github.com/google/blockly/wiki/Text#text-modification";  // untranslated
Blockly.Msg["TEXT_LENGTH_TITLE"] = "longheur ëd %1";
Blockly.Msg["TEXT_LENGTH_TOOLTIP"] = "A smon ël nùmer ëd litre (spassi comprèis) ant ël test fornì.";
Blockly.Msg["TEXT_PRINT_HELPURL"] = "https://github.com/google/blockly/wiki/Text#printing-text";  // untranslated
Blockly.Msg["TEXT_PRINT_TITLE"] = "smon-e %1";
Blockly.Msg["TEXT_PRINT_TOOLTIP"] = "Smon-e ël test, ël nùmer o n'àutr valor ëspessificà.";
Blockly.Msg["TEXT_PROMPT_HELPURL"] = "https://github.com/google/blockly/wiki/Text#getting-input-from-the-user";  // untranslated
Blockly.Msg["TEXT_PROMPT_TOOLTIP_NUMBER"] = "Ciamé un nùmer a l'utent.";
Blockly.Msg["TEXT_PROMPT_TOOLTIP_TEXT"] = "Ciamé un test a l'utent.";
Blockly.Msg["TEXT_PROMPT_TYPE_NUMBER"] = "anvit për un nùmer con un mëssagi";
Blockly.Msg["TEXT_PROMPT_TYPE_TEXT"] = "anvit për un test con un mëssagi";
Blockly.Msg["TEXT_REPLACE_HELPURL"] = "https://github.com/google/blockly/wiki/Text#replacing-substrings";
Blockly.Msg["TEXT_REPLACE_MESSAGE0"] = "rampiassé %1 con %2 an %3";
Blockly.Msg["TEXT_REPLACE_TOOLTIP"] = "Rampiassé tute j'ocorense d'un test con n'àutr.";
Blockly.Msg["TEXT_REVERSE_HELPURL"] = "https://github.com/google/blockly/wiki/Text#reversing-text";
Blockly.Msg["TEXT_REVERSE_MESSAGE0"] = "Anversé %1";
Blockly.Msg["TEXT_REVERSE_TOOLTIP"] = "Anversé l'òrdin dij caràter ant ël test.";
Blockly.Msg["TEXT_TEXT_HELPURL"] = "https://en.wikipedia.org/wiki/String_(computer_science)";
Blockly.Msg["TEXT_TEXT_TOOLTIP"] = "Na litra, na paròla o na linia ëd test.";
Blockly.Msg["TEXT_TRIM_HELPURL"] = "https://github.com/google/blockly/wiki/Text#trimming-removing-spaces";  // untranslated
Blockly.Msg["TEXT_TRIM_OPERATOR_BOTH"] = "gavé jë spassi da le doe bande ëd";
Blockly.Msg["TEXT_TRIM_OPERATOR_LEFT"] = "gavé jë spassi da la banda snistra ëd";
Blockly.Msg["TEXT_TRIM_OPERATOR_RIGHT"] = "gavé jë spassi da la banda drita ëd";
Blockly.Msg["TEXT_TRIM_TOOLTIP"] = "A smon na còpia dël test con jë spassi gavà da n'estremità o da tute doe.";
Blockly.Msg["TODAY"] = "Ancheuj";
Blockly.Msg["UNDO"] = "Anulé";
Blockly.Msg["UNNAMED_KEY"] = "anònim";
Blockly.Msg["VARIABLES_DEFAULT_NAME"] = "element";
Blockly.Msg["VARIABLES_GET_CREATE_SET"] = "Creé 'fissé %1'";
Blockly.Msg["VARIABLES_GET_HELPURL"] = "https://github.com/google/blockly/wiki/Variables#get";  // untranslated
Blockly.Msg["VARIABLES_GET_TOOLTIP"] = "A smon ël valor ëd sa variàbil.";
Blockly.Msg["VARIABLES_SET"] = "fissé %1 a %2";
Blockly.Msg["VARIABLES_SET_CREATE_GET"] = "Creé 'oten-e %1'";
Blockly.Msg["VARIABLES_SET_HELPURL"] = "https://github.com/google/blockly/wiki/Variables#set";  // untranslated
Blockly.Msg["VARIABLES_SET_TOOLTIP"] = "Fissé costa variàbil ugual al valor d'imission.";
Blockly.Msg["VARIABLE_ALREADY_EXISTS"] = "Na variàbil con ël nòm '%1' a esist già.";
Blockly.Msg["VARIABLE_ALREADY_EXISTS_FOR_ANOTHER_TYPE"] = "Na variàbil ciamà '%1' a esist già për n'àutra sòrt: '%2'.";
Blockly.Msg["WORKSPACE_ARIA_LABEL"] = "Spassi ëd travaj ëd Blockly";
Blockly.Msg["WORKSPACE_COMMENT_DEFAULT_TEXT"] = "Dì cheicòs...";
Blockly.Msg["CONTROLS_FOREACH_INPUT_DO"] = Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"];
Blockly.Msg["CONTROLS_FOR_INPUT_DO"] = Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"];
Blockly.Msg["CONTROLS_IF_ELSEIF_TITLE_ELSEIF"] = Blockly.Msg["CONTROLS_IF_MSG_ELSEIF"];
Blockly.Msg["CONTROLS_IF_ELSE_TITLE_ELSE"] = Blockly.Msg["CONTROLS_IF_MSG_ELSE"];
Blockly.Msg["CONTROLS_IF_IF_TITLE_IF"] = Blockly.Msg["CONTROLS_IF_MSG_IF"];
Blockly.Msg["CONTROLS_IF_MSG_THEN"] = Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"];
Blockly.Msg["CONTROLS_WHILEUNTIL_INPUT_DO"] = Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"];
Blockly.Msg["LISTS_CREATE_WITH_ITEM_TITLE"] = Blockly.Msg["VARIABLES_DEFAULT_NAME"];
Blockly.Msg["LISTS_GET_INDEX_HELPURL"] = Blockly.Msg["LISTS_INDEX_OF_HELPURL"];
Blockly.Msg["LISTS_GET_INDEX_INPUT_IN_LIST"] = Blockly.Msg["LISTS_INLIST"];
Blockly.Msg["LISTS_GET_SUBLIST_INPUT_IN_LIST"] = Blockly.Msg["LISTS_INLIST"];
Blockly.Msg["LISTS_INDEX_OF_INPUT_IN_LIST"] = Blockly.Msg["LISTS_INLIST"];
Blockly.Msg["LISTS_SET_INDEX_INPUT_IN_LIST"] = Blockly.Msg["LISTS_INLIST"];
Blockly.Msg["MATH_CHANGE_TITLE_ITEM"] = Blockly.Msg["VARIABLES_DEFAULT_NAME"];
Blockly.Msg["PROCEDURES_DEFRETURN_COMMENT"] = Blockly.Msg["PROCEDURES_DEFNORETURN_COMMENT"];
Blockly.Msg["PROCEDURES_DEFRETURN_DO"] = Blockly.Msg["PROCEDURES_DEFNORETURN_DO"];
Blockly.Msg["PROCEDURES_DEFRETURN_PROCEDURE"] = Blockly.Msg["PROCEDURES_DEFNORETURN_PROCEDURE"];
Blockly.Msg["PROCEDURES_DEFRETURN_TITLE"] = Blockly.Msg["PROCEDURES_DEFNORETURN_TITLE"];
Blockly.Msg["TEXT_APPEND_VARIABLE"] = Blockly.Msg["VARIABLES_DEFAULT_NAME"];
Blockly.Msg["TEXT_CREATE_JOIN_ITEM_TITLE_ITEM"] = Blockly.Msg["VARIABLES_DEFAULT_NAME"];

Blockly.Msg["MATH_HUE"] = "230";
Blockly.Msg["LOOPS_HUE"] = "120";
Blockly.Msg["LISTS_HUE"] = "260";
Blockly.Msg["LOGIC_HUE"] = "210";
Blockly.Msg["VARIABLES_HUE"] = "330";
Blockly.Msg["TEXTS_HUE"] = "160";
Blockly.Msg["PROCEDURES_HUE"] = "290";
Blockly.Msg["COLOUR_HUE"] = "20";
Blockly.Msg["VARIABLES_DYNAMIC_HUE"] = "310";