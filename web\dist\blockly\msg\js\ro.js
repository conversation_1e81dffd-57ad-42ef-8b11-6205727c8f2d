// This file was automatically generated.  Do not modify.

'use strict';

Blockly.Msg["ADD_COMMENT"] = "Adaugă un comentariu";
Blockly.Msg["CANNOT_DELETE_VARIABLE_PROCEDURE"] = "Nu se poate șterge variabila '%1' deoarece face parte din definiția funcției '%2'";
Blockly.Msg["CHANGE_VALUE_TITLE"] = "Schimbați valoarea:";
Blockly.Msg["CLEAN_UP"] = "Curăță blocări";
Blockly.Msg["COLLAPSED_WARNINGS_WARNING"] = "Blocurile blocate conțin avertismente.";
Blockly.Msg["COLLAPSE_ALL"] = "Restrange blocurile";
Blockly.Msg["COLLAPSE_BLOCK"] = "Restrange blocul";
Blockly.Msg["COLOUR_BLEND_COLOUR1"] = "culoare 1";
Blockly.Msg["COLOUR_BLEND_COLOUR2"] = "culoare 2";
Blockly.Msg["COLOUR_BLEND_HELPURL"] = "http://meyerweb.com/eric/tools/color-blend/";
Blockly.Msg["COLOUR_BLEND_RATIO"] = "Raport";
Blockly.Msg["COLOUR_BLEND_TITLE"] = "amestec";
Blockly.Msg["COLOUR_BLEND_TOOLTIP"] = "Amestecă două culori cu un raport dat (0.0 - 1.0).";
Blockly.Msg["COLOUR_PICKER_HELPURL"] = "https://ro.wikipedia.org/wiki/Culoare";
Blockly.Msg["COLOUR_PICKER_TOOLTIP"] = "Alege o culoare din paleta de culori.";
Blockly.Msg["COLOUR_RANDOM_HELPURL"] = "http://randomcolour.com";  // untranslated
Blockly.Msg["COLOUR_RANDOM_TITLE"] = "culoare aleatorie";
Blockly.Msg["COLOUR_RANDOM_TOOLTIP"] = "Alege o culoare la întâmplare.";
Blockly.Msg["COLOUR_RGB_BLUE"] = "albastru";
Blockly.Msg["COLOUR_RGB_GREEN"] = "verde";
Blockly.Msg["COLOUR_RGB_HELPURL"] = "http://www.december.com/html/spec/colorper.html";
Blockly.Msg["COLOUR_RGB_RED"] = "roșu";
Blockly.Msg["COLOUR_RGB_TITLE"] = "colorează cu";
Blockly.Msg["COLOUR_RGB_TOOLTIP"] = "Creează o culoare cu suma specificată de roșu, verde și albastru.  Toate valorile trebuie să fie între 0 și 100.";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_HELPURL"] = "https://github.com/google/blockly/wiki/Loops#loop-termination-blocks";  // untranslated
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_OPERATOR_BREAK"] = "ieși din buclă";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_OPERATOR_CONTINUE"] = "continuă cu următoarea iterație a buclei";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_TOOLTIP_BREAK"] = "Ieși din buclă.";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_TOOLTIP_CONTINUE"] = "Sari peste restul acestei bucle și continuă cu următoarea iterație.";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_WARNING"] = "Avertisment: Acest bloc pote fi utilizat numai în interiorul unei bucle.";
Blockly.Msg["CONTROLS_FOREACH_HELPURL"] = "https://github.com/google/blockly/wiki/Loops#for-each";  // untranslated
Blockly.Msg["CONTROLS_FOREACH_TITLE"] = "pentru fiecare element %1 în listă %2";
Blockly.Msg["CONTROLS_FOREACH_TOOLTIP"] = "Pentru fiecare element din listă, setați variabila „%1” ca valoarea elementului, și apoi faceți unele declarații.";
Blockly.Msg["CONTROLS_FOR_HELPURL"] = "https://github.com/google/blockly/wiki/Loops#count-with";  // untranslated
Blockly.Msg["CONTROLS_FOR_TITLE"] = "numără cu %1 de la %2 la %3 prin %4";
Blockly.Msg["CONTROLS_FOR_TOOLTIP"] = "Cu variablia \"%1\" ia o valoare din numărul început la numărul final, numara in intervalul specificat, apoi face blocurile specificate.";
Blockly.Msg["CONTROLS_IF_ELSEIF_TOOLTIP"] = "Adăugați o condiție în blocul if.";
Blockly.Msg["CONTROLS_IF_ELSE_TOOLTIP"] = "Adauga o stare finala, cuprinde toata conditia din blocul if.";
Blockly.Msg["CONTROLS_IF_HELPURL"] = "https://github.com/google/blockly/wiki/IfElse";  // untranslated
Blockly.Msg["CONTROLS_IF_IF_TOOLTIP"] = "Adaugă, elimină sau reordonează secțiuni pentru a reconfigura acest bloc if.";
Blockly.Msg["CONTROLS_IF_MSG_ELSE"] = "altfel";
Blockly.Msg["CONTROLS_IF_MSG_ELSEIF"] = "altfel dacă";
Blockly.Msg["CONTROLS_IF_MSG_IF"] = "dacă";
Blockly.Msg["CONTROLS_IF_TOOLTIP_1"] = "Dacă o valoare este adevărată, atunci fă unele declarații.";
Blockly.Msg["CONTROLS_IF_TOOLTIP_2"] = "Dacă o valoare este adevărat, atunci face primul bloc de declarații.  Altfel, face al doilea bloc de declarații.";
Blockly.Msg["CONTROLS_IF_TOOLTIP_3"] = "Dacă prima valoare este adevărat, atunci face primul bloc de declarații. Altfel, dacă a doua valoare este adevărat, face al doilea bloc de declarații.";
Blockly.Msg["CONTROLS_IF_TOOLTIP_4"] = "Dacă prima valoare este adevărat, atunci face primul bloc de declarații. Altfel, dacă a doua valoare este adevărat, face al doilea bloc de declarații. În cazul în care niciuna din valori nu este adevărat, face ultimul bloc de declarații.";
Blockly.Msg["CONTROLS_REPEAT_HELPURL"] = "https://en.wikipedia.org/wiki/For_loop";
Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"] = "fă";
Blockly.Msg["CONTROLS_REPEAT_TITLE"] = "repetă de %1 ori";
Blockly.Msg["CONTROLS_REPEAT_TOOLTIP"] = "Face unele afirmații de mai multe ori.";
Blockly.Msg["CONTROLS_WHILEUNTIL_HELPURL"] = "https://github.com/google/blockly/wiki/Loops#repeat";  // untranslated
Blockly.Msg["CONTROLS_WHILEUNTIL_OPERATOR_UNTIL"] = "Repetați până când";
Blockly.Msg["CONTROLS_WHILEUNTIL_OPERATOR_WHILE"] = "repetă în timp ce";
Blockly.Msg["CONTROLS_WHILEUNTIL_TOOLTIP_UNTIL"] = "Atâta timp cât o valoare este adevărat, atunci face unele declarații.";
Blockly.Msg["CONTROLS_WHILEUNTIL_TOOLTIP_WHILE"] = "Atâta timp cât o valoare este adevărat, atunci face unele declarații.";
Blockly.Msg["DELETE_ALL_BLOCKS"] = "Ștergi toate cele %1 (de) blocuri?";
Blockly.Msg["DELETE_BLOCK"] = "Șterge Bloc";
Blockly.Msg["DELETE_VARIABLE"] = "Ștergeți variabila '%1'";
Blockly.Msg["DELETE_VARIABLE_CONFIRMATION"] = "Șterge %1 utilizările variabilei '%2'?";
Blockly.Msg["DELETE_X_BLOCKS"] = "Ștergeți %1 Blocuri";
Blockly.Msg["DIALOG_CANCEL"] = "Revocare";
Blockly.Msg["DIALOG_OK"] = "OK";
Blockly.Msg["DISABLE_BLOCK"] = "Dezactivați bloc";
Blockly.Msg["DUPLICATE_BLOCK"] = "Duplicate";
Blockly.Msg["DUPLICATE_COMMENT"] = "Coment duplicat";
Blockly.Msg["ENABLE_BLOCK"] = "Permite bloc";
Blockly.Msg["EXPAND_ALL"] = "Extinde blocuri";
Blockly.Msg["EXPAND_BLOCK"] = "Extinde bloc";
Blockly.Msg["EXTERNAL_INPUTS"] = "Intrări externe";
Blockly.Msg["HELP"] = "Ajutor";
Blockly.Msg["INLINE_INPUTS"] = "Intrări în linie";
Blockly.Msg["LISTS_CREATE_EMPTY_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#create-empty-list";
Blockly.Msg["LISTS_CREATE_EMPTY_TITLE"] = "creează listă goală";
Blockly.Msg["LISTS_CREATE_EMPTY_TOOLTIP"] = "Returnează o listă, de lungime 0, care nu conține înregistrări de date";
Blockly.Msg["LISTS_CREATE_WITH_CONTAINER_TITLE_ADD"] = "listă";
Blockly.Msg["LISTS_CREATE_WITH_CONTAINER_TOOLTIP"] = "Adaugă, elimină sau reordonează secțiuni pentru a reconfigura acest bloc de listă.";
Blockly.Msg["LISTS_CREATE_WITH_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#create-list-with";  // untranslated
Blockly.Msg["LISTS_CREATE_WITH_INPUT_WITH"] = "creează listă cu";
Blockly.Msg["LISTS_CREATE_WITH_ITEM_TOOLTIP"] = "Adăugați un element la listă.";
Blockly.Msg["LISTS_CREATE_WITH_TOOLTIP"] = "Creați o listă cu orice număr de elemente.";
Blockly.Msg["LISTS_GET_INDEX_FIRST"] = "primul";
Blockly.Msg["LISTS_GET_INDEX_FROM_END"] = "# de la sfârșit";
Blockly.Msg["LISTS_GET_INDEX_FROM_START"] = "#";
Blockly.Msg["LISTS_GET_INDEX_GET"] = "obține";
Blockly.Msg["LISTS_GET_INDEX_GET_REMOVE"] = "obține și elimină";
Blockly.Msg["LISTS_GET_INDEX_LAST"] = "ultimul";
Blockly.Msg["LISTS_GET_INDEX_RANDOM"] = "aleator";
Blockly.Msg["LISTS_GET_INDEX_REMOVE"] = "elimină";
Blockly.Msg["LISTS_GET_INDEX_TAIL"] = "";  // untranslated
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_FIRST"] = "Returnează primul element dintr-o listă.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_FROM"] = "Returnează elementul de la poziția specificată dintr-o listă.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_LAST"] = "Returnează ultimul element într-o listă.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_RANDOM"] = "Returneaza un element aleatoriu într-o listă.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_FIRST"] = "Elimină și returnează primul element dintr-o listă.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_FROM"] = "Elimină și returnează elementul de la poziția specificată dintr-o listă.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_LAST"] = "Elimină și returnează ultimul element dintr-o listă.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_RANDOM"] = "Elimină și returnează un element aleatoriu într-o listă.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_REMOVE_FIRST"] = "Elimină primul element într-o listă.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_REMOVE_FROM"] = "Elimină elementul de la poziția specificată dintr-o listă.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_REMOVE_LAST"] = "Elimină ultimul element într-o listă.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_REMOVE_RANDOM"] = "Elimină un element aleatoriu într-o listă.";
Blockly.Msg["LISTS_GET_SUBLIST_END_FROM_END"] = "la # de la sfarsit";
Blockly.Msg["LISTS_GET_SUBLIST_END_FROM_START"] = "la #";
Blockly.Msg["LISTS_GET_SUBLIST_END_LAST"] = "la ultima";
Blockly.Msg["LISTS_GET_SUBLIST_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#getting-a-sublist";  // untranslated
Blockly.Msg["LISTS_GET_SUBLIST_START_FIRST"] = "obține sub-lista de la primul";
Blockly.Msg["LISTS_GET_SUBLIST_START_FROM_END"] = "obține sub-lista de la # de la sfârșit";
Blockly.Msg["LISTS_GET_SUBLIST_START_FROM_START"] = "obține sub-lista de la #";
Blockly.Msg["LISTS_GET_SUBLIST_TAIL"] = "";  // untranslated
Blockly.Msg["LISTS_GET_SUBLIST_TOOLTIP"] = "Creează o copie a porțiunii specificate dintr-o listă.";
Blockly.Msg["LISTS_INDEX_FROM_END_TOOLTIP"] = "%1 este ultimul element.";
Blockly.Msg["LISTS_INDEX_FROM_START_TOOLTIP"] = "%1 este primul element.";
Blockly.Msg["LISTS_INDEX_OF_FIRST"] = "Găsește prima apariție a elementului";
Blockly.Msg["LISTS_INDEX_OF_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#getting-items-from-a-list";  // untranslated
Blockly.Msg["LISTS_INDEX_OF_LAST"] = "găsește ultima apariție a elementului";
Blockly.Msg["LISTS_INDEX_OF_TOOLTIP"] = "Returnează indexul primei/ultimei apariții a articolului din listă. Returnează %1 dacă elementul nu este găsit.";
Blockly.Msg["LISTS_INLIST"] = "în listă";
Blockly.Msg["LISTS_ISEMPTY_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#is-empty";  // untranslated
Blockly.Msg["LISTS_ISEMPTY_TITLE"] = "%1 este gol";
Blockly.Msg["LISTS_ISEMPTY_TOOLTIP"] = "Returnează adevărat dacă lista este goală.";
Blockly.Msg["LISTS_LENGTH_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#length-of";  // untranslated
Blockly.Msg["LISTS_LENGTH_TITLE"] = "lungime de %1";
Blockly.Msg["LISTS_LENGTH_TOOLTIP"] = "Returnează lungimea unei liste.";
Blockly.Msg["LISTS_REPEAT_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#create-list-with";  // untranslated
Blockly.Msg["LISTS_REPEAT_TITLE"] = "creaza lista cu %1 elemente repetate de %2 ori";
Blockly.Msg["LISTS_REPEAT_TOOLTIP"] = "Creează o listă alcătuită dintr-o anumită valoare repetată de numărul specificat de ori.";
Blockly.Msg["LISTS_REVERSE_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#reversing-a-list";  // untranslated
Blockly.Msg["LISTS_REVERSE_MESSAGE0"] = "inversă %1";
Blockly.Msg["LISTS_REVERSE_TOOLTIP"] = "Inversați copia unei liste.";
Blockly.Msg["LISTS_SET_INDEX_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#in-list--set";  // untranslated
Blockly.Msg["LISTS_SET_INDEX_INPUT_TO"] = "ca";
Blockly.Msg["LISTS_SET_INDEX_INSERT"] = "introduceți la";
Blockly.Msg["LISTS_SET_INDEX_SET"] = "seteaza";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_INSERT_FIRST"] = "Inserează elementul la începutul unei liste.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_INSERT_FROM"] = "Inserează elementul la poziția specificată într-o listă.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_INSERT_LAST"] = "Adaugă elementul la sfârșitul unei liste.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_INSERT_RANDOM"] = "Inserează elementul aleatoriu într-o listă.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_SET_FIRST"] = "Setează primul element într-o listă.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_SET_FROM"] = "Setează elementul de la poziția specificată dintr-o listă.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_SET_LAST"] = "Setează ultimul element într-o listă.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_SET_RANDOM"] = "Setează un element aleator într-o listă.";
Blockly.Msg["LISTS_SORT_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#sorting-a-list";
Blockly.Msg["LISTS_SORT_ORDER_ASCENDING"] = "crescător";
Blockly.Msg["LISTS_SORT_ORDER_DESCENDING"] = "descrescător";
Blockly.Msg["LISTS_SORT_TITLE"] = "sortați %1 %2 %3";
Blockly.Msg["LISTS_SORT_TOOLTIP"] = "Sortați o copie a unei liste.";
Blockly.Msg["LISTS_SORT_TYPE_IGNORECASE"] = "alfabetic, ignorați cazul";
Blockly.Msg["LISTS_SORT_TYPE_NUMERIC"] = "numeric";
Blockly.Msg["LISTS_SORT_TYPE_TEXT"] = "alfabetic";
Blockly.Msg["LISTS_SPLIT_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#splitting-strings-and-joining-lists";  // untranslated
Blockly.Msg["LISTS_SPLIT_LIST_FROM_TEXT"] = "convertește textul în listă";
Blockly.Msg["LISTS_SPLIT_TEXT_FROM_LIST"] = "convertește lista în text";
Blockly.Msg["LISTS_SPLIT_TOOLTIP_JOIN"] = "Concatenează o listă de texte (alternate cu separatorul) într-un text unic";
Blockly.Msg["LISTS_SPLIT_TOOLTIP_SPLIT"] = "Împarte textul într-o listă de texte, despărțite prin fiecare separator";
Blockly.Msg["LISTS_SPLIT_WITH_DELIMITER"] = "cu separatorul";
Blockly.Msg["LOGIC_BOOLEAN_FALSE"] = "fals";
Blockly.Msg["LOGIC_BOOLEAN_HELPURL"] = "https://github.com/google/blockly/wiki/Logic#values";  // untranslated
Blockly.Msg["LOGIC_BOOLEAN_TOOLTIP"] = "Returnează adevărat sau fals.";
Blockly.Msg["LOGIC_BOOLEAN_TRUE"] = "adevărat";
Blockly.Msg["LOGIC_COMPARE_HELPURL"] = "https://en.wikipedia.org/wiki/Inequality_(mathematics)";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_EQ"] = "Returnează adevărat dacă ambele intrări sunt egale.";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_GT"] = "Returnează adevărat dacă prima intrare este mai mare decât a doua intrare.";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_GTE"] = "Returnează adevărat dacă prima intrare este mai mare sau egală cu a doua intrare.";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_LT"] = "Returnează adevărat dacă prima intrare este mai mică decât a doua intrare.";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_LTE"] = "Returnează adevărat dacă prima intrare este mai mică sau egală cu a doua intrare.";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_NEQ"] = "Returnează adevărat daca cele două intrări nu sunt egale.";
Blockly.Msg["LOGIC_NEGATE_HELPURL"] = "https://github.com/google/blockly/wiki/Logic#not";  // untranslated
Blockly.Msg["LOGIC_NEGATE_TITLE"] = "non %1";
Blockly.Msg["LOGIC_NEGATE_TOOLTIP"] = "Returnează adevărat dacă intrarea este falsă.  Returnează fals dacă intrarea este adevărată.";
Blockly.Msg["LOGIC_NULL"] = "nul";
Blockly.Msg["LOGIC_NULL_HELPURL"] = "https://en.wikipedia.org/wiki/Nullable_type";
Blockly.Msg["LOGIC_NULL_TOOLTIP"] = "returnează nul.";
Blockly.Msg["LOGIC_OPERATION_AND"] = "și";
Blockly.Msg["LOGIC_OPERATION_HELPURL"] = "https://github.com/google/blockly/wiki/Logic#logical-operations";  // untranslated
Blockly.Msg["LOGIC_OPERATION_OR"] = "sau";
Blockly.Msg["LOGIC_OPERATION_TOOLTIP_AND"] = "Returnează adevărat daca ambele intrări sunt adevărate.";
Blockly.Msg["LOGIC_OPERATION_TOOLTIP_OR"] = "Returnează adevărat dacă cel puțin una din intrări este adevărată.";
Blockly.Msg["LOGIC_TERNARY_CONDITION"] = "test";
Blockly.Msg["LOGIC_TERNARY_HELPURL"] = "https://en.wikipedia.org/wiki/%3F:";
Blockly.Msg["LOGIC_TERNARY_IF_FALSE"] = "dacă este fals";
Blockly.Msg["LOGIC_TERNARY_IF_TRUE"] = "dacă este adevărat";
Blockly.Msg["LOGIC_TERNARY_TOOLTIP"] = "Verifică condiția din „test”. Dacă condiția este adevărată, returnează valoarea „în cazul în care adevărat”; în caz contrar întoarce valoarea „în cazul în care e fals”.";
Blockly.Msg["MATH_ADDITION_SYMBOL"] = "+";
Blockly.Msg["MATH_ARITHMETIC_HELPURL"] = "https://ro.wikipedia.org/wiki/Aritmetic%C4%83";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_ADD"] = "Returnează suma a două numere.";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_DIVIDE"] = "Returnează câtul celor două numere.";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_MINUS"] = "Returnează diferența dintre cele două numere.";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_MULTIPLY"] = "Returnează produsul celor două numere.";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_POWER"] = "Returneaza numărul rezultat prin ridicarea primului număr la puterea celui de-al doilea.";
Blockly.Msg["MATH_ATAN2_HELPURL"] = "https://en.wikipedia.org/wiki/Atan2";
Blockly.Msg["MATH_ATAN2_TITLE"] = "atan2 of X:%1 Y:%2";
Blockly.Msg["MATH_ATAN2_TOOLTIP"] = "Întoarceți arctangentul punctului (X, Y) în grade de la -180 la 180.";
Blockly.Msg["MATH_CHANGE_HELPURL"] = "https://en.wikipedia.org/wiki/Programming_idiom#Incrementing_a_counter";
Blockly.Msg["MATH_CHANGE_TITLE"] = "schimbă %1 de %2";
Blockly.Msg["MATH_CHANGE_TOOLTIP"] = "Adaugă un număr variabilei '%1'.";
Blockly.Msg["MATH_CONSTANT_HELPURL"] = "https://ro.wikipedia.org/wiki/Constant%C4%83_matematic%C4%83";
Blockly.Msg["MATH_CONSTANT_TOOLTIP"] = "Întoarcă una din constantele comune: π (3.141...), e (2.718...), φ (1,618...), sqrt(2) (1.414...), sqrt(½) (0.707...) sau ∞ (infinitate).";
Blockly.Msg["MATH_CONSTRAIN_HELPURL"] = "https://en.wikipedia.org/wiki/Clamping_(graphics)";  // untranslated
Blockly.Msg["MATH_CONSTRAIN_TITLE"] = "constrânge %1 redus %2 ridicat %3";
Blockly.Msg["MATH_CONSTRAIN_TOOLTIP"] = "Constrânge un număr să fie între limitele specificate (inclusiv).";
Blockly.Msg["MATH_DIVISION_SYMBOL"] = "÷";
Blockly.Msg["MATH_IS_DIVISIBLE_BY"] = "este divizibil cu";
Blockly.Msg["MATH_IS_EVEN"] = "este par";
Blockly.Msg["MATH_IS_NEGATIVE"] = "este negativ";
Blockly.Msg["MATH_IS_ODD"] = "este impar";
Blockly.Msg["MATH_IS_POSITIVE"] = "este pozitiv";
Blockly.Msg["MATH_IS_PRIME"] = "este prim";
Blockly.Msg["MATH_IS_TOOLTIP"] = "Verifică dacă un număr este un par, impar, prim, întreg, pozitiv, negativ, sau dacă este divizibil cu un anumit număr.  Returnează true sau false.";
Blockly.Msg["MATH_IS_WHOLE"] = "este întreg";
Blockly.Msg["MATH_MODULO_HELPURL"] = "https://en.wikipedia.org/wiki/Modulo_operation";
Blockly.Msg["MATH_MODULO_TITLE"] = "restul la %1 ÷ %2";
Blockly.Msg["MATH_MODULO_TOOLTIP"] = "Întoarce restul din împărțirea celor două numere.";
Blockly.Msg["MATH_MULTIPLICATION_SYMBOL"] = "×";
Blockly.Msg["MATH_NUMBER_HELPURL"] = "https://en.wikipedia.org/wiki/Number";
Blockly.Msg["MATH_NUMBER_TOOLTIP"] = "Un număr.";
Blockly.Msg["MATH_ONLIST_HELPURL"] = "";  // untranslated
Blockly.Msg["MATH_ONLIST_OPERATOR_AVERAGE"] = "media listei";
Blockly.Msg["MATH_ONLIST_OPERATOR_MAX"] = "maximul listei";
Blockly.Msg["MATH_ONLIST_OPERATOR_MEDIAN"] = "media listei";
Blockly.Msg["MATH_ONLIST_OPERATOR_MIN"] = "minimul listei";
Blockly.Msg["MATH_ONLIST_OPERATOR_MODE"] = "moduri de listă";
Blockly.Msg["MATH_ONLIST_OPERATOR_RANDOM"] = "element aleatoriu din lista";
Blockly.Msg["MATH_ONLIST_OPERATOR_STD_DEV"] = "deviația standard a listei";
Blockly.Msg["MATH_ONLIST_OPERATOR_SUM"] = "suma listei";
Blockly.Msg["MATH_ONLIST_TOOLTIP_AVERAGE"] = "Întoarce media (aritmetică) a valorilor numerice în listă.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_MAX"] = "Întoarce cel mai mare număr din listă.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_MEDIAN"] = "Întoarce numărul median în listă.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_MIN"] = "Returnează cel mai mic număr din listă.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_MODE"] = "Returnează o listă cu cel(e) mai frecvent(e) element(e) din listă.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_RANDOM"] = "Returnează un element aleatoriu din listă.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_STD_DEV"] = "Întoarce deviația standard a listei.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_SUM"] = "Returnează suma tuturor numerelor din lista.";
Blockly.Msg["MATH_POWER_SYMBOL"] = "^";
Blockly.Msg["MATH_RANDOM_FLOAT_HELPURL"] = "https://en.wikipedia.org/wiki/Random_number_generation";
Blockly.Msg["MATH_RANDOM_FLOAT_TITLE_RANDOM"] = "fracții aleatorii";
Blockly.Msg["MATH_RANDOM_FLOAT_TOOLTIP"] = "Returnează o fracție aleatoare între 0.0 (inclusiv) și 1.0 (exclusiv).";
Blockly.Msg["MATH_RANDOM_INT_HELPURL"] = "https://en.wikipedia.org/wiki/Random_number_generation";
Blockly.Msg["MATH_RANDOM_INT_TITLE"] = "un număr întreg aleator de la %1 la %2";
Blockly.Msg["MATH_RANDOM_INT_TOOLTIP"] = "Returnează un număr întreg aleator aflat între cele două limite specificate, inclusiv.";
Blockly.Msg["MATH_ROUND_HELPURL"] = "https://en.wikipedia.org/wiki/Rounding";
Blockly.Msg["MATH_ROUND_OPERATOR_ROUND"] = "rotund";
Blockly.Msg["MATH_ROUND_OPERATOR_ROUNDDOWN"] = "rotunjit";
Blockly.Msg["MATH_ROUND_OPERATOR_ROUNDUP"] = "rotunjește în sus";
Blockly.Msg["MATH_ROUND_TOOLTIP"] = "Rotunjirea unui număr în sus sau în jos.";
Blockly.Msg["MATH_SINGLE_HELPURL"] = "https://en.wikipedia.org/wiki/Square_root";
Blockly.Msg["MATH_SINGLE_OP_ABSOLUTE"] = "absolută";
Blockly.Msg["MATH_SINGLE_OP_ROOT"] = "rădăcina pătrată";
Blockly.Msg["MATH_SINGLE_TOOLTIP_ABS"] = "Returnează valoarea absolută a unui număr.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_EXP"] = "Returnează e la puterea unui număr.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_LN"] = "Întoarce logaritmul natural al unui număr.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_LOG10"] = "Returnează logaritmul în baza 10 a unui număr.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_NEG"] = "Returnează negația unui număr.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_POW10"] = "Returnează 10 la puterea unui număr.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_ROOT"] = "Returnează rădăcina pătrată a unui număr.";
Blockly.Msg["MATH_SUBTRACTION_SYMBOL"] = "-";
Blockly.Msg["MATH_TRIG_ACOS"] = "arccos";
Blockly.Msg["MATH_TRIG_ASIN"] = "arcsin";
Blockly.Msg["MATH_TRIG_ATAN"] = "arctg";
Blockly.Msg["MATH_TRIG_COS"] = "cos";
Blockly.Msg["MATH_TRIG_HELPURL"] = "https://ro.wikipedia.org/wiki/Funcții_trigonometrice";
Blockly.Msg["MATH_TRIG_SIN"] = "sin";
Blockly.Msg["MATH_TRIG_TAN"] = "tg";
Blockly.Msg["MATH_TRIG_TOOLTIP_ACOS"] = "Returnează arccosinusul unui număr.";
Blockly.Msg["MATH_TRIG_TOOLTIP_ASIN"] = "Returnează arcsinusul unui număr.";
Blockly.Msg["MATH_TRIG_TOOLTIP_ATAN"] = "Returnează arctangenta unui număr.";
Blockly.Msg["MATH_TRIG_TOOLTIP_COS"] = "Întoarce cosinusul unui grad (nu radianul).";
Blockly.Msg["MATH_TRIG_TOOLTIP_SIN"] = "Întoarce cosinusul unui grad (nu radianul).";
Blockly.Msg["MATH_TRIG_TOOLTIP_TAN"] = "Întoarce tangenta unui grad (nu radianul).";
Blockly.Msg["NEW_COLOUR_VARIABLE"] = "Crează o variabilă culoare";
Blockly.Msg["NEW_NUMBER_VARIABLE"] = "Crează o variabilă număr";
Blockly.Msg["NEW_STRING_VARIABLE"] = "Crează o variabilă string";
Blockly.Msg["NEW_VARIABLE"] = "Crează variabilă";
Blockly.Msg["NEW_VARIABLE_TITLE"] = "Noul nume de variabilă:";
Blockly.Msg["NEW_VARIABLE_TYPE_TITLE"] = "Tip nou de variabilă";
Blockly.Msg["ORDINAL_NUMBER_SUFFIX"] = "";  // untranslated
Blockly.Msg["PROCEDURES_ALLOW_STATEMENTS"] = "permite declarațiile";
Blockly.Msg["PROCEDURES_BEFORE_PARAMS"] = "cu:";
Blockly.Msg["PROCEDURES_CALLNORETURN_HELPURL"] = "https://en.wikipedia.org/wiki/Subroutine";
Blockly.Msg["PROCEDURES_CALLNORETURN_TOOLTIP"] = "Executați funcția '%1 'definită de utilizator.";
Blockly.Msg["PROCEDURES_CALLRETURN_HELPURL"] = "https://en.wikipedia.org/wiki/Subroutine";
Blockly.Msg["PROCEDURES_CALLRETURN_TOOLTIP"] = "Executați funcția „%1” definită de utilizator și folosiți producția sa.";
Blockly.Msg["PROCEDURES_CALL_BEFORE_PARAMS"] = "cu:";
Blockly.Msg["PROCEDURES_CREATE_DO"] = "Creați „%1”";
Blockly.Msg["PROCEDURES_DEFNORETURN_COMMENT"] = "Descrieți această funcție ...";
Blockly.Msg["PROCEDURES_DEFNORETURN_DO"] = "";  // untranslated
Blockly.Msg["PROCEDURES_DEFNORETURN_HELPURL"] = "https://en.wikipedia.org/wiki/Procedure_%28computer_science%29";
Blockly.Msg["PROCEDURES_DEFNORETURN_PROCEDURE"] = "fă ceva";
Blockly.Msg["PROCEDURES_DEFNORETURN_TITLE"] = "la";
Blockly.Msg["PROCEDURES_DEFNORETURN_TOOLTIP"] = "Crează o funcție cu nicio ieșire.";
Blockly.Msg["PROCEDURES_DEFRETURN_HELPURL"] = "https://ro.wikipedia.org/wiki/Subrutină";
Blockly.Msg["PROCEDURES_DEFRETURN_RETURN"] = "returnează";
Blockly.Msg["PROCEDURES_DEFRETURN_TOOLTIP"] = "Creează o funcție cu o ieșire.";
Blockly.Msg["PROCEDURES_DEF_DUPLICATE_WARNING"] = "Atenție: Această funcție are parametri duplicați.";
Blockly.Msg["PROCEDURES_HIGHLIGHT_DEF"] = "Evidențiază definiția funcției";
Blockly.Msg["PROCEDURES_IFRETURN_HELPURL"] = "http://c2.com/cgi/wiki?GuardClause";  // untranslated
Blockly.Msg["PROCEDURES_IFRETURN_TOOLTIP"] = "Dacă o valoare este adevărată, atunci returnează valoarea a doua.";
Blockly.Msg["PROCEDURES_IFRETURN_WARNING"] = "Avertisment: Acest bloc poate fi utilizat numai în definiția unei funcții.";
Blockly.Msg["PROCEDURES_MUTATORARG_TITLE"] = "nume de intrare:";
Blockly.Msg["PROCEDURES_MUTATORARG_TOOLTIP"] = "Adaugă un parametru de intrare pentru funcție.";
Blockly.Msg["PROCEDURES_MUTATORCONTAINER_TITLE"] = "intrări";
Blockly.Msg["PROCEDURES_MUTATORCONTAINER_TOOLTIP"] = "Adăugă, șterge sau reordonează parametrii de intrare ai acestei funcții.";
Blockly.Msg["REDO"] = "Refă";
Blockly.Msg["REMOVE_COMMENT"] = "Elimină comentariu";
Blockly.Msg["RENAME_VARIABLE"] = "Redenumirea variabilei...";
Blockly.Msg["RENAME_VARIABLE_TITLE"] = "Redenumește toate variabilele „%1” în:";
Blockly.Msg["TEXT_APPEND_HELPURL"] = "https://github.com/google/blockly/wiki/Text#text-modification";  // untranslated
Blockly.Msg["TEXT_APPEND_TITLE"] = "la %1 adăugați text %2";
Blockly.Msg["TEXT_APPEND_TOOLTIP"] = "Adăugați text la variabila „%1”.";
Blockly.Msg["TEXT_CHANGECASE_HELPURL"] = "https://github.com/google/blockly/wiki/Text#adjusting-text-case";  // untranslated
Blockly.Msg["TEXT_CHANGECASE_OPERATOR_LOWERCASE"] = "la litere mici";
Blockly.Msg["TEXT_CHANGECASE_OPERATOR_TITLECASE"] = "către Titlul de caz";
Blockly.Msg["TEXT_CHANGECASE_OPERATOR_UPPERCASE"] = "la MAJUSCULE";
Blockly.Msg["TEXT_CHANGECASE_TOOLTIP"] = "Întoarce o copie a textului într-un caz diferit.";
Blockly.Msg["TEXT_CHARAT_FIRST"] = "obține prima literă";
Blockly.Msg["TEXT_CHARAT_FROM_END"] = "obține litera # de la sfârșit";
Blockly.Msg["TEXT_CHARAT_FROM_START"] = "obtine litera #";
Blockly.Msg["TEXT_CHARAT_HELPURL"] = "https://github.com/google/blockly/wiki/Text#extracting-text";  // untranslated
Blockly.Msg["TEXT_CHARAT_LAST"] = "obține o literă oarecare";
Blockly.Msg["TEXT_CHARAT_RANDOM"] = "obtine o litera oarecare";
Blockly.Msg["TEXT_CHARAT_TAIL"] = "";  // untranslated
Blockly.Msg["TEXT_CHARAT_TITLE"] = "în text %1 %2";
Blockly.Msg["TEXT_CHARAT_TOOLTIP"] = "Returnează litera la poziția specificată.";
Blockly.Msg["TEXT_COUNT_HELPURL"] = "https://github.com/google/blockly/wiki/Text#counting-substrings";  // untranslated
Blockly.Msg["TEXT_COUNT_MESSAGE0"] = "numără %1 in %2";
Blockly.Msg["TEXT_COUNT_TOOLTIP"] = "Aflați de câte ori apare un text într-un alt text.";
Blockly.Msg["TEXT_CREATE_JOIN_ITEM_TOOLTIP"] = "Adaugă un element în text.";
Blockly.Msg["TEXT_CREATE_JOIN_TITLE_JOIN"] = "alăturați-vă";
Blockly.Msg["TEXT_CREATE_JOIN_TOOLTIP"] = "Adaugă, elimină sau reordonează secțiuni ca să reconfigureze blocul text.";
Blockly.Msg["TEXT_GET_SUBSTRING_END_FROM_END"] = "la litera # de la sfarsit";
Blockly.Msg["TEXT_GET_SUBSTRING_END_FROM_START"] = "la litera #";
Blockly.Msg["TEXT_GET_SUBSTRING_END_LAST"] = "la ultima literă";
Blockly.Msg["TEXT_GET_SUBSTRING_HELPURL"] = "https://github.com/google/blockly/wiki/Text#extracting-a-region-of-text";  // untranslated
Blockly.Msg["TEXT_GET_SUBSTRING_INPUT_IN_TEXT"] = "în text";
Blockly.Msg["TEXT_GET_SUBSTRING_START_FIRST"] = "obține un subșir de la prima literă";
Blockly.Msg["TEXT_GET_SUBSTRING_START_FROM_END"] = "obține un subșir de la litera # de la sfârșit";
Blockly.Msg["TEXT_GET_SUBSTRING_START_FROM_START"] = "obține subșir de la litera #";
Blockly.Msg["TEXT_GET_SUBSTRING_TAIL"] = "";  // untranslated
Blockly.Msg["TEXT_GET_SUBSTRING_TOOLTIP"] = "Returnează o anumită parte din text.";
Blockly.Msg["TEXT_INDEXOF_HELPURL"] = "https://github.com/google/blockly/wiki/Text#finding-text";  // untranslated
Blockly.Msg["TEXT_INDEXOF_OPERATOR_FIRST"] = "găsește prima apariție a textului";
Blockly.Msg["TEXT_INDEXOF_OPERATOR_LAST"] = "găsește ultima apariție a textului";
Blockly.Msg["TEXT_INDEXOF_TITLE"] = "în text %1 %2 %3";
Blockly.Msg["TEXT_INDEXOF_TOOLTIP"] = "Returnează indicele primei/ultimei apariții din primul text în al doilea text. Returnează %1 dacă textul nu este găsit.";
Blockly.Msg["TEXT_ISEMPTY_HELPURL"] = "https://github.com/google/blockly/wiki/Text#checking-for-empty-text";  // untranslated
Blockly.Msg["TEXT_ISEMPTY_TITLE"] = "%1 este gol";
Blockly.Msg["TEXT_ISEMPTY_TOOLTIP"] = "Returnează adevărat dacă textul furnizat este gol.";
Blockly.Msg["TEXT_JOIN_HELPURL"] = "https://github.com/google/blockly/wiki/Text#text-creation";  // untranslated
Blockly.Msg["TEXT_JOIN_TITLE_CREATEWITH"] = "crează text cu";
Blockly.Msg["TEXT_JOIN_TOOLTIP"] = "Creați o bucată de text prin unirea oricărui număr de elemente.";
Blockly.Msg["TEXT_LENGTH_HELPURL"] = "https://github.com/google/blockly/wiki/Text#text-modification";  // untranslated
Blockly.Msg["TEXT_LENGTH_TITLE"] = "lungime de %1";
Blockly.Msg["TEXT_LENGTH_TOOLTIP"] = "Returnează numărul de litere (inclusiv spațiile) în textul furnizat.";
Blockly.Msg["TEXT_PRINT_HELPURL"] = "https://github.com/google/blockly/wiki/Text#printing-text";  // untranslated
Blockly.Msg["TEXT_PRINT_TITLE"] = "imprimare %1";
Blockly.Msg["TEXT_PRINT_TOOLTIP"] = "Afișează textul specificat, numărul sau altă valoare.";
Blockly.Msg["TEXT_PROMPT_HELPURL"] = "https://github.com/google/blockly/wiki/Text#getting-input-from-the-user";  // untranslated
Blockly.Msg["TEXT_PROMPT_TOOLTIP_NUMBER"] = "Solicită utilizatorul pentru un număr.";
Blockly.Msg["TEXT_PROMPT_TOOLTIP_TEXT"] = "Solicită utilizatorul pentru text.";
Blockly.Msg["TEXT_PROMPT_TYPE_NUMBER"] = "solicită pentru număr cu mesaj";
Blockly.Msg["TEXT_PROMPT_TYPE_TEXT"] = "solicită pentru text cu mesaj";
Blockly.Msg["TEXT_REPLACE_HELPURL"] = "https://github.com/google/blockly/wiki/Text#replacing-substrings";  // untranslated
Blockly.Msg["TEXT_REPLACE_MESSAGE0"] = "înlocuiți %1 cu %2 în %3";
Blockly.Msg["TEXT_REPLACE_TOOLTIP"] = "Înlocuiți toate aparițiile anumitor texte într-un alt text.";
Blockly.Msg["TEXT_REVERSE_HELPURL"] = "https://github.com/google/blockly/wiki/Text#reversing-text";  // untranslated
Blockly.Msg["TEXT_REVERSE_MESSAGE0"] = "inversă %1";
Blockly.Msg["TEXT_REVERSE_TOOLTIP"] = "Inversează ordinea caracterelor din text.";
Blockly.Msg["TEXT_TEXT_HELPURL"] = "https://en.wikipedia.org/wiki/String_(computer_science)";
Blockly.Msg["TEXT_TEXT_TOOLTIP"] = "O literă, cuvânt sau linie de text.";
Blockly.Msg["TEXT_TRIM_HELPURL"] = "https://github.com/google/blockly/wiki/Text#trimming-removing-spaces";  // untranslated
Blockly.Msg["TEXT_TRIM_OPERATOR_BOTH"] = "taie spațiile de pe ambele părți ale";
Blockly.Msg["TEXT_TRIM_OPERATOR_LEFT"] = "tăiați spațiile din partea stângă a";
Blockly.Msg["TEXT_TRIM_OPERATOR_RIGHT"] = "taie spațiile din partea dreaptă a";
Blockly.Msg["TEXT_TRIM_TOOLTIP"] = "Returnează o copie a textului fără spațiile de la unul sau ambele capete.";
Blockly.Msg["TODAY"] = "Astăzi";
Blockly.Msg["UNDO"] = "Anulează";
Blockly.Msg["UNNAMED_KEY"] = "unnamed";  // untranslated
Blockly.Msg["VARIABLES_DEFAULT_NAME"] = "element";
Blockly.Msg["VARIABLES_GET_CREATE_SET"] = "Crează 'set %1'";
Blockly.Msg["VARIABLES_GET_HELPURL"] = "https://github.com/google/blockly/wiki/Variables#get";  // untranslated
Blockly.Msg["VARIABLES_GET_TOOLTIP"] = "Returnează valoarea acestei variabile.";
Blockly.Msg["VARIABLES_SET"] = "seteaza %1 la %2";
Blockly.Msg["VARIABLES_SET_CREATE_GET"] = "Crează 'get %1'";
Blockly.Msg["VARIABLES_SET_HELPURL"] = "https://github.com/google/blockly/wiki/Variables#set";  // untranslated
Blockly.Msg["VARIABLES_SET_TOOLTIP"] = "Setează această variabilă sa fie egală la intrare.";
Blockly.Msg["VARIABLE_ALREADY_EXISTS"] = "O variabilă cu numele '%1'  există deja.";
Blockly.Msg["VARIABLE_ALREADY_EXISTS_FOR_ANOTHER_TYPE"] = "o variabilă numită '%1' există deja pentru alt tip: '%2'.";
Blockly.Msg["WORKSPACE_ARIA_LABEL"] = "Blockly Workspace";  // untranslated
Blockly.Msg["WORKSPACE_COMMENT_DEFAULT_TEXT"] = "Spune ceva...";
Blockly.Msg["CONTROLS_FOREACH_INPUT_DO"] = Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"];
Blockly.Msg["CONTROLS_FOR_INPUT_DO"] = Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"];
Blockly.Msg["CONTROLS_IF_ELSEIF_TITLE_ELSEIF"] = Blockly.Msg["CONTROLS_IF_MSG_ELSEIF"];
Blockly.Msg["CONTROLS_IF_ELSE_TITLE_ELSE"] = Blockly.Msg["CONTROLS_IF_MSG_ELSE"];
Blockly.Msg["CONTROLS_IF_IF_TITLE_IF"] = Blockly.Msg["CONTROLS_IF_MSG_IF"];
Blockly.Msg["CONTROLS_IF_MSG_THEN"] = Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"];
Blockly.Msg["CONTROLS_WHILEUNTIL_INPUT_DO"] = Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"];
Blockly.Msg["LISTS_CREATE_WITH_ITEM_TITLE"] = Blockly.Msg["VARIABLES_DEFAULT_NAME"];
Blockly.Msg["LISTS_GET_INDEX_HELPURL"] = Blockly.Msg["LISTS_INDEX_OF_HELPURL"];
Blockly.Msg["LISTS_GET_INDEX_INPUT_IN_LIST"] = Blockly.Msg["LISTS_INLIST"];
Blockly.Msg["LISTS_GET_SUBLIST_INPUT_IN_LIST"] = Blockly.Msg["LISTS_INLIST"];
Blockly.Msg["LISTS_INDEX_OF_INPUT_IN_LIST"] = Blockly.Msg["LISTS_INLIST"];
Blockly.Msg["LISTS_SET_INDEX_INPUT_IN_LIST"] = Blockly.Msg["LISTS_INLIST"];
Blockly.Msg["MATH_CHANGE_TITLE_ITEM"] = Blockly.Msg["VARIABLES_DEFAULT_NAME"];
Blockly.Msg["PROCEDURES_DEFRETURN_COMMENT"] = Blockly.Msg["PROCEDURES_DEFNORETURN_COMMENT"];
Blockly.Msg["PROCEDURES_DEFRETURN_DO"] = Blockly.Msg["PROCEDURES_DEFNORETURN_DO"];
Blockly.Msg["PROCEDURES_DEFRETURN_PROCEDURE"] = Blockly.Msg["PROCEDURES_DEFNORETURN_PROCEDURE"];
Blockly.Msg["PROCEDURES_DEFRETURN_TITLE"] = Blockly.Msg["PROCEDURES_DEFNORETURN_TITLE"];
Blockly.Msg["TEXT_APPEND_VARIABLE"] = Blockly.Msg["VARIABLES_DEFAULT_NAME"];
Blockly.Msg["TEXT_CREATE_JOIN_ITEM_TITLE_ITEM"] = Blockly.Msg["VARIABLES_DEFAULT_NAME"];

Blockly.Msg["MATH_HUE"] = "230";
Blockly.Msg["LOOPS_HUE"] = "120";
Blockly.Msg["LISTS_HUE"] = "260";
Blockly.Msg["LOGIC_HUE"] = "210";
Blockly.Msg["VARIABLES_HUE"] = "330";
Blockly.Msg["TEXTS_HUE"] = "160";
Blockly.Msg["PROCEDURES_HUE"] = "290";
Blockly.Msg["COLOUR_HUE"] = "20";
Blockly.Msg["VARIABLES_DYNAMIC_HUE"] = "310";