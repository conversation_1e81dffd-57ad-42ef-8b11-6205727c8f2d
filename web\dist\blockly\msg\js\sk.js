// This file was automatically generated.  Do not modify.

'use strict';

Blockly.Msg["ADD_COMMENT"] = "Pridať komentár";
Blockly.Msg["CANNOT_DELETE_VARIABLE_PROCEDURE"] = "Nie je možné zmazať premennú „%1“, pretože je súčasťou definície funkcie „%2“";
Blockly.Msg["CHANGE_VALUE_TITLE"] = "Zmeniť hodnotu:";
Blockly.Msg["CLEAN_UP"] = "Narovnať bloky";
Blockly.Msg["COLLAPSED_WARNINGS_WARNING"] = "Zrútené bloky obsahujú varovanie.";
Blockly.Msg["COLLAPSE_ALL"] = "Zvinúť bloky";
Blockly.Msg["COLLAPSE_BLOCK"] = "Zvinúť blok";
Blockly.Msg["COLOUR_BLEND_COLOUR1"] = "farba 1";
Blockly.Msg["COLOUR_BLEND_COLOUR2"] = "farba 2";
Blockly.Msg["COLOUR_BLEND_HELPURL"] = "https://meyerweb.com/eric/tools/color-blend/#:::rgbp";  // untranslated
Blockly.Msg["COLOUR_BLEND_RATIO"] = "pomer";
Blockly.Msg["COLOUR_BLEND_TITLE"] = "zmiešať";
Blockly.Msg["COLOUR_BLEND_TOOLTIP"] = "Zmieša dve farby v danom pomere (0.0 - 1.0).";
Blockly.Msg["COLOUR_PICKER_HELPURL"] = "https://en.wikipedia.org/wiki/Color";
Blockly.Msg["COLOUR_PICKER_TOOLTIP"] = "Zvoľte farbu z palety.";
Blockly.Msg["COLOUR_RANDOM_HELPURL"] = "http://randomcolour.com";  // untranslated
Blockly.Msg["COLOUR_RANDOM_TITLE"] = "náhodná farba";
Blockly.Msg["COLOUR_RANDOM_TOOLTIP"] = "Zvoliť farbu náhodne.";
Blockly.Msg["COLOUR_RGB_BLUE"] = "modrá";
Blockly.Msg["COLOUR_RGB_GREEN"] = "zelená";
Blockly.Msg["COLOUR_RGB_HELPURL"] = "https://www.december.com/html/spec/colorpercompact.html";  // untranslated
Blockly.Msg["COLOUR_RGB_RED"] = "červená";
Blockly.Msg["COLOUR_RGB_TITLE"] = "ofarbiť s";
Blockly.Msg["COLOUR_RGB_TOOLTIP"] = "Vytvoriť farbu pomocou zadaného množstva červenej, zelenej a modrej. Množstvo musí byť medzi 0 a 100.";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_HELPURL"] = "https://github.com/google/blockly/wiki/Loops#loop-termination-blocks";  // untranslated
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_OPERATOR_BREAK"] = "opustiť slučku";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_OPERATOR_CONTINUE"] = "prejdi na nasledujúce opakovanie slučky";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_TOOLTIP_BREAK"] = "Opustiť túto slučku.";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_TOOLTIP_CONTINUE"] = "Vynechať zvyšok tejto slučky a pokračovať ďalším opakovaním.";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_WARNING"] = "Upozornenie: Tento blok sa môže používať len v rámci slučky.";
Blockly.Msg["CONTROLS_FOREACH_HELPURL"] = "https://github.com/google/blockly/wiki/Loops#for-each";  // untranslated
Blockly.Msg["CONTROLS_FOREACH_TITLE"] = "pre každý prvok %1 v zozname %2";
Blockly.Msg["CONTROLS_FOREACH_TOOLTIP"] = "Pre každý prvok v zozname priraď jeho hodnotu do premenej '%1' a vykonaj príkazy.";
Blockly.Msg["CONTROLS_FOR_HELPURL"] = "https://github.com/google/blockly/wiki/Loops#count-with";  // untranslated
Blockly.Msg["CONTROLS_FOR_TITLE"] = "počítať s %1 od %2 do %3 o %4";
Blockly.Msg["CONTROLS_FOR_TOOLTIP"] = "Nechá premennú '%1' nadobúdať hodnoty od začiatočného čísla po konečné s daným medzikrokom a vykoná zadané bloky.";
Blockly.Msg["CONTROLS_IF_ELSEIF_TOOLTIP"] = "Pridať podmienku k \"ak\" bloku.";
Blockly.Msg["CONTROLS_IF_ELSE_TOOLTIP"] = "Pridať poslednú záchytnú podmienku k \"ak\" bloku.";
Blockly.Msg["CONTROLS_IF_HELPURL"] = "https://github.com/google/blockly/wiki/IfElse";  // untranslated
Blockly.Msg["CONTROLS_IF_IF_TOOLTIP"] = "Pridať, odstrániť alebo zmeniť poradie oddielov tohto \"ak\" bloku.";
Blockly.Msg["CONTROLS_IF_MSG_ELSE"] = "inak";
Blockly.Msg["CONTROLS_IF_MSG_ELSEIF"] = "inak ak";
Blockly.Msg["CONTROLS_IF_MSG_IF"] = "ak";
Blockly.Msg["CONTROLS_IF_TOOLTIP_1"] = "Ak je hodnota pravda, vykonaj príkazy.";
Blockly.Msg["CONTROLS_IF_TOOLTIP_2"] = "Ak je hodnota pravda, vykonaj príkazy v prvom bloku. Inak vykonaj príkazy v druhom bloku.";
Blockly.Msg["CONTROLS_IF_TOOLTIP_3"] = "Ak je prvá hodnota pravda, vykonaj príkazy v prvom bloku. Inak, ak je druhá hodnota pravda, vykonaj príkazy v druhom bloku.";
Blockly.Msg["CONTROLS_IF_TOOLTIP_4"] = "Ak je prvá hodnota pravda, vykonaj príkazy v prvom bloku. Inak, ak je druhá hodnota pravda, vykonaj príkazy v druhom bloku. Ak ani jedna hodnota nie je pravda, vykonaj príkazy v poslednom bloku.";
Blockly.Msg["CONTROLS_REPEAT_HELPURL"] = "https://en.wikipedia.org/wiki/For_loop";
Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"] = "rob";
Blockly.Msg["CONTROLS_REPEAT_TITLE"] = "opakuj %1 krát";
Blockly.Msg["CONTROLS_REPEAT_TOOLTIP"] = "Opakuj určité príkazy viackrát.";
Blockly.Msg["CONTROLS_WHILEUNTIL_HELPURL"] = "https://github.com/google/blockly/wiki/Loops#repeat";  // untranslated
Blockly.Msg["CONTROLS_WHILEUNTIL_OPERATOR_UNTIL"] = "opakuj kým nebude";
Blockly.Msg["CONTROLS_WHILEUNTIL_OPERATOR_WHILE"] = "opakuj kým";
Blockly.Msg["CONTROLS_WHILEUNTIL_TOOLTIP_UNTIL"] = "Kým je hodnota nepravdivá, vykonávaj príkazy.";
Blockly.Msg["CONTROLS_WHILEUNTIL_TOOLTIP_WHILE"] = "Kým je hodnota pravdivá, vykonávaj príkazy.";
Blockly.Msg["DELETE_ALL_BLOCKS"] = "Zmazať všetkých %1 dielcov?";
Blockly.Msg["DELETE_BLOCK"] = "Odstrániť blok";
Blockly.Msg["DELETE_VARIABLE"] = "Odstrániť premennú '%1'";
Blockly.Msg["DELETE_VARIABLE_CONFIRMATION"] = "Odstrániť %1 použití premennej '%2'?";
Blockly.Msg["DELETE_X_BLOCKS"] = "Odstrániť %1 blokov";
Blockly.Msg["DIALOG_CANCEL"] = "Zrušiť";
Blockly.Msg["DIALOG_OK"] = "OK";
Blockly.Msg["DISABLE_BLOCK"] = "Vypnúť blok";
Blockly.Msg["DUPLICATE_BLOCK"] = "Duplikovať";
Blockly.Msg["DUPLICATE_COMMENT"] = "Duplicitný komentár";
Blockly.Msg["ENABLE_BLOCK"] = "Povoliť blok";
Blockly.Msg["EXPAND_ALL"] = "Rozvinúť bloky";
Blockly.Msg["EXPAND_BLOCK"] = "Rozvinúť blok";
Blockly.Msg["EXTERNAL_INPUTS"] = "Vonkajšie vstupy";
Blockly.Msg["HELP"] = "Pomoc";
Blockly.Msg["INLINE_INPUTS"] = "Riadkové vstupy";
Blockly.Msg["LISTS_CREATE_EMPTY_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#create-empty-list";  // untranslated
Blockly.Msg["LISTS_CREATE_EMPTY_TITLE"] = "prázdny zoznam";
Blockly.Msg["LISTS_CREATE_EMPTY_TOOLTIP"] = "Vráti zoznam nulovej dĺžky, ktorý neobsahuje žiadne prvky.";
Blockly.Msg["LISTS_CREATE_WITH_CONTAINER_TITLE_ADD"] = "zoznam";
Blockly.Msg["LISTS_CREATE_WITH_CONTAINER_TOOLTIP"] = "Pridaj, odstráň alebo zmeň poradie v tomto zoznamovom bloku.";
Blockly.Msg["LISTS_CREATE_WITH_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#create-list-with";
Blockly.Msg["LISTS_CREATE_WITH_INPUT_WITH"] = "vytvor zoznam s";
Blockly.Msg["LISTS_CREATE_WITH_ITEM_TOOLTIP"] = "Pridaj prvok do zoznamu.";
Blockly.Msg["LISTS_CREATE_WITH_TOOLTIP"] = "Vytvor zoznam s ľubovoľným počtom prvkov.";
Blockly.Msg["LISTS_GET_INDEX_FIRST"] = "prvý";
Blockly.Msg["LISTS_GET_INDEX_FROM_END"] = "# od konca";
Blockly.Msg["LISTS_GET_INDEX_FROM_START"] = "#";  // untranslated
Blockly.Msg["LISTS_GET_INDEX_GET"] = "zisti";
Blockly.Msg["LISTS_GET_INDEX_GET_REMOVE"] = "zisti a odstráň";
Blockly.Msg["LISTS_GET_INDEX_LAST"] = "posledný";
Blockly.Msg["LISTS_GET_INDEX_RANDOM"] = "náhodný";
Blockly.Msg["LISTS_GET_INDEX_REMOVE"] = "odstráň";
Blockly.Msg["LISTS_GET_INDEX_TAIL"] = "";  // untranslated
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_FIRST"] = "Vráti počiatočný prvok zoznamu.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_FROM"] = "Vráti prvok na určenej pozícii v zozname.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_LAST"] = "Vráti posledný prvok zoznamu.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_RANDOM"] = "Vráti náhodný prvok zoznamu.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_FIRST"] = "Odstráni a vráti prvý prvok v zozname.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_FROM"] = "Odstráni a vráti prvok z určenej pozície v zozname.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_LAST"] = "Odstráni a vráti posledný prvok v zozname.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_RANDOM"] = "Odstráni a vráti náhodný prvok v zozname.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_REMOVE_FIRST"] = "Odstráni prvý prvok v zozname.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_REMOVE_FROM"] = "Odstráni prvok na určenej pozícii v zozname.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_REMOVE_LAST"] = "Odstráni posledný prvok v zozname.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_REMOVE_RANDOM"] = "Odstráni náhodný prvok v zozname.";
Blockly.Msg["LISTS_GET_SUBLIST_END_FROM_END"] = "po # od konca";
Blockly.Msg["LISTS_GET_SUBLIST_END_FROM_START"] = "po #";
Blockly.Msg["LISTS_GET_SUBLIST_END_LAST"] = "po koniec";
Blockly.Msg["LISTS_GET_SUBLIST_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#getting-a-sublist";  // untranslated
Blockly.Msg["LISTS_GET_SUBLIST_START_FIRST"] = "Získať podzoznam od začiatku";
Blockly.Msg["LISTS_GET_SUBLIST_START_FROM_END"] = "Získať podzoznam od # od konca";
Blockly.Msg["LISTS_GET_SUBLIST_START_FROM_START"] = "získať podzoznam od #";
Blockly.Msg["LISTS_GET_SUBLIST_TAIL"] = "";  // untranslated
Blockly.Msg["LISTS_GET_SUBLIST_TOOLTIP"] = "Skopíruje určený úsek zoznamu.";
Blockly.Msg["LISTS_INDEX_FROM_END_TOOLTIP"] = "%1 je posledný prvok.";
Blockly.Msg["LISTS_INDEX_FROM_START_TOOLTIP"] = "%1 je počiatočný prvok.";
Blockly.Msg["LISTS_INDEX_OF_FIRST"] = "nájdi prvý výskyt prvku";
Blockly.Msg["LISTS_INDEX_OF_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#getting-items-from-a-list";  // untranslated
Blockly.Msg["LISTS_INDEX_OF_LAST"] = "nájdi posledný výskyt prvku";
Blockly.Msg["LISTS_INDEX_OF_TOOLTIP"] = "Vráti index prvého/posledného výskytu prvku v zozname. Ak sa nič nenašlo, vráti %1.";
Blockly.Msg["LISTS_INLIST"] = "v zozname";
Blockly.Msg["LISTS_ISEMPTY_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#is-empty";  // untranslated
Blockly.Msg["LISTS_ISEMPTY_TITLE"] = "%1 je prázdny";
Blockly.Msg["LISTS_ISEMPTY_TOOLTIP"] = "Vráti pravda, ak je zoznam prázdny.";
Blockly.Msg["LISTS_LENGTH_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#length-of";  // untranslated
Blockly.Msg["LISTS_LENGTH_TITLE"] = "dĺžka %1";
Blockly.Msg["LISTS_LENGTH_TOOLTIP"] = "Vráti dĺžku zoznamu";
Blockly.Msg["LISTS_REPEAT_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#create-list-with";  // untranslated
Blockly.Msg["LISTS_REPEAT_TITLE"] = "vytvor zoznam s prvkom %1 opakovaným %2 krát";
Blockly.Msg["LISTS_REPEAT_TOOLTIP"] = "Vytvorí zoznam s niekoľkými rovnakými prvkami s danou hodnotou.";
Blockly.Msg["LISTS_REVERSE_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#reversing-a-list";  // untranslated
Blockly.Msg["LISTS_REVERSE_MESSAGE0"] = "obrátiť %1";
Blockly.Msg["LISTS_REVERSE_TOOLTIP"] = "Obrátiť kópiu zoznamu.";
Blockly.Msg["LISTS_SET_INDEX_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#in-list--set";  // untranslated
Blockly.Msg["LISTS_SET_INDEX_INPUT_TO"] = "ako";
Blockly.Msg["LISTS_SET_INDEX_INSERT"] = "vložiť na";
Blockly.Msg["LISTS_SET_INDEX_SET"] = "nastaviť";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_INSERT_FIRST"] = "Vsunie prvok na začiatok zoznamu.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_INSERT_FROM"] = "Vsunie prvok na určenú pozíciu v zozname.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_INSERT_LAST"] = "Pripojí prvok na koniec zoznamu.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_INSERT_RANDOM"] = "Vsunie prvok na náhodné miesto v zozname.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_SET_FIRST"] = "Nastaví prvý prvok v zozname.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_SET_FROM"] = "Nastaví prvok na určenej pozícii v zozname.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_SET_LAST"] = "Nastaví posledný prvok v zozname.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_SET_RANDOM"] = "Nastaví posledný prvok v zozname.";
Blockly.Msg["LISTS_SORT_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#sorting-a-list";
Blockly.Msg["LISTS_SORT_ORDER_ASCENDING"] = "Vzostupne";
Blockly.Msg["LISTS_SORT_ORDER_DESCENDING"] = "Zostupne";
Blockly.Msg["LISTS_SORT_TITLE"] = "zoradiť %1 %2 %3";
Blockly.Msg["LISTS_SORT_TOOLTIP"] = "Zoradiť kópiu zoznamu.";
Blockly.Msg["LISTS_SORT_TYPE_IGNORECASE"] = "abecedne, ignorovať veľkosť písmen";
Blockly.Msg["LISTS_SORT_TYPE_NUMERIC"] = "numericky";
Blockly.Msg["LISTS_SORT_TYPE_TEXT"] = "abecedne";
Blockly.Msg["LISTS_SPLIT_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#splitting-strings-and-joining-lists";  // untranslated
Blockly.Msg["LISTS_SPLIT_LIST_FROM_TEXT"] = "vytvoriť zoznam z textu";
Blockly.Msg["LISTS_SPLIT_TEXT_FROM_LIST"] = "vytvoriť text zo zoznamu";
Blockly.Msg["LISTS_SPLIT_TOOLTIP_JOIN"] = "Spojiť zoznam textov do jedného textu s oddeľovačmi.";
Blockly.Msg["LISTS_SPLIT_TOOLTIP_SPLIT"] = "Rozdelenie textu do zoznamu textov, lámanie na oddeľovačoch.";
Blockly.Msg["LISTS_SPLIT_WITH_DELIMITER"] = "s oddeľovačom";
Blockly.Msg["LOGIC_BOOLEAN_FALSE"] = "nepravda";
Blockly.Msg["LOGIC_BOOLEAN_HELPURL"] = "https://github.com/google/blockly/wiki/Logic#values";  // untranslated
Blockly.Msg["LOGIC_BOOLEAN_TOOLTIP"] = "Vráť buď hodnotu pravda alebo nepravda.";
Blockly.Msg["LOGIC_BOOLEAN_TRUE"] = "pravda";
Blockly.Msg["LOGIC_COMPARE_HELPURL"] = "https://en.wikipedia.org/wiki/Inequality_(mathematics)";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_EQ"] = "Vráť hodnotu pravda, ak sú vstupy rovnaké.";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_GT"] = "Vráť hodnotu pravda ak prvý vstup je väčší než druhý.";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_GTE"] = "Vráť hodnotu pravda ak prvý vstup je väčší alebo rovný druhému.";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_LT"] = "Vráť hodnotu pravda, ak prvý vstup je menší než druhý.";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_LTE"] = "Vráť hodnotu pravda ak prvý vstup je menší alebo rovný druhému.";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_NEQ"] = "Vráť hodnotu pravda, ak vstupy nie sú rovnaké.";
Blockly.Msg["LOGIC_NEGATE_HELPURL"] = "https://github.com/google/blockly/wiki/Logic#not";  // untranslated
Blockly.Msg["LOGIC_NEGATE_TITLE"] = "nie je %1";
Blockly.Msg["LOGIC_NEGATE_TOOLTIP"] = "Vráti hodnotu pravda, ak je vstup nepravda. Vráti hodnotu nepravda ak je vstup pravda.";
Blockly.Msg["LOGIC_NULL"] = "nič";
Blockly.Msg["LOGIC_NULL_HELPURL"] = "https://en.wikipedia.org/wiki/Nullable_type";  // untranslated
Blockly.Msg["LOGIC_NULL_TOOLTIP"] = "Vráti hodnotu nula.";
Blockly.Msg["LOGIC_OPERATION_AND"] = "a";
Blockly.Msg["LOGIC_OPERATION_HELPURL"] = "https://github.com/google/blockly/wiki/Logic#logical-operations";  // untranslated
Blockly.Msg["LOGIC_OPERATION_OR"] = "alebo";
Blockly.Msg["LOGIC_OPERATION_TOOLTIP_AND"] = "Vráť hodnotu pravda, ak sú vstupy pravdivé.";
Blockly.Msg["LOGIC_OPERATION_TOOLTIP_OR"] = "Vráť hodnotu pravda, ak je aspoň jeden vstup pravda.";
Blockly.Msg["LOGIC_TERNARY_CONDITION"] = "test";
Blockly.Msg["LOGIC_TERNARY_HELPURL"] = "https://en.wikipedia.org/wiki/%3F:";  // untranslated
Blockly.Msg["LOGIC_TERNARY_IF_FALSE"] = "ak nepravda";
Blockly.Msg["LOGIC_TERNARY_IF_TRUE"] = "ak pravda";
Blockly.Msg["LOGIC_TERNARY_TOOLTIP"] = "Skontroluj podmienku testom. Ak je podmienka pravda, vráť hodnotu \"ak pravda\", inak vráť hodnotu \"ak nepravda\".";
Blockly.Msg["MATH_ADDITION_SYMBOL"] = "+";  // untranslated
Blockly.Msg["MATH_ARITHMETIC_HELPURL"] = "https://en.wikipedia.org/wiki/Arithmetic";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_ADD"] = "Vráť súčet dvoch čísel.";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_DIVIDE"] = "Vráť podiel dvoch čísel.";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_MINUS"] = "Vráť rozdiel dvoch čísel.";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_MULTIPLY"] = "Vráť súčin dvoch čísel.";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_POWER"] = "Vráť prvé číslo umocnené druhým.";
Blockly.Msg["MATH_ATAN2_HELPURL"] = "https://en.wikipedia.org/wiki/Atan2";
Blockly.Msg["MATH_ATAN2_TITLE"] = "atan2 of X:%1 Y:%2";
Blockly.Msg["MATH_ATAN2_TOOLTIP"] = "Vráťte arktangent bodu (X, Y) v stupňoch od -180 do 180.";
Blockly.Msg["MATH_CHANGE_HELPURL"] = "https://en.wikipedia.org/wiki/Programming_idiom#Incrementing_a_counter";
Blockly.Msg["MATH_CHANGE_TITLE"] = "zmeniť %1 o %2";
Blockly.Msg["MATH_CHANGE_TOOLTIP"] = "Pridaj číslo do premennej \"%1\".";
Blockly.Msg["MATH_CONSTANT_HELPURL"] = "https://en.wikipedia.org/wiki/Mathematical_constant‎";
Blockly.Msg["MATH_CONSTANT_TOOLTIP"] = "Vráť jednu zo zvyčajných konštánt: π (3.141…), e (2.718…), φ (1.618…), sqrt(2) (1.414…), sqrt(½) (0.707…), alebo ∞ (nekonečno).";
Blockly.Msg["MATH_CONSTRAIN_HELPURL"] = "https://en.wikipedia.org/wiki/Clamping_(graphics)";  // untranslated
Blockly.Msg["MATH_CONSTRAIN_TITLE"] = "obmedz %1 od %2 do %3";
Blockly.Msg["MATH_CONSTRAIN_TOOLTIP"] = "Obmedzí číslo do zadaných hraníc (vrátane).";
Blockly.Msg["MATH_DIVISION_SYMBOL"] = "÷";  // untranslated
Blockly.Msg["MATH_IS_DIVISIBLE_BY"] = "je deliteľné";
Blockly.Msg["MATH_IS_EVEN"] = "je párne";
Blockly.Msg["MATH_IS_NEGATIVE"] = "je záporné";
Blockly.Msg["MATH_IS_ODD"] = "je nepárne";
Blockly.Msg["MATH_IS_POSITIVE"] = "je kladné";
Blockly.Msg["MATH_IS_PRIME"] = "je prvočíslo";
Blockly.Msg["MATH_IS_TOOLTIP"] = "Skontroluj či je číslo párne, nepárne, celé, kladné, záporné alebo deliteľné určitým číslom. Vráť hodnotu pravda alebo nepravda.";
Blockly.Msg["MATH_IS_WHOLE"] = "je celé číslo";
Blockly.Msg["MATH_MODULO_HELPURL"] = "https://en.wikipedia.org/wiki/Modulo_operation";
Blockly.Msg["MATH_MODULO_TITLE"] = "zvyšok po delení %1 + %2";
Blockly.Msg["MATH_MODULO_TOOLTIP"] = "Vráť zvyšok po delení jedného čísla druhým.";
Blockly.Msg["MATH_MULTIPLICATION_SYMBOL"] = "×";  // untranslated
Blockly.Msg["MATH_NUMBER_HELPURL"] = "https://en.wikipedia.org/wiki/Number";
Blockly.Msg["MATH_NUMBER_TOOLTIP"] = "Číslo.";
Blockly.Msg["MATH_ONLIST_HELPURL"] = "";  // untranslated
Blockly.Msg["MATH_ONLIST_OPERATOR_AVERAGE"] = "priemer zoznamu";
Blockly.Msg["MATH_ONLIST_OPERATOR_MAX"] = "najväčšie v zozname";
Blockly.Msg["MATH_ONLIST_OPERATOR_MEDIAN"] = "medián zoznamu";
Blockly.Msg["MATH_ONLIST_OPERATOR_MIN"] = "najmenšie v zozname";
Blockly.Msg["MATH_ONLIST_OPERATOR_MODE"] = "najčastejšie v zozname";
Blockly.Msg["MATH_ONLIST_OPERATOR_RANDOM"] = "náhodný prvok zoznamu";
Blockly.Msg["MATH_ONLIST_OPERATOR_STD_DEV"] = "smerodajná odchýlka zoznamu";
Blockly.Msg["MATH_ONLIST_OPERATOR_SUM"] = "súčet zoznamu";
Blockly.Msg["MATH_ONLIST_TOOLTIP_AVERAGE"] = "Vráť aritmetický priemer čísel v zozname.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_MAX"] = "Vrátiť najväčšie číslo v zozname.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_MEDIAN"] = "Vráť medián čísel v zozname.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_MIN"] = "Vrátiť najmenšie číslo v zozname.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_MODE"] = "Vrátiť najčastejší prvok v zozname.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_RANDOM"] = "Vráť náhodne zvolený prvok zoznamu.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_STD_DEV"] = "Vráť smeroddajnú odchýlku zoznamu.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_SUM"] = "Vráť súčet všetkých čísel v zozname.";
Blockly.Msg["MATH_POWER_SYMBOL"] = "^";  // untranslated
Blockly.Msg["MATH_RANDOM_FLOAT_HELPURL"] = "https://en.wikipedia.org/wiki/Random_number_generation";
Blockly.Msg["MATH_RANDOM_FLOAT_TITLE_RANDOM"] = "náhodné číslo od 0 do 1";
Blockly.Msg["MATH_RANDOM_FLOAT_TOOLTIP"] = "Vráť náhodné číslo z intervalu 0.0 (vrátane) až 1.0.";
Blockly.Msg["MATH_RANDOM_INT_HELPURL"] = "https://en.wikipedia.org/wiki/Random_number_generation";
Blockly.Msg["MATH_RANDOM_INT_TITLE"] = "náhodné celé číslo od %1 do %2";
Blockly.Msg["MATH_RANDOM_INT_TOOLTIP"] = "Vráť náhodné celé číslo z určeného intervalu (vrátane).";
Blockly.Msg["MATH_ROUND_HELPURL"] = "https://en.wikipedia.org/wiki/Rounding";
Blockly.Msg["MATH_ROUND_OPERATOR_ROUND"] = "zaokrúhli";
Blockly.Msg["MATH_ROUND_OPERATOR_ROUNDDOWN"] = "zaokrúhli nadol";
Blockly.Msg["MATH_ROUND_OPERATOR_ROUNDUP"] = "zaokrúhli nahor";
Blockly.Msg["MATH_ROUND_TOOLTIP"] = "Zaokrúhli číslo nahor alebo nadol.";
Blockly.Msg["MATH_SINGLE_HELPURL"] = "https://en.wikipedia.org/wiki/Square_root";
Blockly.Msg["MATH_SINGLE_OP_ABSOLUTE"] = "absolútna hodnota";
Blockly.Msg["MATH_SINGLE_OP_ROOT"] = "druhá odmocnina";
Blockly.Msg["MATH_SINGLE_TOOLTIP_ABS"] = "Vráť absolútnu hodnotu čísla.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_EXP"] = "Vráť e umocnené číslom.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_LN"] = "Vráť prirodzený logaritmus čísla.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_LOG10"] = "Vráť logaritmus čísla so základom 10.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_NEG"] = "Vráť opačné číslo.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_POW10"] = "Vráť 10 umocnené číslom.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_ROOT"] = "Vráť druhú odmocninu čísla.";
Blockly.Msg["MATH_SUBTRACTION_SYMBOL"] = "-";  // untranslated
Blockly.Msg["MATH_TRIG_ACOS"] = "arccos";
Blockly.Msg["MATH_TRIG_ASIN"] = "arcsin";
Blockly.Msg["MATH_TRIG_ATAN"] = "arctan";
Blockly.Msg["MATH_TRIG_COS"] = "cos";  // untranslated
Blockly.Msg["MATH_TRIG_HELPURL"] = "https://en.wikipedia.org/wiki/Trigonometric_functions";
Blockly.Msg["MATH_TRIG_SIN"] = "sin";  // untranslated
Blockly.Msg["MATH_TRIG_TAN"] = "tan";  // untranslated
Blockly.Msg["MATH_TRIG_TOOLTIP_ACOS"] = "Vráť arkus kosínus čísla.";
Blockly.Msg["MATH_TRIG_TOOLTIP_ASIN"] = "Vráť arkus sínus čísla.";
Blockly.Msg["MATH_TRIG_TOOLTIP_ATAN"] = "Vráť arkus tangens čísla.";
Blockly.Msg["MATH_TRIG_TOOLTIP_COS"] = "Vráť kosínus uhla (v stupňoch).";
Blockly.Msg["MATH_TRIG_TOOLTIP_SIN"] = "Vráť sínus uhla (v stupňoch).";
Blockly.Msg["MATH_TRIG_TOOLTIP_TAN"] = "Vráť tangens uhla (v stupňoch).";
Blockly.Msg["NEW_COLOUR_VARIABLE"] = "vytvoriť farbu premennej";
Blockly.Msg["NEW_NUMBER_VARIABLE"] = "Vytvoriť číselnú premennú...";
Blockly.Msg["NEW_STRING_VARIABLE"] = "Vytvoriť reťazovú premennú...";
Blockly.Msg["NEW_VARIABLE"] = "Vytvoriť premennú...";
Blockly.Msg["NEW_VARIABLE_TITLE"] = "Názov novej premennej:";
Blockly.Msg["NEW_VARIABLE_TYPE_TITLE"] = "nový typ premennej";
Blockly.Msg["ORDINAL_NUMBER_SUFFIX"] = "";  // untranslated
Blockly.Msg["PROCEDURES_ALLOW_STATEMENTS"] = "povoliť príkazy";
Blockly.Msg["PROCEDURES_BEFORE_PARAMS"] = "s:";
Blockly.Msg["PROCEDURES_CALLNORETURN_HELPURL"] = "https://sk.wikipedia.org/wiki/Podprogram";
Blockly.Msg["PROCEDURES_CALLNORETURN_TOOLTIP"] = "Spustí používateľom definovanú funkciu '%1'.";
Blockly.Msg["PROCEDURES_CALLRETURN_HELPURL"] = "https://sk.wikipedia.org/wiki/Podprogram";
Blockly.Msg["PROCEDURES_CALLRETURN_TOOLTIP"] = "Spustí používateľom definovanú funkciu '%1' a použije jej výstup.";
Blockly.Msg["PROCEDURES_CALL_BEFORE_PARAMS"] = "s:";
Blockly.Msg["PROCEDURES_CREATE_DO"] = "Vytvoriť '%1'";
Blockly.Msg["PROCEDURES_DEFNORETURN_COMMENT"] = "Doplň, čo robí táto funkcia...";
Blockly.Msg["PROCEDURES_DEFNORETURN_DO"] = "";  // untranslated
Blockly.Msg["PROCEDURES_DEFNORETURN_HELPURL"] = "https://en.wikipedia.org/wiki/Subroutine";  // untranslated
Blockly.Msg["PROCEDURES_DEFNORETURN_PROCEDURE"] = "urob niečo";
Blockly.Msg["PROCEDURES_DEFNORETURN_TITLE"] = "na";
Blockly.Msg["PROCEDURES_DEFNORETURN_TOOLTIP"] = "Vytvorí funciu bez výstupu.";
Blockly.Msg["PROCEDURES_DEFRETURN_HELPURL"] = "https://en.wikipedia.org/wiki/Subroutine";  // untranslated
Blockly.Msg["PROCEDURES_DEFRETURN_RETURN"] = "vrátiť";
Blockly.Msg["PROCEDURES_DEFRETURN_TOOLTIP"] = "Vytvorí funkciu s výstupom.";
Blockly.Msg["PROCEDURES_DEF_DUPLICATE_WARNING"] = "Upozornenie: Táto funkcia má duplicitné parametre.";
Blockly.Msg["PROCEDURES_HIGHLIGHT_DEF"] = "Zvýrazniť definíciu funkcie";
Blockly.Msg["PROCEDURES_IFRETURN_HELPURL"] = "http://c2.com/cgi/wiki?GuardClause";  // untranslated
Blockly.Msg["PROCEDURES_IFRETURN_TOOLTIP"] = "Ak je hodnota pravda, tak vráti druhú hodnotu.";
Blockly.Msg["PROCEDURES_IFRETURN_WARNING"] = "Upozornenie: Tento blok môže byť len vo vnútri funkcie.";
Blockly.Msg["PROCEDURES_MUTATORARG_TITLE"] = "názov vstupu:";
Blockly.Msg["PROCEDURES_MUTATORARG_TOOLTIP"] = "Pridať vstup do funkcie.";
Blockly.Msg["PROCEDURES_MUTATORCONTAINER_TITLE"] = "vstupy";
Blockly.Msg["PROCEDURES_MUTATORCONTAINER_TOOLTIP"] = "Pridať, odstrániť alebo zmeniť poradie vstupov tejto funkcie.";
Blockly.Msg["REDO"] = "Znova";
Blockly.Msg["REMOVE_COMMENT"] = "Odstrániť komentár";
Blockly.Msg["RENAME_VARIABLE"] = "Premenovať premennú...";
Blockly.Msg["RENAME_VARIABLE_TITLE"] = "Premenovať všetky premenné '%1' na:";
Blockly.Msg["TEXT_APPEND_HELPURL"] = "https://github.com/google/blockly/wiki/Text#text-modification";  // untranslated
Blockly.Msg["TEXT_APPEND_TITLE"] = "do %1 pridaj text %2";
Blockly.Msg["TEXT_APPEND_TOOLTIP"] = "Pridaj určitý text do premennej '%1'.";
Blockly.Msg["TEXT_CHANGECASE_HELPURL"] = "https://github.com/google/blockly/wiki/Text#adjusting-text-case";  // untranslated
Blockly.Msg["TEXT_CHANGECASE_OPERATOR_LOWERCASE"] = "na malé písmená";
Blockly.Msg["TEXT_CHANGECASE_OPERATOR_TITLECASE"] = "na Veľké Začiatočné Písmená";
Blockly.Msg["TEXT_CHANGECASE_OPERATOR_UPPERCASE"] = "na VEĽKÉ PÍSMENÁ";
Blockly.Msg["TEXT_CHANGECASE_TOOLTIP"] = "Vráť kópiu textu s inou veľkosťou písmen.";
Blockly.Msg["TEXT_CHARAT_FIRST"] = "zisti prvé písmeno";
Blockly.Msg["TEXT_CHARAT_FROM_END"] = "zisti # písmeno od konca";
Blockly.Msg["TEXT_CHARAT_FROM_START"] = "zisti písmeno #";
Blockly.Msg["TEXT_CHARAT_HELPURL"] = "https://github.com/google/blockly/wiki/Text#extracting-text";  // untranslated
Blockly.Msg["TEXT_CHARAT_LAST"] = "zisti posledné písmeno";
Blockly.Msg["TEXT_CHARAT_RANDOM"] = "vyber náhodné písmeno";
Blockly.Msg["TEXT_CHARAT_TAIL"] = "";  // untranslated
Blockly.Msg["TEXT_CHARAT_TITLE"] = "v texte %1 %2";
Blockly.Msg["TEXT_CHARAT_TOOLTIP"] = "Vráti písmeno na určenej pozícii.";
Blockly.Msg["TEXT_COUNT_HELPURL"] = "https://github.com/google/blockly/wiki/Text#counting-substrings";  // untranslated
Blockly.Msg["TEXT_COUNT_MESSAGE0"] = "počet výskytov %1 v %2";
Blockly.Msg["TEXT_COUNT_TOOLTIP"] = "Počet výskytov textu nachádzajúcom sa v inom texte.";
Blockly.Msg["TEXT_CREATE_JOIN_ITEM_TOOLTIP"] = "Pridaj prvok do textu.";
Blockly.Msg["TEXT_CREATE_JOIN_TITLE_JOIN"] = "spoj";
Blockly.Msg["TEXT_CREATE_JOIN_TOOLTIP"] = "Pridaj, odstráň alebo zmeň poradie oddielov v tomto textovom bloku.";
Blockly.Msg["TEXT_GET_SUBSTRING_END_FROM_END"] = "po # písmeno od konca";
Blockly.Msg["TEXT_GET_SUBSTRING_END_FROM_START"] = "po písmeno #";
Blockly.Msg["TEXT_GET_SUBSTRING_END_LAST"] = "po koniec";
Blockly.Msg["TEXT_GET_SUBSTRING_HELPURL"] = "https://github.com/google/blockly/wiki/Text#extracting-a-region-of-text";  // untranslated
Blockly.Msg["TEXT_GET_SUBSTRING_INPUT_IN_TEXT"] = "v texte";
Blockly.Msg["TEXT_GET_SUBSTRING_START_FIRST"] = "vyber podreťazec od začiatku";
Blockly.Msg["TEXT_GET_SUBSTRING_START_FROM_END"] = "vyber podreťazec od # písmena od konca";
Blockly.Msg["TEXT_GET_SUBSTRING_START_FROM_START"] = "vyber podreťazec od písmena #";
Blockly.Msg["TEXT_GET_SUBSTRING_TAIL"] = "";  // untranslated
Blockly.Msg["TEXT_GET_SUBSTRING_TOOLTIP"] = "Vráti určenú časť textu.";
Blockly.Msg["TEXT_INDEXOF_HELPURL"] = "https://github.com/google/blockly/wiki/Text#finding-text";  // untranslated
Blockly.Msg["TEXT_INDEXOF_OPERATOR_FIRST"] = "nájdi prvý výskyt textu";
Blockly.Msg["TEXT_INDEXOF_OPERATOR_LAST"] = "nájdi posledný výskyt textu";
Blockly.Msg["TEXT_INDEXOF_TITLE"] = "v texte %1 %2 %3";
Blockly.Msg["TEXT_INDEXOF_TOOLTIP"] = "Vráti index prvého/posledného výskytu prvého textu v druhom texte. Ak nenájde, vráti %1.";
Blockly.Msg["TEXT_ISEMPTY_HELPURL"] = "https://github.com/google/blockly/wiki/Text#checking-for-empty-text";  // untranslated
Blockly.Msg["TEXT_ISEMPTY_TITLE"] = "%1 je prázdny";
Blockly.Msg["TEXT_ISEMPTY_TOOLTIP"] = "Vráti hodnotu pravda, ak zadaný text je prázdny.";
Blockly.Msg["TEXT_JOIN_HELPURL"] = "https://github.com/google/blockly/wiki/Text#text-creation";  // untranslated
Blockly.Msg["TEXT_JOIN_TITLE_CREATEWITH"] = "vytvor text z";
Blockly.Msg["TEXT_JOIN_TOOLTIP"] = "Vytvor text spojením určitého počtu prvkov.";
Blockly.Msg["TEXT_LENGTH_HELPURL"] = "https://github.com/google/blockly/wiki/Text#text-modification";  // untranslated
Blockly.Msg["TEXT_LENGTH_TITLE"] = "dĺžka %1";
Blockly.Msg["TEXT_LENGTH_TOOLTIP"] = "Vráti počet písmen (s medzerami) v zadanom texte.";
Blockly.Msg["TEXT_PRINT_HELPURL"] = "https://github.com/google/blockly/wiki/Text#printing-text";  // untranslated
Blockly.Msg["TEXT_PRINT_TITLE"] = "píš %1";
Blockly.Msg["TEXT_PRINT_TOOLTIP"] = "Napíš zadaný text, číslo alebo hodnotu.";
Blockly.Msg["TEXT_PROMPT_HELPURL"] = "https://github.com/google/blockly/wiki/Text#getting-input-from-the-user";  // untranslated
Blockly.Msg["TEXT_PROMPT_TOOLTIP_NUMBER"] = "Výzva pre používateľa na zadanie čísla.";
Blockly.Msg["TEXT_PROMPT_TOOLTIP_TEXT"] = "Výzva pre používateľa na zadanie textu.";
Blockly.Msg["TEXT_PROMPT_TYPE_NUMBER"] = "výzva na zadanie čísla so správou";
Blockly.Msg["TEXT_PROMPT_TYPE_TEXT"] = "výzva za zadanie textu so správou";
Blockly.Msg["TEXT_REPLACE_HELPURL"] = "https://github.com/google/blockly/wiki/Text#replacing-substrings";  // untranslated
Blockly.Msg["TEXT_REPLACE_MESSAGE0"] = "zameniť %1 za %2 v reťazci %3";
Blockly.Msg["TEXT_REPLACE_TOOLTIP"] = "Zameniť všetky výskyty textu za iný text.";
Blockly.Msg["TEXT_REVERSE_HELPURL"] = "https://github.com/google/blockly/wiki/Text#reversing-text";  // untranslated
Blockly.Msg["TEXT_REVERSE_MESSAGE0"] = "text odzadu %1";
Blockly.Msg["TEXT_REVERSE_TOOLTIP"] = "Obrátiť poradie písmen v texte.";
Blockly.Msg["TEXT_TEXT_HELPURL"] = "https://en.wikipedia.org/wiki/String_(computer_science)";
Blockly.Msg["TEXT_TEXT_TOOLTIP"] = "Písmeno, slovo alebo riadok textu.";
Blockly.Msg["TEXT_TRIM_HELPURL"] = "https://github.com/google/blockly/wiki/Text#trimming-removing-spaces";  // untranslated
Blockly.Msg["TEXT_TRIM_OPERATOR_BOTH"] = "odstráň medzery z oboch strán";
Blockly.Msg["TEXT_TRIM_OPERATOR_LEFT"] = "odstráň medzery z ľavej strany";
Blockly.Msg["TEXT_TRIM_OPERATOR_RIGHT"] = "odstráň medzery z pravej strany";
Blockly.Msg["TEXT_TRIM_TOOLTIP"] = "Vráť kópiu textu bez medzier na jednom alebo oboch koncoch.";
Blockly.Msg["TODAY"] = "Dnes";
Blockly.Msg["UNDO"] = "Späť";
Blockly.Msg["UNNAMED_KEY"] = "nepomenované";
Blockly.Msg["VARIABLES_DEFAULT_NAME"] = "prvok";
Blockly.Msg["VARIABLES_GET_CREATE_SET"] = "Vytvoriť \"nastaviť %1\"";
Blockly.Msg["VARIABLES_GET_HELPURL"] = "https://github.com/google/blockly/wiki/Variables#get";  // untranslated
Blockly.Msg["VARIABLES_GET_TOOLTIP"] = "Vráti hodnotu tejto premennej.";
Blockly.Msg["VARIABLES_SET"] = "nastaviť %1 na %2";
Blockly.Msg["VARIABLES_SET_CREATE_GET"] = "Vytvoriť \"získať %1\"";
Blockly.Msg["VARIABLES_SET_HELPURL"] = "https://github.com/google/blockly/wiki/Variables#set";  // untranslated
Blockly.Msg["VARIABLES_SET_TOOLTIP"] = "Nastaví túto premennú, aby sa rovnala vstupu.";
Blockly.Msg["VARIABLE_ALREADY_EXISTS"] = "Premenná s názvom %1 už existuje.";
Blockly.Msg["VARIABLE_ALREADY_EXISTS_FOR_ANOTHER_TYPE"] = "Premenná s názvom '%1' už existuje pre inú premennú typu '%2'.";
Blockly.Msg["WORKSPACE_ARIA_LABEL"] = "Pracovisko Bloskly";
Blockly.Msg["WORKSPACE_COMMENT_DEFAULT_TEXT"] = "Povedz niečo...";
Blockly.Msg["CONTROLS_FOREACH_INPUT_DO"] = Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"];
Blockly.Msg["CONTROLS_FOR_INPUT_DO"] = Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"];
Blockly.Msg["CONTROLS_IF_ELSEIF_TITLE_ELSEIF"] = Blockly.Msg["CONTROLS_IF_MSG_ELSEIF"];
Blockly.Msg["CONTROLS_IF_ELSE_TITLE_ELSE"] = Blockly.Msg["CONTROLS_IF_MSG_ELSE"];
Blockly.Msg["CONTROLS_IF_IF_TITLE_IF"] = Blockly.Msg["CONTROLS_IF_MSG_IF"];
Blockly.Msg["CONTROLS_IF_MSG_THEN"] = Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"];
Blockly.Msg["CONTROLS_WHILEUNTIL_INPUT_DO"] = Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"];
Blockly.Msg["LISTS_CREATE_WITH_ITEM_TITLE"] = Blockly.Msg["VARIABLES_DEFAULT_NAME"];
Blockly.Msg["LISTS_GET_INDEX_HELPURL"] = Blockly.Msg["LISTS_INDEX_OF_HELPURL"];
Blockly.Msg["LISTS_GET_INDEX_INPUT_IN_LIST"] = Blockly.Msg["LISTS_INLIST"];
Blockly.Msg["LISTS_GET_SUBLIST_INPUT_IN_LIST"] = Blockly.Msg["LISTS_INLIST"];
Blockly.Msg["LISTS_INDEX_OF_INPUT_IN_LIST"] = Blockly.Msg["LISTS_INLIST"];
Blockly.Msg["LISTS_SET_INDEX_INPUT_IN_LIST"] = Blockly.Msg["LISTS_INLIST"];
Blockly.Msg["MATH_CHANGE_TITLE_ITEM"] = Blockly.Msg["VARIABLES_DEFAULT_NAME"];
Blockly.Msg["PROCEDURES_DEFRETURN_COMMENT"] = Blockly.Msg["PROCEDURES_DEFNORETURN_COMMENT"];
Blockly.Msg["PROCEDURES_DEFRETURN_DO"] = Blockly.Msg["PROCEDURES_DEFNORETURN_DO"];
Blockly.Msg["PROCEDURES_DEFRETURN_PROCEDURE"] = Blockly.Msg["PROCEDURES_DEFNORETURN_PROCEDURE"];
Blockly.Msg["PROCEDURES_DEFRETURN_TITLE"] = Blockly.Msg["PROCEDURES_DEFNORETURN_TITLE"];
Blockly.Msg["TEXT_APPEND_VARIABLE"] = Blockly.Msg["VARIABLES_DEFAULT_NAME"];
Blockly.Msg["TEXT_CREATE_JOIN_ITEM_TITLE_ITEM"] = Blockly.Msg["VARIABLES_DEFAULT_NAME"];

Blockly.Msg["MATH_HUE"] = "230";
Blockly.Msg["LOOPS_HUE"] = "120";
Blockly.Msg["LISTS_HUE"] = "260";
Blockly.Msg["LOGIC_HUE"] = "210";
Blockly.Msg["VARIABLES_HUE"] = "330";
Blockly.Msg["TEXTS_HUE"] = "160";
Blockly.Msg["PROCEDURES_HUE"] = "290";
Blockly.Msg["COLOUR_HUE"] = "20";
Blockly.Msg["VARIABLES_DYNAMIC_HUE"] = "310";