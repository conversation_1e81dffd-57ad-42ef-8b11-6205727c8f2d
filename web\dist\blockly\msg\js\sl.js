// This file was automatically generated.  Do not modify.

'use strict';

Blockly.Msg["ADD_COMMENT"] = "Dodaj komentar";
Blockly.Msg["CANNOT_DELETE_VARIABLE_PROCEDURE"] = "Ni mogoče izbrisati spremenljivke »%1«, ker je uporabljena v definiciji funkcije »%2«.";
Blockly.Msg["CHANGE_VALUE_TITLE"] = "Spremeni vrednost:";
Blockly.Msg["CLEAN_UP"] = "Ponastavi bloke";
Blockly.Msg["COLLAPSED_WARNINGS_WARNING"] = "Skrčeni bloki vsebujejo opozorila.";
Blockly.Msg["COLLAPSE_ALL"] = "Skrči bloke";
Blockly.Msg["COLLAPSE_BLOCK"] = "Skrči blok";
Blockly.Msg["COLOUR_BLEND_COLOUR1"] = "barva 1";
Blockly.Msg["COLOUR_BLEND_COLOUR2"] = "barva 2";
Blockly.Msg["COLOUR_BLEND_HELPURL"] = "http://meyerweb.com/eric/tools/color-blend/";
Blockly.Msg["COLOUR_BLEND_RATIO"] = "razmerje";
Blockly.Msg["COLOUR_BLEND_TITLE"] = "mešanica";
Blockly.Msg["COLOUR_BLEND_TOOLTIP"] = "Zmeša dve barvi v določene razmerju (0,0 – 1,0).";
Blockly.Msg["COLOUR_PICKER_HELPURL"] = "https://sl.wikipedia.org/wiki/Barva";
Blockly.Msg["COLOUR_PICKER_TOOLTIP"] = "Izberite barvo s palete.";
Blockly.Msg["COLOUR_RANDOM_HELPURL"] = "http://randomcolour.com";  // untranslated
Blockly.Msg["COLOUR_RANDOM_TITLE"] = "naključna barva";
Blockly.Msg["COLOUR_RANDOM_TOOLTIP"] = "Izberite naključno barvo.";
Blockly.Msg["COLOUR_RGB_BLUE"] = "modra";
Blockly.Msg["COLOUR_RGB_GREEN"] = "zelena";
Blockly.Msg["COLOUR_RGB_HELPURL"] = "http://www.december.com/html/spec/colorper.html";
Blockly.Msg["COLOUR_RGB_RED"] = "rdeča";
Blockly.Msg["COLOUR_RGB_TITLE"] = "določena barva";
Blockly.Msg["COLOUR_RGB_TOOLTIP"] = "Ustvari barvo z določeno količino rdeče, zelene in modre. Vse vrednosti morajo biti med 0 in 100.";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_HELPURL"] = "https://github.com/google/blockly/wiki/Loops#loop-termination-blocks";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_OPERATOR_BREAK"] = "prekini zanko";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_OPERATOR_CONTINUE"] = "nadaljuj z naslednjo ponovitvijo zanke";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_TOOLTIP_BREAK"] = "Prekine vsebujočo zanko.";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_TOOLTIP_CONTINUE"] = "Preskoči preostanek te zanke in nadaljuje z naslednjo ponovitvijo.";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_WARNING"] = "Pozor: Ta blok lahko uporabite znotraj zanke samo enkrat.";
Blockly.Msg["CONTROLS_FOREACH_HELPURL"] = "https://github.com/google/blockly/wiki/Loops#for-each";
Blockly.Msg["CONTROLS_FOREACH_TITLE"] = "za vsak element %1 v seznamu %2";
Blockly.Msg["CONTROLS_FOREACH_TOOLTIP"] = "Za vsak element v seznamu nastavi spremenljivko »%1« na ta element. Pri tem se izvedejo določeni stavki.";
Blockly.Msg["CONTROLS_FOR_HELPURL"] = "https://github.com/google/blockly/wiki/Loops#count-with";
Blockly.Msg["CONTROLS_FOR_TITLE"] = "štej s/z %1 od %2 do %3 po %4";
Blockly.Msg["CONTROLS_FOR_TOOLTIP"] = "Vrednost spremenljivke »%1« se v določenem koraku spreminja od začetnega do končnega števila. Pri tem se izvedejo določeni bloki.";
Blockly.Msg["CONTROLS_IF_ELSEIF_TOOLTIP"] = "Dodajte bloku »če« pogoj.";
Blockly.Msg["CONTROLS_IF_ELSE_TOOLTIP"] = "Dodajte bloku »če« končni pogoj.";
Blockly.Msg["CONTROLS_IF_HELPURL"] = "https://github.com/google/blockly/wiki/IfElse";
Blockly.Msg["CONTROLS_IF_IF_TOOLTIP"] = "Dodajte, odstranite ali spremenite vrstni red odsekov za ponovno nastavitev bloka »če«.";
Blockly.Msg["CONTROLS_IF_MSG_ELSE"] = "sicer";
Blockly.Msg["CONTROLS_IF_MSG_ELSEIF"] = "sicer če";
Blockly.Msg["CONTROLS_IF_MSG_IF"] = "če";
Blockly.Msg["CONTROLS_IF_TOOLTIP_1"] = "Če je vrednost resnična, izvedi določene stavke.";
Blockly.Msg["CONTROLS_IF_TOOLTIP_2"] = "Če je vrednost resnična, izvedi prvo skupino stavkov. Sicer izvedi drugo skupino stavkov.";
Blockly.Msg["CONTROLS_IF_TOOLTIP_3"] = "Če je prva vrednost resnična, izvedi prvo skupino stavkov. Sicer, če je resnična druga vrednost, izvedi drugo skupino stavkov.";
Blockly.Msg["CONTROLS_IF_TOOLTIP_4"] = "Če je prva vrednost resnična, izvedi prvo skupino stavkov. Sicer, če je resnična druga vrednost, izvedi drugo skupino stavkov. Če ni resnična nobena od vrednosti, izvedi zadnjo skupino stavkov.";
Blockly.Msg["CONTROLS_REPEAT_HELPURL"] = "https://sl.wikipedia.org/wiki/Zanka_for";
Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"] = "izvedi";
Blockly.Msg["CONTROLS_REPEAT_TITLE"] = "ponovi %1-krat";
Blockly.Msg["CONTROLS_REPEAT_TOOLTIP"] = "Določeni stavki se izvedejo večkrat.";
Blockly.Msg["CONTROLS_WHILEUNTIL_HELPURL"] = "https://github.com/google/blockly/wiki/Loops#repeat";
Blockly.Msg["CONTROLS_WHILEUNTIL_OPERATOR_UNTIL"] = "ponavljaj, dokler ni";
Blockly.Msg["CONTROLS_WHILEUNTIL_OPERATOR_WHILE"] = "ponavljaj, dokler";
Blockly.Msg["CONTROLS_WHILEUNTIL_TOOLTIP_UNTIL"] = "Določeni stavki se izvajajo, dokler je vrednost neresnična.";
Blockly.Msg["CONTROLS_WHILEUNTIL_TOOLTIP_WHILE"] = "Določeni stavki se izvajajo, dokler je vrednost resnična.";
Blockly.Msg["DELETE_ALL_BLOCKS"] = "Izbrišem vse bloke (%1)?";
Blockly.Msg["DELETE_BLOCK"] = "Izbriši blok";
Blockly.Msg["DELETE_VARIABLE"] = "Izbriši spremenljivko »%1«";
Blockly.Msg["DELETE_VARIABLE_CONFIRMATION"] = "Izbrišem %1 uporab spremenljivke »%2«?";
Blockly.Msg["DELETE_X_BLOCKS"] = "Izbriši bloke (%1)";
Blockly.Msg["DIALOG_CANCEL"] = "Prekliči";
Blockly.Msg["DIALOG_OK"] = "V redu";
Blockly.Msg["DISABLE_BLOCK"] = "Onemogoči blok";
Blockly.Msg["DUPLICATE_BLOCK"] = "Podvoji";
Blockly.Msg["DUPLICATE_COMMENT"] = "Podvoji komentar";
Blockly.Msg["ENABLE_BLOCK"] = "Omogoči blok";
Blockly.Msg["EXPAND_ALL"] = "Razširi bloke";
Blockly.Msg["EXPAND_BLOCK"] = "Razširi blok";
Blockly.Msg["EXTERNAL_INPUTS"] = "Zunanji vnosi";
Blockly.Msg["HELP"] = "Pomoč";
Blockly.Msg["INLINE_INPUTS"] = "Vrstični vnosi";
Blockly.Msg["LISTS_CREATE_EMPTY_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#create-empty-list";
Blockly.Msg["LISTS_CREATE_EMPTY_TITLE"] = "ustvari prazen seznam";
Blockly.Msg["LISTS_CREATE_EMPTY_TOOLTIP"] = "Vrne seznam dolžine 0, ki ne vsebuje nobenih podatkovnih zapisov.";
Blockly.Msg["LISTS_CREATE_WITH_CONTAINER_TITLE_ADD"] = "seznam";
Blockly.Msg["LISTS_CREATE_WITH_CONTAINER_TOOLTIP"] = "Doda, odstrani ali spremeni vrstni red blokov seznama.";
Blockly.Msg["LISTS_CREATE_WITH_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#create-list-with";
Blockly.Msg["LISTS_CREATE_WITH_INPUT_WITH"] = "ustvari seznam iz";
Blockly.Msg["LISTS_CREATE_WITH_ITEM_TOOLTIP"] = "Doda element v seznam.";
Blockly.Msg["LISTS_CREATE_WITH_TOOLTIP"] = "Ustvari seznam s poljubnim številom elementov.";
Blockly.Msg["LISTS_GET_INDEX_FIRST"] = "prvo mesto";
Blockly.Msg["LISTS_GET_INDEX_FROM_END"] = "mesto št. od konca";
Blockly.Msg["LISTS_GET_INDEX_FROM_START"] = "št.";
Blockly.Msg["LISTS_GET_INDEX_GET"] = "vrni";
Blockly.Msg["LISTS_GET_INDEX_GET_REMOVE"] = "odstrani in vrni";
Blockly.Msg["LISTS_GET_INDEX_LAST"] = "zadnje mesto";
Blockly.Msg["LISTS_GET_INDEX_RANDOM"] = "naključno mesto";
Blockly.Msg["LISTS_GET_INDEX_REMOVE"] = "odstrani";
Blockly.Msg["LISTS_GET_INDEX_TAIL"] = "";  // untranslated
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_FIRST"] = "Vrne prvi element seznama.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_FROM"] = "Vrne element na določenem mestu v seznamu.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_LAST"] = "Vrne zadnji element seznama.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_RANDOM"] = "Vrne naključni element seznama.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_FIRST"] = "Odstrani in vrne prvi element seznama.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_FROM"] = "Odstrani in vrne element na določenem mestu v seznamu.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_LAST"] = "Odstrani in vrne zadnji element seznama.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_RANDOM"] = "Odstrani in vrne naključni element seznama.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_REMOVE_FIRST"] = "Odstrani prvi element seznama.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_REMOVE_FROM"] = "Odstrani element na določenem mestu v seznamu.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_REMOVE_LAST"] = "Odstrani zadnji element seznama.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_REMOVE_RANDOM"] = "Odstrani naključni element seznama.";
Blockly.Msg["LISTS_GET_SUBLIST_END_FROM_END"] = "do mesta št. od konca";
Blockly.Msg["LISTS_GET_SUBLIST_END_FROM_START"] = "do mesta št.";
Blockly.Msg["LISTS_GET_SUBLIST_END_LAST"] = "do zadnjega mesta";
Blockly.Msg["LISTS_GET_SUBLIST_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#getting-a-sublist";
Blockly.Msg["LISTS_GET_SUBLIST_START_FIRST"] = "ustvari podseznam od prvega mesta";
Blockly.Msg["LISTS_GET_SUBLIST_START_FROM_END"] = "ustvari podseznam od mesta št. od konca";
Blockly.Msg["LISTS_GET_SUBLIST_START_FROM_START"] = "ustvari podseznam od mesta št.";
Blockly.Msg["LISTS_GET_SUBLIST_TAIL"] = "";  // untranslated
Blockly.Msg["LISTS_GET_SUBLIST_TOOLTIP"] = "Ustvari kopijo določenega dela seznama.";
Blockly.Msg["LISTS_INDEX_FROM_END_TOOLTIP"] = "Zadnji element je št. %1.";
Blockly.Msg["LISTS_INDEX_FROM_START_TOOLTIP"] = "Prvi element je št. %1.";
Blockly.Msg["LISTS_INDEX_OF_FIRST"] = "najdi prvo pojavitev elementa";
Blockly.Msg["LISTS_INDEX_OF_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#getting-items-from-a-list";
Blockly.Msg["LISTS_INDEX_OF_LAST"] = "najdi zadnjo pojavitev elementa";
Blockly.Msg["LISTS_INDEX_OF_TOOLTIP"] = "Vrne mesto (indeks) prve/zadnje pojavitve elementa v seznamu. Če elementa ne najde, vrne %1.";
Blockly.Msg["LISTS_INLIST"] = "v seznamu";
Blockly.Msg["LISTS_ISEMPTY_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#is-empty";
Blockly.Msg["LISTS_ISEMPTY_TITLE"] = "%1 je prazen";
Blockly.Msg["LISTS_ISEMPTY_TOOLTIP"] = "Vrne resnično, če je seznam prazen.";
Blockly.Msg["LISTS_LENGTH_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#length-of";
Blockly.Msg["LISTS_LENGTH_TITLE"] = "dolžina %1";
Blockly.Msg["LISTS_LENGTH_TOOLTIP"] = "Vrne dolžino seznama.";
Blockly.Msg["LISTS_REPEAT_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#create-list-with";
Blockly.Msg["LISTS_REPEAT_TITLE"] = "ustvari seznam z elementom %1, ki se ponovi %2-krat";
Blockly.Msg["LISTS_REPEAT_TOOLTIP"] = "Ustvari seznam iz dane vrednosti z določenim številom ponovitev.";
Blockly.Msg["LISTS_REVERSE_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#reversing-a-list";  // untranslated
Blockly.Msg["LISTS_REVERSE_MESSAGE0"] = "obrni %1";
Blockly.Msg["LISTS_REVERSE_TOOLTIP"] = "Obrne kopijo seznama.";
Blockly.Msg["LISTS_SET_INDEX_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#in-list--set";
Blockly.Msg["LISTS_SET_INDEX_INPUT_TO"] = "element";
Blockly.Msg["LISTS_SET_INDEX_INSERT"] = "vstavi na";
Blockly.Msg["LISTS_SET_INDEX_SET"] = "nastavi na";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_INSERT_FIRST"] = "Vstavi element na začetek seznama.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_INSERT_FROM"] = "Vstavi element na določeno mesto v seznamu.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_INSERT_LAST"] = "Doda element na konec seznama.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_INSERT_RANDOM"] = "Vstavi element na naključno mesto v seznamu.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_SET_FIRST"] = "Nastavi prvi element seznama.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_SET_FROM"] = "Nastavi element na določenem mestu v seznamu.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_SET_LAST"] = "Nastavi zadnji element seznama.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_SET_RANDOM"] = "Nastavi naključni element seznama.";
Blockly.Msg["LISTS_SORT_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#sorting-a-list";
Blockly.Msg["LISTS_SORT_ORDER_ASCENDING"] = "naraščajoče";
Blockly.Msg["LISTS_SORT_ORDER_DESCENDING"] = "padajoče";
Blockly.Msg["LISTS_SORT_TITLE"] = "uredi %1 %2 %3";
Blockly.Msg["LISTS_SORT_TOOLTIP"] = "Uredi kopijo seznama.";
Blockly.Msg["LISTS_SORT_TYPE_IGNORECASE"] = "abecedno, prezri velikost črk";
Blockly.Msg["LISTS_SORT_TYPE_NUMERIC"] = "številsko";
Blockly.Msg["LISTS_SORT_TYPE_TEXT"] = "abecedno";
Blockly.Msg["LISTS_SPLIT_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#splitting-strings-and-joining-lists";
Blockly.Msg["LISTS_SPLIT_LIST_FROM_TEXT"] = "ustvari seznam iz besedila";
Blockly.Msg["LISTS_SPLIT_TEXT_FROM_LIST"] = "ustvari besedilo iz seznama";
Blockly.Msg["LISTS_SPLIT_TOOLTIP_JOIN"] = "Združi seznam besedil v eno besedilo z ločilom med besedili.";
Blockly.Msg["LISTS_SPLIT_TOOLTIP_SPLIT"] = "Razdruži besedilo v seznam besedil s prelomom pri vsakem ločilu.";
Blockly.Msg["LISTS_SPLIT_WITH_DELIMITER"] = "z ločilom";
Blockly.Msg["LOGIC_BOOLEAN_FALSE"] = "neresnično";
Blockly.Msg["LOGIC_BOOLEAN_HELPURL"] = "https://github.com/google/blockly/wiki/Logic#values";
Blockly.Msg["LOGIC_BOOLEAN_TOOLTIP"] = "Vrne resnično ali neresnično.";
Blockly.Msg["LOGIC_BOOLEAN_TRUE"] = "resnično";
Blockly.Msg["LOGIC_COMPARE_HELPURL"] = "https://en.wikipedia.org/wiki/Inequality_(mathematics)";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_EQ"] = "Vrne resnično, če sta vnosa enaka.";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_GT"] = "Vrne resnično, če je prvi vnos večji od drugega.";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_GTE"] = "Vrne resnično, če je prvi vnos večji ali enak drugemu.";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_LT"] = "Vrne resnično, če je prvi vnos manjši od drugega.";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_LTE"] = "Vrne resnično, če je prvi vnos manjši ali enak drugemu.";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_NEQ"] = "Vrne resnično, če vnosa nista enaka.";
Blockly.Msg["LOGIC_NEGATE_HELPURL"] = "https://github.com/google/blockly/wiki/Logic#not";
Blockly.Msg["LOGIC_NEGATE_TITLE"] = "ne %1";
Blockly.Msg["LOGIC_NEGATE_TOOLTIP"] = "Vrne resnično, če je vnos neresničen. Vrne neresnično, če je vnos resničen.";
Blockly.Msg["LOGIC_NULL"] = "prazno";
Blockly.Msg["LOGIC_NULL_HELPURL"] = "https://en.wikipedia.org/wiki/Nullable_type";
Blockly.Msg["LOGIC_NULL_TOOLTIP"] = "Vrne prazno.";
Blockly.Msg["LOGIC_OPERATION_AND"] = "in";
Blockly.Msg["LOGIC_OPERATION_HELPURL"] = "https://github.com/google/blockly/wiki/Logic#logical-operations";
Blockly.Msg["LOGIC_OPERATION_OR"] = "ali";
Blockly.Msg["LOGIC_OPERATION_TOOLTIP_AND"] = "Vrne resnično, če sta oba vnosa resnična.";
Blockly.Msg["LOGIC_OPERATION_TOOLTIP_OR"] = "Vrne resnično, če je vsaj eden od vnosov resničen.";
Blockly.Msg["LOGIC_TERNARY_CONDITION"] = "test";
Blockly.Msg["LOGIC_TERNARY_HELPURL"] = "https://en.wikipedia.org/wiki/%3F:";
Blockly.Msg["LOGIC_TERNARY_IF_FALSE"] = "če neresnično";
Blockly.Msg["LOGIC_TERNARY_IF_TRUE"] = "če resnično";
Blockly.Msg["LOGIC_TERNARY_TOOLTIP"] = "Preveri pogoj v »testu«. Če je pogoj resničen, potem vrne vrednost »če resnično«; sicer vrne vrednost »če neresnično«.";
Blockly.Msg["MATH_ADDITION_SYMBOL"] = "+";
Blockly.Msg["MATH_ARITHMETIC_HELPURL"] = "https://sl.wikipedia.org/wiki/Aritmetika";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_ADD"] = "Vrne vsoto dveh števil.";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_DIVIDE"] = "Vrne količnik dveh števil.";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_MINUS"] = "Vrne razliko dveh števil.";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_MULTIPLY"] = "Vrne zmnožek dveh števil.";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_POWER"] = "Vrne prvo število na potenco drugega števila.";
Blockly.Msg["MATH_ATAN2_HELPURL"] = "https://sl.wikipedia.org/wiki/Atan2";
Blockly.Msg["MATH_ATAN2_TITLE"] = "atan2 koordinat X: %1 in Y: %2";
Blockly.Msg["MATH_ATAN2_TOOLTIP"] = "Vrne arkus tangens točke (X, Y) v stopinjah med −180 in 180.";
Blockly.Msg["MATH_CHANGE_HELPURL"] = "https://en.wikipedia.org/wiki/Increment_and_decrement_operators";
Blockly.Msg["MATH_CHANGE_TITLE"] = "spremeni %1 za %2";
Blockly.Msg["MATH_CHANGE_TOOLTIP"] = "Prišteje število k spremenljivki »%1«.";
Blockly.Msg["MATH_CONSTANT_HELPURL"] = "https://sl.wikipedia.org/wiki/Matematična_konstanta";
Blockly.Msg["MATH_CONSTANT_TOOLTIP"] = "Vrne eno izmed pogostih konstant: π (3,141…), e (2,718…), φ (1,618…), sqrt(2) (1,414…), sqrt(½) (0,707 ...) ali ∞ (neskončno).";
Blockly.Msg["MATH_CONSTRAIN_HELPURL"] = "https://en.wikipedia.org/wiki/Clamping_%28graphics%29";
Blockly.Msg["MATH_CONSTRAIN_TITLE"] = "omeji %1 na najmanj %2 in največ %3";
Blockly.Msg["MATH_CONSTRAIN_TOOLTIP"] = "Omeji število, da bo med določenima (vključenima) mejama.";
Blockly.Msg["MATH_DIVISION_SYMBOL"] = "÷";
Blockly.Msg["MATH_IS_DIVISIBLE_BY"] = "je deljivo s/z";
Blockly.Msg["MATH_IS_EVEN"] = "je sodo";
Blockly.Msg["MATH_IS_NEGATIVE"] = "je negativno";
Blockly.Msg["MATH_IS_ODD"] = "je liho";
Blockly.Msg["MATH_IS_POSITIVE"] = "je pozitivno";
Blockly.Msg["MATH_IS_PRIME"] = "je praštevilo";
Blockly.Msg["MATH_IS_TOOLTIP"] = "Preveri, ali je število sodo, liho, praštevilo, celo, pozitivno, negativno ali če je deljivo z določenim številom. Vrne resnično ali neresnično.";
Blockly.Msg["MATH_IS_WHOLE"] = "je celo";
Blockly.Msg["MATH_MODULO_HELPURL"] = "https://sl.wikipedia.org/wiki/Modulo";
Blockly.Msg["MATH_MODULO_TITLE"] = "ostanek pri %1 ÷ %2";
Blockly.Msg["MATH_MODULO_TOOLTIP"] = "Vrne ostanek pri deljenju dveh števil.";
Blockly.Msg["MATH_MULTIPLICATION_SYMBOL"] = "×";
Blockly.Msg["MATH_NUMBER_HELPURL"] = "https://sl.wikipedia.org/wiki/%C5%A0tevilo";
Blockly.Msg["MATH_NUMBER_TOOLTIP"] = "Število.";
Blockly.Msg["MATH_ONLIST_HELPURL"] = "";  // untranslated
Blockly.Msg["MATH_ONLIST_OPERATOR_AVERAGE"] = "povprečje seznama";
Blockly.Msg["MATH_ONLIST_OPERATOR_MAX"] = "maksimum seznama";
Blockly.Msg["MATH_ONLIST_OPERATOR_MEDIAN"] = "mediana seznama";
Blockly.Msg["MATH_ONLIST_OPERATOR_MIN"] = "minimum seznama";
Blockly.Msg["MATH_ONLIST_OPERATOR_MODE"] = "modus seznama";
Blockly.Msg["MATH_ONLIST_OPERATOR_RANDOM"] = "naključni element seznama";
Blockly.Msg["MATH_ONLIST_OPERATOR_STD_DEV"] = "standardni odmik seznama";
Blockly.Msg["MATH_ONLIST_OPERATOR_SUM"] = "vsota seznama";
Blockly.Msg["MATH_ONLIST_TOOLTIP_AVERAGE"] = "Vrne povprečje (aritmetično sredino) števil v seznamu.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_MAX"] = "Vrne največje število v seznamu.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_MEDIAN"] = "Vrne mediano število v seznamu.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_MIN"] = "Vrne najmanjše število v seznamu.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_MODE"] = "Vrne seznam najpogostejšega(ih) elementa(-ov) v seznamu.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_RANDOM"] = "Vrne naključno število izmed števil v seznamu.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_STD_DEV"] = "Vrne standardni odmik elementov v seznamu.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_SUM"] = "Vrne vsoto vseh števil v seznamu.";
Blockly.Msg["MATH_POWER_SYMBOL"] = "^";
Blockly.Msg["MATH_RANDOM_FLOAT_HELPURL"] = "https://en.wikipedia.org/wiki/Random_number_generation";
Blockly.Msg["MATH_RANDOM_FLOAT_TITLE_RANDOM"] = "naključni ulomek";
Blockly.Msg["MATH_RANDOM_FLOAT_TOOLTIP"] = "Vrne naključni ulomek med (vključno) 0,0 in 1,0 (izključno).";
Blockly.Msg["MATH_RANDOM_INT_HELPURL"] = "https://en.wikipedia.org/wiki/Random_number_generation";
Blockly.Msg["MATH_RANDOM_INT_TITLE"] = "naključno število med %1 in %2";
Blockly.Msg["MATH_RANDOM_INT_TOOLTIP"] = "Vrne naključno število med dvema določenima mejama, vključno z mejama.";
Blockly.Msg["MATH_ROUND_HELPURL"] = "https://sl.wikipedia.org/wiki/Zaokro%C5%BEanje";
Blockly.Msg["MATH_ROUND_OPERATOR_ROUND"] = "zaokroži";
Blockly.Msg["MATH_ROUND_OPERATOR_ROUNDDOWN"] = "zaokroži navzdol";
Blockly.Msg["MATH_ROUND_OPERATOR_ROUNDUP"] = "zaokroži navzgor";
Blockly.Msg["MATH_ROUND_TOOLTIP"] = "Zaokroži število navzgor ali navzdol.";
Blockly.Msg["MATH_SINGLE_HELPURL"] = "https://sl.wikipedia.org/wiki/Kvadratni_koren";
Blockly.Msg["MATH_SINGLE_OP_ABSOLUTE"] = "absolutno";
Blockly.Msg["MATH_SINGLE_OP_ROOT"] = "kvadratni koren";
Blockly.Msg["MATH_SINGLE_TOOLTIP_ABS"] = "Vrne absolutno vrednost števila.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_EXP"] = "Vrne e na potenco števila.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_LN"] = "Vrne naravni logaritem števila.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_LOG10"] = "Vrne desetiški logaritem števila.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_NEG"] = "Vrne negacijo števila.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_POW10"] = "Vrne 10 na potenco števila.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_ROOT"] = "Vrne kvadratni koren števila.";
Blockly.Msg["MATH_SUBTRACTION_SYMBOL"] = "-";
Blockly.Msg["MATH_TRIG_ACOS"] = "acos";
Blockly.Msg["MATH_TRIG_ASIN"] = "asin";
Blockly.Msg["MATH_TRIG_ATAN"] = "atan";
Blockly.Msg["MATH_TRIG_COS"] = "cos";
Blockly.Msg["MATH_TRIG_HELPURL"] = "https://sl.wikipedia.org/wiki/Trigonometrična_funkcija";
Blockly.Msg["MATH_TRIG_SIN"] = "sin";
Blockly.Msg["MATH_TRIG_TAN"] = "tan";
Blockly.Msg["MATH_TRIG_TOOLTIP_ACOS"] = "Vrne arkus kosinus števila.";
Blockly.Msg["MATH_TRIG_TOOLTIP_ASIN"] = "Vrne arkus sinus števila.";
Blockly.Msg["MATH_TRIG_TOOLTIP_ATAN"] = "Vrne arkus tangens števila.";
Blockly.Msg["MATH_TRIG_TOOLTIP_COS"] = "Vrne kosinus kota v stopinjah (ne radianih).";
Blockly.Msg["MATH_TRIG_TOOLTIP_SIN"] = "Vrne sinus kota v stopinjah (ne radianih).";
Blockly.Msg["MATH_TRIG_TOOLTIP_TAN"] = "Vrne tangens kota v stopinjah (ne radianih).";
Blockly.Msg["NEW_COLOUR_VARIABLE"] = "Ustvari spremenljivko barve ...";
Blockly.Msg["NEW_NUMBER_VARIABLE"] = "Ustvari spremenljivko števila ...";
Blockly.Msg["NEW_STRING_VARIABLE"] = "Ustvari spremenljivko niza ...";
Blockly.Msg["NEW_VARIABLE"] = "Ustvari spremenljivko ...";
Blockly.Msg["NEW_VARIABLE_TITLE"] = "Ime nove spremenljivke:";
Blockly.Msg["NEW_VARIABLE_TYPE_TITLE"] = "Vrsta nove spremenljivke:";
Blockly.Msg["ORDINAL_NUMBER_SUFFIX"] = "";  // untranslated
Blockly.Msg["PROCEDURES_ALLOW_STATEMENTS"] = "dovoli stavke";
Blockly.Msg["PROCEDURES_BEFORE_PARAMS"] = "s/z:";
Blockly.Msg["PROCEDURES_CALLNORETURN_HELPURL"] = "https://sl.wikipedia.org/wiki/Subrutina";
Blockly.Msg["PROCEDURES_CALLNORETURN_TOOLTIP"] = "Izvede uporabniško določeno funkcijo »%1«.";
Blockly.Msg["PROCEDURES_CALLRETURN_HELPURL"] = "https://sl.wikipedia.org/wiki/Subrutina";
Blockly.Msg["PROCEDURES_CALLRETURN_TOOLTIP"] = "Izvede uporabniško funkcijo »%1« in uporabi njen izhod.";
Blockly.Msg["PROCEDURES_CALL_BEFORE_PARAMS"] = "s/z:";
Blockly.Msg["PROCEDURES_CREATE_DO"] = "Ustvari »%1«";
Blockly.Msg["PROCEDURES_DEFNORETURN_COMMENT"] = "Opiši funkcijo ...";
Blockly.Msg["PROCEDURES_DEFNORETURN_DO"] = "";  // untranslated
Blockly.Msg["PROCEDURES_DEFNORETURN_HELPURL"] = "https://en.wikipedia.org/wiki/Procedure_%28computer_science%29";
Blockly.Msg["PROCEDURES_DEFNORETURN_PROCEDURE"] = "nekaj";
Blockly.Msg["PROCEDURES_DEFNORETURN_TITLE"] = "izvedi";
Blockly.Msg["PROCEDURES_DEFNORETURN_TOOLTIP"] = "Ustvari funkcijo brez izhoda.";
Blockly.Msg["PROCEDURES_DEFRETURN_HELPURL"] = "https://en.wikipedia.org/wiki/Procedure_%28computer_science%29";
Blockly.Msg["PROCEDURES_DEFRETURN_RETURN"] = "vrni";
Blockly.Msg["PROCEDURES_DEFRETURN_TOOLTIP"] = "Ustvari funkcijo z izhodom.";
Blockly.Msg["PROCEDURES_DEF_DUPLICATE_WARNING"] = "Pozor: Ta funkcija ima podvojene parametre.";
Blockly.Msg["PROCEDURES_HIGHLIGHT_DEF"] = "Označi blok funkcije";
Blockly.Msg["PROCEDURES_IFRETURN_HELPURL"] = "http://c2.com/cgi/wiki?GuardClause";
Blockly.Msg["PROCEDURES_IFRETURN_TOOLTIP"] = "Če je vrednost resnična, vrne drugo vrednost.";
Blockly.Msg["PROCEDURES_IFRETURN_WARNING"] = "Pozor: Ta blok lahko uporabite samo v bloku funkcije.";
Blockly.Msg["PROCEDURES_MUTATORARG_TITLE"] = "ime vnosa:";
Blockly.Msg["PROCEDURES_MUTATORARG_TOOLTIP"] = "Funkciji doda vnos.";
Blockly.Msg["PROCEDURES_MUTATORCONTAINER_TITLE"] = "vnosi";
Blockly.Msg["PROCEDURES_MUTATORCONTAINER_TOOLTIP"] = "Doda, odstrani ali spremeni vrstni red vnosov za to funkcijo.";
Blockly.Msg["REDO"] = "Ponovi";
Blockly.Msg["REMOVE_COMMENT"] = "Odstrani komentar";
Blockly.Msg["RENAME_VARIABLE"] = "Preimenuj spremenljivko ...";
Blockly.Msg["RENAME_VARIABLE_TITLE"] = "Preimenuj vse spremenljivke »%1« v:";
Blockly.Msg["TEXT_APPEND_HELPURL"] = "https://github.com/google/blockly/wiki/Text#text-modification";
Blockly.Msg["TEXT_APPEND_TITLE"] = "k %1 dodaj besedilo %2";
Blockly.Msg["TEXT_APPEND_TOOLTIP"] = "Doda besedilo k spremenljivki »%1«.";
Blockly.Msg["TEXT_CHANGECASE_HELPURL"] = "https://github.com/google/blockly/wiki/Text#adjusting-text-case";
Blockly.Msg["TEXT_CHANGECASE_OPERATOR_LOWERCASE"] = "v male črke";
Blockly.Msg["TEXT_CHANGECASE_OPERATOR_TITLECASE"] = "v Velike Začetnice";
Blockly.Msg["TEXT_CHANGECASE_OPERATOR_UPPERCASE"] = "v VELIKE ČRKE";
Blockly.Msg["TEXT_CHANGECASE_TOOLTIP"] = "Vrne kopijo besedila v drugi obliki.";
Blockly.Msg["TEXT_CHARAT_FIRST"] = "vrni prvo črko";
Blockly.Msg["TEXT_CHARAT_FROM_END"] = "vrni črko št. od konca";
Blockly.Msg["TEXT_CHARAT_FROM_START"] = "vrni črko št.";
Blockly.Msg["TEXT_CHARAT_HELPURL"] = "https://github.com/google/blockly/wiki/Text#extracting-text";
Blockly.Msg["TEXT_CHARAT_LAST"] = "vrni zadnjo črko";
Blockly.Msg["TEXT_CHARAT_RANDOM"] = "vrni naključno črko";
Blockly.Msg["TEXT_CHARAT_TAIL"] = "";  // untranslated
Blockly.Msg["TEXT_CHARAT_TITLE"] = "v besedilu %1 %2";
Blockly.Msg["TEXT_CHARAT_TOOLTIP"] = "Vrne črko na določenem mestu.";
Blockly.Msg["TEXT_COUNT_HELPURL"] = "https://github.com/google/blockly/wiki/Text#counting-substrings";  // untranslated
Blockly.Msg["TEXT_COUNT_MESSAGE0"] = "preštej %1 v %2";
Blockly.Msg["TEXT_COUNT_TOOLTIP"] = "Preštej, kolikokrat se neko besedilo pojavi v drugem besedilu.";
Blockly.Msg["TEXT_CREATE_JOIN_ITEM_TOOLTIP"] = "Doda element k besedilu.";
Blockly.Msg["TEXT_CREATE_JOIN_TITLE_JOIN"] = "združi";
Blockly.Msg["TEXT_CREATE_JOIN_TOOLTIP"] = "Doda, odstrani ali spremeni vrstni red odsekov za ponovno nastavitev tega bloka besedila.";
Blockly.Msg["TEXT_GET_SUBSTRING_END_FROM_END"] = "do črke št. od konca";
Blockly.Msg["TEXT_GET_SUBSTRING_END_FROM_START"] = "do črke št.";
Blockly.Msg["TEXT_GET_SUBSTRING_END_LAST"] = "do zadnje črke";
Blockly.Msg["TEXT_GET_SUBSTRING_HELPURL"] = "https://github.com/google/blockly/wiki/Text#extracting-a-region-of-text";
Blockly.Msg["TEXT_GET_SUBSTRING_INPUT_IN_TEXT"] = "iz besedila";
Blockly.Msg["TEXT_GET_SUBSTRING_START_FIRST"] = "vrni podniz od prve črke";
Blockly.Msg["TEXT_GET_SUBSTRING_START_FROM_END"] = "vrni podniz od črke št. od konca";
Blockly.Msg["TEXT_GET_SUBSTRING_START_FROM_START"] = "vrni podniz od črke št.";
Blockly.Msg["TEXT_GET_SUBSTRING_TAIL"] = "";  // untranslated
Blockly.Msg["TEXT_GET_SUBSTRING_TOOLTIP"] = "Vrne določen del besedila.";
Blockly.Msg["TEXT_INDEXOF_HELPURL"] = "https://github.com/google/blockly/wiki/Text#finding-text";
Blockly.Msg["TEXT_INDEXOF_OPERATOR_FIRST"] = "najdi prvo pojavitev besedila";
Blockly.Msg["TEXT_INDEXOF_OPERATOR_LAST"] = "najdi zadnjo pojavitev besedila";
Blockly.Msg["TEXT_INDEXOF_TITLE"] = "v besedilu %1 %2 %3";
Blockly.Msg["TEXT_INDEXOF_TOOLTIP"] = "Vrne mesto (indeks) prve/zadnje pojavitve drugega besedila v prvem besedilu. Če besedila ne najde, vrne %1.";
Blockly.Msg["TEXT_ISEMPTY_HELPURL"] = "https://github.com/google/blockly/wiki/Text#checking-for-empty-text";
Blockly.Msg["TEXT_ISEMPTY_TITLE"] = "%1 je prazno";
Blockly.Msg["TEXT_ISEMPTY_TOOLTIP"] = "Vrne resnično, če je določeno besedilo prazno.";
Blockly.Msg["TEXT_JOIN_HELPURL"] = "https://github.com/google/blockly/wiki/Text#text-creation";
Blockly.Msg["TEXT_JOIN_TITLE_CREATEWITH"] = "ustvari besedilo iz";
Blockly.Msg["TEXT_JOIN_TOOLTIP"] = "Ustvari besedilo tako, da združi poljubno število elementov.";
Blockly.Msg["TEXT_LENGTH_HELPURL"] = "https://github.com/google/blockly/wiki/Text#text-modification";
Blockly.Msg["TEXT_LENGTH_TITLE"] = "dolžina %1";
Blockly.Msg["TEXT_LENGTH_TOOLTIP"] = "Vrne število znakov (vključno s presledki) v določenem besedilu.";
Blockly.Msg["TEXT_PRINT_HELPURL"] = "https://github.com/google/blockly/wiki/Text#printing-text";
Blockly.Msg["TEXT_PRINT_TITLE"] = "izpiši %1";
Blockly.Msg["TEXT_PRINT_TOOLTIP"] = "Izpiše določeno besedilo, številko ali drugo vrednost.";
Blockly.Msg["TEXT_PROMPT_HELPURL"] = "https://github.com/google/blockly/wiki/Text#getting-input-from-the-user";
Blockly.Msg["TEXT_PROMPT_TOOLTIP_NUMBER"] = "Vpraša uporabnika za vnos številke.";
Blockly.Msg["TEXT_PROMPT_TOOLTIP_TEXT"] = "Vpraša uporabnika za vnos besedila.";
Blockly.Msg["TEXT_PROMPT_TYPE_NUMBER"] = "vprašaj za številko s sporočilom";
Blockly.Msg["TEXT_PROMPT_TYPE_TEXT"] = "vprašaj za besedilo s sporočilom";
Blockly.Msg["TEXT_REPLACE_HELPURL"] = "https://github.com/google/blockly/wiki/Text#replacing-substrings";  // untranslated
Blockly.Msg["TEXT_REPLACE_MESSAGE0"] = "zamenjaj %1 z %2 v %3";
Blockly.Msg["TEXT_REPLACE_TOOLTIP"] = "Zamenja vse pojavitve besedila v drugem besedilu.";
Blockly.Msg["TEXT_REVERSE_HELPURL"] = "https://github.com/google/blockly/wiki/Text#reversing-text";  // untranslated
Blockly.Msg["TEXT_REVERSE_MESSAGE0"] = "obrni %1";
Blockly.Msg["TEXT_REVERSE_TOOLTIP"] = "Obrne vrstni red znakov v besedilu.";
Blockly.Msg["TEXT_TEXT_HELPURL"] = "https://sl.wikipedia.org/wiki/Niz";
Blockly.Msg["TEXT_TEXT_TOOLTIP"] = "Črka, beseda ali vrstica besedila.";
Blockly.Msg["TEXT_TRIM_HELPURL"] = "https://github.com/google/blockly/wiki/Text#trimming-removing-spaces";
Blockly.Msg["TEXT_TRIM_OPERATOR_BOTH"] = "odstrani presledke z obeh strani";
Blockly.Msg["TEXT_TRIM_OPERATOR_LEFT"] = "odstrani presledke z leve strani";
Blockly.Msg["TEXT_TRIM_OPERATOR_RIGHT"] = "odstrani presledke z desne strani";
Blockly.Msg["TEXT_TRIM_TOOLTIP"] = "Vrne kopijo besedila z odstranjenimi presledki z ene ali obeh strani.";
Blockly.Msg["TODAY"] = "Danes";
Blockly.Msg["UNDO"] = "Razveljavi";
Blockly.Msg["UNNAMED_KEY"] = "nepoimenovano";
Blockly.Msg["VARIABLES_DEFAULT_NAME"] = "element";
Blockly.Msg["VARIABLES_GET_CREATE_SET"] = "Ustvari »nastavi %1«";
Blockly.Msg["VARIABLES_GET_HELPURL"] = "https://github.com/google/blockly/wiki/Variables#get";
Blockly.Msg["VARIABLES_GET_TOOLTIP"] = "Vrne vrednost spremenljivke.";
Blockly.Msg["VARIABLES_SET"] = "nastavi %1 na %2";
Blockly.Msg["VARIABLES_SET_CREATE_GET"] = "Ustvari »vrni %1«";
Blockly.Msg["VARIABLES_SET_HELPURL"] = "https://github.com/google/blockly/wiki/Variables#set";
Blockly.Msg["VARIABLES_SET_TOOLTIP"] = "Nastavi, da je vrednost spremenljivke enaka vnosu.";
Blockly.Msg["VARIABLE_ALREADY_EXISTS"] = "Spremenljivka »%1« že obstaja.";
Blockly.Msg["VARIABLE_ALREADY_EXISTS_FOR_ANOTHER_TYPE"] = "Spremenljivka z imenom »%1« za tip »%2« že obstaja.";
Blockly.Msg["WORKSPACE_ARIA_LABEL"] = "Blocklyjev delovni prostor";
Blockly.Msg["WORKSPACE_COMMENT_DEFAULT_TEXT"] = "Povej nekaj ...";
Blockly.Msg["CONTROLS_FOREACH_INPUT_DO"] = Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"];
Blockly.Msg["CONTROLS_FOR_INPUT_DO"] = Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"];
Blockly.Msg["CONTROLS_IF_ELSEIF_TITLE_ELSEIF"] = Blockly.Msg["CONTROLS_IF_MSG_ELSEIF"];
Blockly.Msg["CONTROLS_IF_ELSE_TITLE_ELSE"] = Blockly.Msg["CONTROLS_IF_MSG_ELSE"];
Blockly.Msg["CONTROLS_IF_IF_TITLE_IF"] = Blockly.Msg["CONTROLS_IF_MSG_IF"];
Blockly.Msg["CONTROLS_IF_MSG_THEN"] = Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"];
Blockly.Msg["CONTROLS_WHILEUNTIL_INPUT_DO"] = Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"];
Blockly.Msg["LISTS_CREATE_WITH_ITEM_TITLE"] = Blockly.Msg["VARIABLES_DEFAULT_NAME"];
Blockly.Msg["LISTS_GET_INDEX_HELPURL"] = Blockly.Msg["LISTS_INDEX_OF_HELPURL"];
Blockly.Msg["LISTS_GET_INDEX_INPUT_IN_LIST"] = Blockly.Msg["LISTS_INLIST"];
Blockly.Msg["LISTS_GET_SUBLIST_INPUT_IN_LIST"] = Blockly.Msg["LISTS_INLIST"];
Blockly.Msg["LISTS_INDEX_OF_INPUT_IN_LIST"] = Blockly.Msg["LISTS_INLIST"];
Blockly.Msg["LISTS_SET_INDEX_INPUT_IN_LIST"] = Blockly.Msg["LISTS_INLIST"];
Blockly.Msg["MATH_CHANGE_TITLE_ITEM"] = Blockly.Msg["VARIABLES_DEFAULT_NAME"];
Blockly.Msg["PROCEDURES_DEFRETURN_COMMENT"] = Blockly.Msg["PROCEDURES_DEFNORETURN_COMMENT"];
Blockly.Msg["PROCEDURES_DEFRETURN_DO"] = Blockly.Msg["PROCEDURES_DEFNORETURN_DO"];
Blockly.Msg["PROCEDURES_DEFRETURN_PROCEDURE"] = Blockly.Msg["PROCEDURES_DEFNORETURN_PROCEDURE"];
Blockly.Msg["PROCEDURES_DEFRETURN_TITLE"] = Blockly.Msg["PROCEDURES_DEFNORETURN_TITLE"];
Blockly.Msg["TEXT_APPEND_VARIABLE"] = Blockly.Msg["VARIABLES_DEFAULT_NAME"];
Blockly.Msg["TEXT_CREATE_JOIN_ITEM_TITLE_ITEM"] = Blockly.Msg["VARIABLES_DEFAULT_NAME"];

Blockly.Msg["MATH_HUE"] = "230";
Blockly.Msg["LOOPS_HUE"] = "120";
Blockly.Msg["LISTS_HUE"] = "260";
Blockly.Msg["LOGIC_HUE"] = "210";
Blockly.Msg["VARIABLES_HUE"] = "330";
Blockly.Msg["TEXTS_HUE"] = "160";
Blockly.Msg["PROCEDURES_HUE"] = "290";
Blockly.Msg["COLOUR_HUE"] = "20";
Blockly.Msg["VARIABLES_DYNAMIC_HUE"] = "310";