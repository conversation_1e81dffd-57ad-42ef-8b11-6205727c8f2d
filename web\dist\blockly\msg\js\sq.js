// This file was automatically generated.  Do not modify.

'use strict';

Blockly.Msg["ADD_COMMENT"] = "Vendos nje Koment";
Blockly.Msg["CANNOT_DELETE_VARIABLE_PROCEDURE"] = "Nuk mund të fshihet variabla '%1' sepse është pjesë e definicionit të funksionit '%2'";
Blockly.Msg["CHANGE_VALUE_TITLE"] = "Ndrysho Vlerat:";
Blockly.Msg["CLEAN_UP"] = "Pastro blloqet";
Blockly.Msg["COLLAPSED_WARNINGS_WARNING"] = "Blloqet e shembura përmbajnë paralajmërime.";
Blockly.Msg["COLLAPSE_ALL"] = "Mbyll blloqet";
Blockly.Msg["COLLAPSE_BLOCK"] = "Mbyll bllokun";
Blockly.Msg["COLOUR_BLEND_COLOUR1"] = "Ngjyra 1";
Blockly.Msg["COLOUR_BLEND_COLOUR2"] = "Ngjyra 2";
Blockly.Msg["COLOUR_BLEND_HELPURL"] = "http://meyerweb.com/eric/tools/color-blend/";
Blockly.Msg["COLOUR_BLEND_RATIO"] = "Perpjesetim";
Blockly.Msg["COLOUR_BLEND_TITLE"] = "Përzierje";
Blockly.Msg["COLOUR_BLEND_TOOLTIP"] = "Perzien dy ngjyra së bashku me një raport të dhënë (0.0 - 1.0).";
Blockly.Msg["COLOUR_PICKER_HELPURL"] = "http://en.wikipedia.org/wiki/Color";
Blockly.Msg["COLOUR_PICKER_TOOLTIP"] = "Zgjidh nje ngjyre nga nje game ngjyrash.";
Blockly.Msg["COLOUR_RANDOM_HELPURL"] = "http://randomcolour.com";  // untranslated
Blockly.Msg["COLOUR_RANDOM_TITLE"] = "ngjyre e rastesishme";
Blockly.Msg["COLOUR_RANDOM_TOOLTIP"] = "Zgjidhni një ngjyrë në mënyrë të rastësishme.";
Blockly.Msg["COLOUR_RGB_BLUE"] = "blu";
Blockly.Msg["COLOUR_RGB_GREEN"] = "jeshile";
Blockly.Msg["COLOUR_RGB_HELPURL"] = "http://www.december.com/html/spec/colorper.html";
Blockly.Msg["COLOUR_RGB_RED"] = "e kuqe";
Blockly.Msg["COLOUR_RGB_TITLE"] = "ngjyre me";
Blockly.Msg["COLOUR_RGB_TOOLTIP"] = "Krijo një ngjyrë me shumën e specifikuar te te kuqes, te gjelbëres, dhe bluse. Te gjitha vlerat duhet te jene mes 0 dhe 100.";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_HELPURL"] = "https://github.com/google/blockly/wiki/Loops#loop-termination-blocks";  // untranslated
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_OPERATOR_BREAK"] = "dil nga nje faze perseritese";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_OPERATOR_CONTINUE"] = "vazhdo me elementin tjeter te nje faze perseritese";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_TOOLTIP_BREAK"] = "Ndahu nga unaza.";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_TOOLTIP_CONTINUE"] = "Kapërce pjesën e mbetur të unazës, dhe vazhdo me ripërsëritjen tjetër.";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_WARNING"] = "Paralajmërim: Ky bllok mund të përdoret vetëm brenda unazës.";
Blockly.Msg["CONTROLS_FOREACH_HELPURL"] = "https://github.com/google/blockly/wiki/Loops#for-each";  // untranslated
Blockly.Msg["CONTROLS_FOREACH_TITLE"] = "per cdo produkt %1 ne liste %2";
Blockly.Msg["CONTROLS_FOREACH_TOOLTIP"] = "Per cdo produkt ne nje \"liste\" \"vendos\" ndryshoren '%1' produktit, dhe pastaj bej disa deklarata.";
Blockly.Msg["CONTROLS_FOR_HELPURL"] = "https://github.com/google/blockly/wiki/Loops#count-with";  // untranslated
Blockly.Msg["CONTROLS_FOR_TITLE"] = "numero me %1 nga %2 ne %3 me nga %4";
Blockly.Msg["CONTROLS_FOR_TOOLTIP"] = "Bëje identifikuesin \"%1\" që ta ketë vlerat prej numrit të fillimit deri tek numri i fundit, duke numëruar nga intervali i specifikuar, dhe ti bëj blloqet e specifikuara.";
Blockly.Msg["CONTROLS_IF_ELSEIF_TOOLTIP"] = "\"Vendos\" \"kushtein\"tek \"pjesa\" \"if\"";
Blockly.Msg["CONTROLS_IF_ELSE_TOOLTIP"] = "Shto një përfundues, që i mbërrin të gjitha kushtet në bllokun nëse.";
Blockly.Msg["CONTROLS_IF_HELPURL"] = "https://github.com/google/blockly/wiki/IfElse";  // untranslated
Blockly.Msg["CONTROLS_IF_IF_TOOLTIP"] = "Shto, fshij, ose rirregullo sektoret për ta rikonfiguruar këte bllok nëse.";
Blockly.Msg["CONTROLS_IF_MSG_ELSE"] = "përndryshe";
Blockly.Msg["CONTROLS_IF_MSG_ELSEIF"] = "përndryshe nëse";
Blockly.Msg["CONTROLS_IF_MSG_IF"] = "nëse";
Blockly.Msg["CONTROLS_IF_TOOLTIP_1"] = "Nëse një vlerë është e saktë, atëherë ekzekuto disa fjali.";
Blockly.Msg["CONTROLS_IF_TOOLTIP_2"] = "Nëse një vlerë është e saktë, atëherë ekzekuto bllokun e parë të fjalive. Përndryshe, ekzekuto bllokun e dytë të fjalive.";
Blockly.Msg["CONTROLS_IF_TOOLTIP_3"] = "Nëse vlera e parë është e saktë, atëherë ekzekuto bllokun e parë të fjalive. Përndryshe, nëse vlera e dytë është e saktë, ekzekuto bllokun e dytë të fjalive.";
Blockly.Msg["CONTROLS_IF_TOOLTIP_4"] = "Nëse vlera e parë është e saktë, atëherë ekzekuto bllokun e parë të fjalive. Përndryshe, nëse vlera e dytë është e saktë, ekzekuto bllokun e dytë të fjalive. Nëse asnjëra nga vlerat nuk është e saktë, ekzekuto bllokun e fundit të fjalive.";
Blockly.Msg["CONTROLS_REPEAT_HELPURL"] = "http://en.wikipedia.org/wiki/For_loop";
Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"] = "ekzekuto";
Blockly.Msg["CONTROLS_REPEAT_TITLE"] = "përsërit %1 herë";
Blockly.Msg["CONTROLS_REPEAT_TOOLTIP"] = "Ekzekuto disa fjali disa herë.";
Blockly.Msg["CONTROLS_WHILEUNTIL_HELPURL"] = "https://github.com/google/blockly/wiki/Loops#repeat";  // untranslated
Blockly.Msg["CONTROLS_WHILEUNTIL_OPERATOR_UNTIL"] = "përsërit derisa";
Blockly.Msg["CONTROLS_WHILEUNTIL_OPERATOR_WHILE"] = "përsërit përderisa";
Blockly.Msg["CONTROLS_WHILEUNTIL_TOOLTIP_UNTIL"] = "Përderisa një vlerë është e pasaktë, atëherë ekzekuto disa fjali.";
Blockly.Msg["CONTROLS_WHILEUNTIL_TOOLTIP_WHILE"] = "Përderisa një vlerë është e saktë, atëherë ekzekuto disa fjali.";
Blockly.Msg["DELETE_ALL_BLOCKS"] = "Fshijë të gjitha %1 të blloqeve?";
Blockly.Msg["DELETE_BLOCK"] = "Fshij bllokun";
Blockly.Msg["DELETE_VARIABLE"] = "Fshi variablën '%1'";
Blockly.Msg["DELETE_VARIABLE_CONFIRMATION"] = "Fshi përdorimin %1 të variablës '%2'?";
Blockly.Msg["DELETE_X_BLOCKS"] = "Fshij %1 blloqe";
Blockly.Msg["DIALOG_CANCEL"] = "Anulo";
Blockly.Msg["DIALOG_OK"] = "Në rregull";
Blockly.Msg["DISABLE_BLOCK"] = "Çaktivizo bllokun";
Blockly.Msg["DUPLICATE_BLOCK"] = "Kopjo";
Blockly.Msg["DUPLICATE_COMMENT"] = "Koment Dublikatë";
Blockly.Msg["ENABLE_BLOCK"] = "Aktivizo bllokun";
Blockly.Msg["EXPAND_ALL"] = "Zmadho blloqet";
Blockly.Msg["EXPAND_BLOCK"] = "Zmadho bllokun";
Blockly.Msg["EXTERNAL_INPUTS"] = "Hyrjet e jashtme";
Blockly.Msg["HELP"] = "Ndihmë";
Blockly.Msg["INLINE_INPUTS"] = "Hyrjet e brendshme";
Blockly.Msg["LISTS_CREATE_EMPTY_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#create-empty-list";
Blockly.Msg["LISTS_CREATE_EMPTY_TITLE"] = "krijo një listë të zbrazët";
Blockly.Msg["LISTS_CREATE_EMPTY_TOOLTIP"] = "Kthen një listë, te gjatësisë 0, duke mos përmbajtur asnjë regjistrim të të dhënave";
Blockly.Msg["LISTS_CREATE_WITH_CONTAINER_TITLE_ADD"] = "listë";
Blockly.Msg["LISTS_CREATE_WITH_CONTAINER_TOOLTIP"] = "Shto, fshij, ose rirregullo sektoret për ta rikonfiguruar këtë bllok të listës.";
Blockly.Msg["LISTS_CREATE_WITH_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#create-list-with";  // untranslated
Blockly.Msg["LISTS_CREATE_WITH_INPUT_WITH"] = "krijo listë me";
Blockly.Msg["LISTS_CREATE_WITH_ITEM_TOOLTIP"] = "Shto një send në listë.";
Blockly.Msg["LISTS_CREATE_WITH_TOOLTIP"] = "Krijo një listë me ndonjë numbër të sendeve.";
Blockly.Msg["LISTS_GET_INDEX_FIRST"] = "i parë";
Blockly.Msg["LISTS_GET_INDEX_FROM_END"] = "# nga fundi";
Blockly.Msg["LISTS_GET_INDEX_FROM_START"] = "#";
Blockly.Msg["LISTS_GET_INDEX_GET"] = "merr";
Blockly.Msg["LISTS_GET_INDEX_GET_REMOVE"] = "merr dhe fshij";
Blockly.Msg["LISTS_GET_INDEX_LAST"] = "i fundit";
Blockly.Msg["LISTS_GET_INDEX_RANDOM"] = "i rastësishëm";
Blockly.Msg["LISTS_GET_INDEX_REMOVE"] = "largo";
Blockly.Msg["LISTS_GET_INDEX_TAIL"] = "";  // untranslated
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_FIRST"] = "Rikthen tek artikulli i par në list.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_FROM"] = "Kthen një send në pozicionin e specifikuar në listë.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_LAST"] = "Kthen artikullin e fundit në list.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_RANDOM"] = "Kthen një send të rastësishëm në listë.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_FIRST"] = "Fshin dhe kthen sendin e parë në listë.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_FROM"] = "Fshin dhe kthen sendin në pozicionin e specifikuar në listë.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_LAST"] = "Fshin dhe kthen sendin e fundit në listë.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_RANDOM"] = "Fshin dhe kthen një send të rastësishëm në listë.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_REMOVE_FIRST"] = "Fshin sendin e parë në listë.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_REMOVE_FROM"] = "Fshin sendin në pozicionin e specifikuar në listë.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_REMOVE_LAST"] = "Fshin sendin e fundit në listë.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_REMOVE_RANDOM"] = "Kthen një send të rastësishëm në listë.";
Blockly.Msg["LISTS_GET_SUBLIST_END_FROM_END"] = "tek # nga fundi";
Blockly.Msg["LISTS_GET_SUBLIST_END_FROM_START"] = "tek #";
Blockly.Msg["LISTS_GET_SUBLIST_END_LAST"] = "tek i fundit";
Blockly.Msg["LISTS_GET_SUBLIST_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#getting-a-sublist";  // untranslated
Blockly.Msg["LISTS_GET_SUBLIST_START_FIRST"] = "merr nën-listën nga i pari";
Blockly.Msg["LISTS_GET_SUBLIST_START_FROM_END"] = "merr nën listën nga # nga fundi";
Blockly.Msg["LISTS_GET_SUBLIST_START_FROM_START"] = "merr nën-listën nga #";
Blockly.Msg["LISTS_GET_SUBLIST_TAIL"] = "";  // untranslated
Blockly.Msg["LISTS_GET_SUBLIST_TOOLTIP"] = "Krijon në kopje të pjesës së specifikuar të listës.";
Blockly.Msg["LISTS_INDEX_FROM_END_TOOLTIP"] = "%1 është sendi i fundit.";
Blockly.Msg["LISTS_INDEX_FROM_START_TOOLTIP"] = "%1 është sendi i parë.";
Blockly.Msg["LISTS_INDEX_OF_FIRST"] = "gjen ndodhjen e parë të sendit";
Blockly.Msg["LISTS_INDEX_OF_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#getting-items-from-a-list";  // untranslated
Blockly.Msg["LISTS_INDEX_OF_LAST"] = "gjen ndodhjen e fundit të sendit";
Blockly.Msg["LISTS_INDEX_OF_TOOLTIP"] = "Kthen indeksin e ndodhjes së parë/fudit të sendit në listë. Kthen %1 nëse teksti nuk është gjetur.";
Blockly.Msg["LISTS_INLIST"] = "në listë";
Blockly.Msg["LISTS_ISEMPTY_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#is-empty";  // untranslated
Blockly.Msg["LISTS_ISEMPTY_TITLE"] = "%1 është e zbraztë";
Blockly.Msg["LISTS_ISEMPTY_TOOLTIP"] = "Kthehet i saktë nëse lista është e zbraztë.";
Blockly.Msg["LISTS_LENGTH_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#length-of";  // untranslated
Blockly.Msg["LISTS_LENGTH_TITLE"] = "gjatësia e %1";
Blockly.Msg["LISTS_LENGTH_TOOLTIP"] = "Kthen gjatësinë e listës.";
Blockly.Msg["LISTS_REPEAT_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#create-list-with";  // untranslated
Blockly.Msg["LISTS_REPEAT_TITLE"] = "krijo listën me sendin %1 të përsëritur %2 herë";
Blockly.Msg["LISTS_REPEAT_TOOLTIP"] = "Krijon në listë qe përmban vlerën e dhënë të përsëritur aq herë sa numri i specifikuar.";
Blockly.Msg["LISTS_REVERSE_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#reversing-a-list";  // untranslated
Blockly.Msg["LISTS_REVERSE_MESSAGE0"] = "kthe %1";
Blockly.Msg["LISTS_REVERSE_TOOLTIP"] = "Ndërro renditjen e një kopjeje të listës.";
Blockly.Msg["LISTS_SET_INDEX_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#in-list--set";  // untranslated
Blockly.Msg["LISTS_SET_INDEX_INPUT_TO"] = "sikurse";
Blockly.Msg["LISTS_SET_INDEX_INSERT"] = "fut në";
Blockly.Msg["LISTS_SET_INDEX_SET"] = "vendos";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_INSERT_FIRST"] = "Fut sendin në fillim të listës.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_INSERT_FROM"] = "Fut sendin në pozicionin e specifikuar të listës.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_INSERT_LAST"] = "Bashkangjit sendin në fund të listës.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_INSERT_RANDOM"] = "Fut sendin rastësisht në listë.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_SET_FIRST"] = "Vendos sendin e parë në listë.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_SET_FROM"] = "Vendos sendin në pozicionin e specifikuar në listë.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_SET_LAST"] = "Vendos sendin e fundit në listë.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_SET_RANDOM"] = "Vendos një send të rastësishëm në listë.";
Blockly.Msg["LISTS_SORT_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#sorting-a-list";
Blockly.Msg["LISTS_SORT_ORDER_ASCENDING"] = "ngjitje";
Blockly.Msg["LISTS_SORT_ORDER_DESCENDING"] = "zbritje";
Blockly.Msg["LISTS_SORT_TITLE"] = "rendit %1 %2 %3";
Blockly.Msg["LISTS_SORT_TOOLTIP"] = "Rendit një kopje të listës.";
Blockly.Msg["LISTS_SORT_TYPE_IGNORECASE"] = "alfabetike, injoro madhësinë e shkronjave";
Blockly.Msg["LISTS_SORT_TYPE_NUMERIC"] = "numerike";
Blockly.Msg["LISTS_SORT_TYPE_TEXT"] = "alfabetike";
Blockly.Msg["LISTS_SPLIT_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#splitting-strings-and-joining-lists";  // untranslated
Blockly.Msg["LISTS_SPLIT_LIST_FROM_TEXT"] = "bëj listë nga teksti";
Blockly.Msg["LISTS_SPLIT_TEXT_FROM_LIST"] = "bëj tekst nga lista";
Blockly.Msg["LISTS_SPLIT_TOOLTIP_JOIN"] = "Ngjit një listë tekstesh në një, të ndara me ndarës.";
Blockly.Msg["LISTS_SPLIT_TOOLTIP_SPLIT"] = "Ndaj tekstin në një listë tekstesh, duke ndarë në secilin ndarës.";
Blockly.Msg["LISTS_SPLIT_WITH_DELIMITER"] = "me ndarës";
Blockly.Msg["LOGIC_BOOLEAN_FALSE"] = "e pasaktë";
Blockly.Msg["LOGIC_BOOLEAN_HELPURL"] = "https://github.com/google/blockly/wiki/Logic#values";  // untranslated
Blockly.Msg["LOGIC_BOOLEAN_TOOLTIP"] = "Kthehet ose të saktë ose të pasaktë.";
Blockly.Msg["LOGIC_BOOLEAN_TRUE"] = "e saktë";
Blockly.Msg["LOGIC_COMPARE_HELPURL"] = "http://en.wikipedia.org/wiki/Inequality_(mathematics)";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_EQ"] = "Ktheje të saktë nëse të dy hyrjet janë të barabarta me njëra-tjetrën.";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_GT"] = "Ktheje të saktë nëse hyrja e parë është më e madhe se hyrja e dytë.";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_GTE"] = "Ktheje të saktë nëse hyrja e parë është më e madhe ose e barabartë me hyrjen e dytë.";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_LT"] = "Ktheje të saktë nëse hyrja e parë është më e vogël se hyrja e dytë.";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_LTE"] = "Ktheje të saktë nëse hyrja e parë është më e vogël ose e barabartë me hyrjen e dytë.";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_NEQ"] = "Ktheje të saktë nëse të dy hyrjet nuk janë të barabarta me njëra-tjetrën.";
Blockly.Msg["LOGIC_NEGATE_HELPURL"] = "https://github.com/google/blockly/wiki/Logic#not";  // untranslated
Blockly.Msg["LOGIC_NEGATE_TITLE"] = "jo %1";
Blockly.Msg["LOGIC_NEGATE_TOOLTIP"] = "Kthehet e saktë nëse hyrja është e pasaktë. Kthehet e pasaktë nëse hyrja është e saktë.";
Blockly.Msg["LOGIC_NULL"] = "pavlerë";
Blockly.Msg["LOGIC_NULL_HELPURL"] = "http://en.wikipedia.org/wiki/Nullable_type";
Blockly.Msg["LOGIC_NULL_TOOLTIP"] = "Kthehet e pavlerë.";
Blockly.Msg["LOGIC_OPERATION_AND"] = "dhe";
Blockly.Msg["LOGIC_OPERATION_HELPURL"] = "https://github.com/google/blockly/wiki/Logic#logical-operations";  // untranslated
Blockly.Msg["LOGIC_OPERATION_OR"] = "ose";
Blockly.Msg["LOGIC_OPERATION_TOOLTIP_AND"] = "Kthehet të saktë nëse të dy hyrjet janë të sakta.";
Blockly.Msg["LOGIC_OPERATION_TOOLTIP_OR"] = "Kthehet e saktë nëse së paku njëra nga hyrjet është e saktë.";
Blockly.Msg["LOGIC_TERNARY_CONDITION"] = "test";
Blockly.Msg["LOGIC_TERNARY_HELPURL"] = "http://en.wikipedia.org/wiki/%3F:";
Blockly.Msg["LOGIC_TERNARY_IF_FALSE"] = "nëse e pasaktë";
Blockly.Msg["LOGIC_TERNARY_IF_TRUE"] = "nëse e saktë";
Blockly.Msg["LOGIC_TERNARY_TOOLTIP"] = "Kontrollo kushtin në 'test'. Nëse kushti është i saktë, kthen vlerën 'nëse e saktë'; përndryshe kthen vlerën 'nëse e pasaktë'.";
Blockly.Msg["MATH_ADDITION_SYMBOL"] = "+";
Blockly.Msg["MATH_ARITHMETIC_HELPURL"] = "http://sq.wikipedia.org/wiki/Aritmetika";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_ADD"] = "Kthen shumën e dy numrave.";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_DIVIDE"] = "Kthen herësin e dy numrave.";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_MINUS"] = "Kthen ndryshimin e dy numrave.";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_MULTIPLY"] = "Kthen produktin e dy numrave.";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_POWER"] = "Kthen numrin e parë të ngritur në fuqinë e numrit të dytë.";
Blockly.Msg["MATH_ATAN2_HELPURL"] = "https://en.wikipedia.org/wiki/Atan2";
Blockly.Msg["MATH_ATAN2_TITLE"] = "atan2 of X:%1 Y:%2";
Blockly.Msg["MATH_ATAN2_TOOLTIP"] = "Ktheni arkangjentin e pikës (X, Y) në gradë nga -180 në 180.";
Blockly.Msg["MATH_CHANGE_HELPURL"] = "http://en.wikipedia.org/wiki/Programming_idiom#Incrementing_a_counter";
Blockly.Msg["MATH_CHANGE_TITLE"] = "ndrysho %1 nga %2";
Blockly.Msg["MATH_CHANGE_TOOLTIP"] = "Shto një numër në ndryshoren '%1'.";
Blockly.Msg["MATH_CONSTANT_HELPURL"] = "http://en.wikipedia.org/wiki/Mathematical_constant";
Blockly.Msg["MATH_CONSTANT_TOOLTIP"] = "Kthen një nga konstantet e përbashkëta: : π (3.141…), e (2.718…), φ (1.618…), sqrt(2) (1.414…), sqrt(½) (0.707…), or ∞ (infiniti).";
Blockly.Msg["MATH_CONSTRAIN_HELPURL"] = "https://en.wikipedia.org/wiki/Clamping_(graphics)";  // untranslated
Blockly.Msg["MATH_CONSTRAIN_TITLE"] = "detyro %1 e ulët %2 e lartë %3";
Blockly.Msg["MATH_CONSTRAIN_TOOLTIP"] = "Vëni një numër që të jetë në mes të kufive të specifikuara(përfshirëse).";
Blockly.Msg["MATH_DIVISION_SYMBOL"] = "÷";
Blockly.Msg["MATH_IS_DIVISIBLE_BY"] = "është i pjestueshme me";
Blockly.Msg["MATH_IS_EVEN"] = "është çift";
Blockly.Msg["MATH_IS_NEGATIVE"] = "është negativ";
Blockly.Msg["MATH_IS_ODD"] = "është tek";
Blockly.Msg["MATH_IS_POSITIVE"] = "është pozitiv";
Blockly.Msg["MATH_IS_PRIME"] = "është prim";
Blockly.Msg["MATH_IS_TOOLTIP"] = "Kontrollo nëse një numër është çift, tek, prim, i plotë, pozitiv, negativ, ose nëse është i pjestueshëm me një numër të caktuar. Kthehet e saktë ose e pasaktë.";
Blockly.Msg["MATH_IS_WHOLE"] = "është i plotë";
Blockly.Msg["MATH_MODULO_HELPURL"] = "http://en.wikipedia.org/wiki/Modulo_operation";
Blockly.Msg["MATH_MODULO_TITLE"] = "mbetësi i %1 ÷ %2";
Blockly.Msg["MATH_MODULO_TOOLTIP"] = "Kthen mbetësin nga pjestimi i dy numrave.";
Blockly.Msg["MATH_MULTIPLICATION_SYMBOL"] = "x";
Blockly.Msg["MATH_NUMBER_HELPURL"] = "http://en.wikipedia.org/wiki/Number";
Blockly.Msg["MATH_NUMBER_TOOLTIP"] = "Një numër.";
Blockly.Msg["MATH_ONLIST_HELPURL"] = "";  // untranslated
Blockly.Msg["MATH_ONLIST_OPERATOR_AVERAGE"] = "mesatarja e listës";
Blockly.Msg["MATH_ONLIST_OPERATOR_MAX"] = "numri më i madh i listës";
Blockly.Msg["MATH_ONLIST_OPERATOR_MEDIAN"] = "mediana e listës";
Blockly.Msg["MATH_ONLIST_OPERATOR_MIN"] = "numri më i ulët i listës";
Blockly.Msg["MATH_ONLIST_OPERATOR_MODE"] = "modat e listës";
Blockly.Msg["MATH_ONLIST_OPERATOR_RANDOM"] = "send i rastësishëm i listës";
Blockly.Msg["MATH_ONLIST_OPERATOR_STD_DEV"] = "devijimi standard i listës";
Blockly.Msg["MATH_ONLIST_OPERATOR_SUM"] = "mbledhja e listës";
Blockly.Msg["MATH_ONLIST_TOOLTIP_AVERAGE"] = "Kthen mesatarën (kuptimi aritmetik) i vlerave numerike të listës.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_MAX"] = "Kthe numrin më të madh të listës.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_MEDIAN"] = "Kthe numrin median të listës.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_MIN"] = "Kthe numrin me të vogël të listës.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_MODE"] = "Kthe listën e sendit(eve) më të zakonshme të listës.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_RANDOM"] = "Kthe një element të rastësishëm nga lista.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_STD_DEV"] = "Kthe devijimin standard të listës.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_SUM"] = "Kthe shumën e të gjithë numrave të listës.";
Blockly.Msg["MATH_POWER_SYMBOL"] = "^";
Blockly.Msg["MATH_RANDOM_FLOAT_HELPURL"] = "http://en.wikipedia.org/wiki/Random_number_generation";
Blockly.Msg["MATH_RANDOM_FLOAT_TITLE_RANDOM"] = "fraksioni i rastësishëm";
Blockly.Msg["MATH_RANDOM_FLOAT_TOOLTIP"] = "Kthe fraksionin e rastësishëm në mes të 0.0 (përfshirëse) dhe 1.0 (jopërfshirëse).";
Blockly.Msg["MATH_RANDOM_INT_HELPURL"] = "http://en.wikipedia.org/wiki/Random_number_generation";
Blockly.Msg["MATH_RANDOM_INT_TITLE"] = "numër i plotë i rastësishëm nga %1 deri në %2";
Blockly.Msg["MATH_RANDOM_INT_TOOLTIP"] = "Kthe një numër të plotë të rastësishëm të dy kufijve të specifikuar, të përfshirë.";
Blockly.Msg["MATH_ROUND_HELPURL"] = "http://en.wikipedia.org/wiki/Rounding";
Blockly.Msg["MATH_ROUND_OPERATOR_ROUND"] = "rrumbullakësimi";
Blockly.Msg["MATH_ROUND_OPERATOR_ROUNDDOWN"] = "rrumbullakësimi i ulët";
Blockly.Msg["MATH_ROUND_OPERATOR_ROUNDUP"] = "rrumbullakësimi i lartë";
Blockly.Msg["MATH_ROUND_TOOLTIP"] = "Rrumbullakësimi i numrit të lartë ose të ulët.";
Blockly.Msg["MATH_SINGLE_HELPURL"] = "http://en.wikipedia.org/wiki/Square_root";
Blockly.Msg["MATH_SINGLE_OP_ABSOLUTE"] = "absolut";
Blockly.Msg["MATH_SINGLE_OP_ROOT"] = "rrënja katrore";
Blockly.Msg["MATH_SINGLE_TOOLTIP_ABS"] = "Kthen vlerën absolute të një numri.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_EXP"] = "Kthen e në fuqinë e një numri.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_LN"] = "Kthen logaritmën natyrale të një numri.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_LOG10"] = "Kthen 10 logaritmet bazë të një numri.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_NEG"] = "Kthe negacionin e një numri.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_POW10"] = "Kthen 10 në fuqinë e një numri.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_ROOT"] = "Kthen rrënjën katrore të një numri.";
Blockly.Msg["MATH_SUBTRACTION_SYMBOL"] = "-";
Blockly.Msg["MATH_TRIG_ACOS"] = "acosinus";
Blockly.Msg["MATH_TRIG_ASIN"] = "asinus";
Blockly.Msg["MATH_TRIG_ATAN"] = "atangjentë";
Blockly.Msg["MATH_TRIG_COS"] = "cos";
Blockly.Msg["MATH_TRIG_HELPURL"] = "http://en.wikipedia.org/wiki/Trigonometric_functions";
Blockly.Msg["MATH_TRIG_SIN"] = "sin";
Blockly.Msg["MATH_TRIG_TAN"] = "tan";
Blockly.Msg["MATH_TRIG_TOOLTIP_ACOS"] = "Rikthe cos-1 e nje numeri.";
Blockly.Msg["MATH_TRIG_TOOLTIP_ASIN"] = "Rikthe sin-1 e nje numeri.";
Blockly.Msg["MATH_TRIG_TOOLTIP_ATAN"] = "Kthe tg-1 e nje numeri.";
Blockly.Msg["MATH_TRIG_TOOLTIP_COS"] = "Kthe kosinusin e nje grade (jo ne radiant).";
Blockly.Msg["MATH_TRIG_TOOLTIP_SIN"] = "Kthe kosinusin e nje kendi (jo ne radiant).";
Blockly.Msg["MATH_TRIG_TOOLTIP_TAN"] = "Kthe tangentin e nje kendi (jo radiant).";
Blockly.Msg["NEW_COLOUR_VARIABLE"] = "Krijo variabël ngjyrë...";
Blockly.Msg["NEW_NUMBER_VARIABLE"] = "Krijo variabël numër";
Blockly.Msg["NEW_STRING_VARIABLE"] = "Krijo variabël varg";
Blockly.Msg["NEW_VARIABLE"] = "Krijo variabël...";
Blockly.Msg["NEW_VARIABLE_TITLE"] = "Emri i identifikatorit të ri:";
Blockly.Msg["NEW_VARIABLE_TYPE_TITLE"] = "Tip i ri i variablës:";
Blockly.Msg["ORDINAL_NUMBER_SUFFIX"] = "";  // untranslated
Blockly.Msg["PROCEDURES_ALLOW_STATEMENTS"] = "lejo deklaratat";
Blockly.Msg["PROCEDURES_BEFORE_PARAMS"] = "me:";
Blockly.Msg["PROCEDURES_CALLNORETURN_HELPURL"] = "https://en.wikipedia.org/wiki/Subroutine";
Blockly.Msg["PROCEDURES_CALLNORETURN_TOOLTIP"] = "Lësho funksionin e definuar nga përdoruesi '%1'.";
Blockly.Msg["PROCEDURES_CALLRETURN_HELPURL"] = "https://en.wikipedia.org/wiki/Subroutine";
Blockly.Msg["PROCEDURES_CALLRETURN_TOOLTIP"] = "Lëho funksionin e definuar nga përdoruesi '%1' dhe përdor daljen e tij.";
Blockly.Msg["PROCEDURES_CALL_BEFORE_PARAMS"] = "me:";
Blockly.Msg["PROCEDURES_CREATE_DO"] = "Krijo '%1'";
Blockly.Msg["PROCEDURES_DEFNORETURN_COMMENT"] = "Përshkruaj këtë funksion...";
Blockly.Msg["PROCEDURES_DEFNORETURN_DO"] = "";  // untranslated
Blockly.Msg["PROCEDURES_DEFNORETURN_HELPURL"] = "http://en.wikipedia.org/wiki/Procedure_%28computer_science%29";
Blockly.Msg["PROCEDURES_DEFNORETURN_PROCEDURE"] = "bëj diqka";
Blockly.Msg["PROCEDURES_DEFNORETURN_TITLE"] = "te";
Blockly.Msg["PROCEDURES_DEFNORETURN_TOOLTIP"] = "Krijon një funksion pa dalje.";
Blockly.Msg["PROCEDURES_DEFRETURN_HELPURL"] = "http://en.wikipedia.org/wiki/Procedure_%28computer_science%29";
Blockly.Msg["PROCEDURES_DEFRETURN_RETURN"] = "rikthe";
Blockly.Msg["PROCEDURES_DEFRETURN_TOOLTIP"] = "Krijon një funksion me një dalje.";
Blockly.Msg["PROCEDURES_DEF_DUPLICATE_WARNING"] = "Paralajmërim: Ky funksion ka parametra të dyfishuar.";
Blockly.Msg["PROCEDURES_HIGHLIGHT_DEF"] = "Thekso definicionin e funksionit";
Blockly.Msg["PROCEDURES_IFRETURN_HELPURL"] = "http://c2.com/cgi/wiki?GuardClause";  // untranslated
Blockly.Msg["PROCEDURES_IFRETURN_TOOLTIP"] = "Nëse një vlerë është e saktë, atëherë kthe një vlerë të dytë.";
Blockly.Msg["PROCEDURES_IFRETURN_WARNING"] = "Paralajmërim: Ky bllok mund të përdoret vetëm brenda definicionit të funksionit.";
Blockly.Msg["PROCEDURES_MUTATORARG_TITLE"] = "Fut emrin:";
Blockly.Msg["PROCEDURES_MUTATORARG_TOOLTIP"] = "Shto një input në këtë funksion.";
Blockly.Msg["PROCEDURES_MUTATORCONTAINER_TITLE"] = "Informacioni i futur";
Blockly.Msg["PROCEDURES_MUTATORCONTAINER_TOOLTIP"] = "Shto, hiq, ose rirendit inputet e këtij funksioni.";
Blockly.Msg["REDO"] = "Ribëj";
Blockly.Msg["REMOVE_COMMENT"] = "Fshij komentin";
Blockly.Msg["RENAME_VARIABLE"] = "Ndrysho emrin variables...";
Blockly.Msg["RENAME_VARIABLE_TITLE"] = "Ndrysho emrin e te gjitha '%1' variablave ne :";
Blockly.Msg["TEXT_APPEND_HELPURL"] = "https://github.com/google/blockly/wiki/Text#text-modification";  // untranslated
Blockly.Msg["TEXT_APPEND_TITLE"] = "ne %1 shto tekst %2";
Blockly.Msg["TEXT_APPEND_TOOLTIP"] = "shto tekst tek varibla '%1'.";
Blockly.Msg["TEXT_CHANGECASE_HELPURL"] = "https://github.com/google/blockly/wiki/Text#adjusting-text-case";  // untranslated
Blockly.Msg["TEXT_CHANGECASE_OPERATOR_LOWERCASE"] = "me shkronja te vogla shtypi";
Blockly.Msg["TEXT_CHANGECASE_OPERATOR_TITLECASE"] = "Fillimi me shkronje te madhe shtypi";
Blockly.Msg["TEXT_CHANGECASE_OPERATOR_UPPERCASE"] = "me shkronja te medha shtypi";
Blockly.Msg["TEXT_CHANGECASE_TOOLTIP"] = "Kthe nje kopje te tekstit ne nje rast te ndryshem.";
Blockly.Msg["TEXT_CHARAT_FIRST"] = "merr shkronjen e pare";
Blockly.Msg["TEXT_CHARAT_FROM_END"] = "merr shkronjen # nga fundi";
Blockly.Msg["TEXT_CHARAT_FROM_START"] = "merr shkronjen #";
Blockly.Msg["TEXT_CHARAT_HELPURL"] = "https://github.com/google/blockly/wiki/Text#extracting-text";  // untranslated
Blockly.Msg["TEXT_CHARAT_LAST"] = "merr shkronjen e fundit";
Blockly.Msg["TEXT_CHARAT_RANDOM"] = "merr nje shkronje te rastesishme";
Blockly.Msg["TEXT_CHARAT_TAIL"] = "";  // untranslated
Blockly.Msg["TEXT_CHARAT_TITLE"] = "në tekst %1 %2";
Blockly.Msg["TEXT_CHARAT_TOOLTIP"] = "Kthe nje shkronje nga nje pozicion i caktuar.";
Blockly.Msg["TEXT_COUNT_HELPURL"] = "https://github.com/google/blockly/wiki/Text#counting-substrings";
Blockly.Msg["TEXT_COUNT_MESSAGE0"] = "numro %1 në %2";
Blockly.Msg["TEXT_COUNT_TOOLTIP"] = "Numrin sa herë paraqitet një tekst brenda një teksti tjetër.";
Blockly.Msg["TEXT_CREATE_JOIN_ITEM_TOOLTIP"] = "Shto nje gje ne tekst";
Blockly.Msg["TEXT_CREATE_JOIN_TITLE_JOIN"] = "bashkangjit";
Blockly.Msg["TEXT_CREATE_JOIN_TOOLTIP"] = "Shto, fshij, ose rirregullo sektoret për ta rikonfiguruar këtë bllok teksti.";
Blockly.Msg["TEXT_GET_SUBSTRING_END_FROM_END"] = "ne shkronjen # nga fundi";
Blockly.Msg["TEXT_GET_SUBSTRING_END_FROM_START"] = "ne shkronjen #";
Blockly.Msg["TEXT_GET_SUBSTRING_END_LAST"] = "tek shkronja e fundit";
Blockly.Msg["TEXT_GET_SUBSTRING_HELPURL"] = "https://github.com/google/blockly/wiki/Text#extracting-a-region-of-text";  // untranslated
Blockly.Msg["TEXT_GET_SUBSTRING_INPUT_IN_TEXT"] = "ne tekst";
Blockly.Msg["TEXT_GET_SUBSTRING_START_FIRST"] = "merr vlerat qe vazhdojne me shkronjen e pare";
Blockly.Msg["TEXT_GET_SUBSTRING_START_FROM_END"] = "merr nenvargun nga shkronja # nga fundi";
Blockly.Msg["TEXT_GET_SUBSTRING_START_FROM_START"] = "Merr nenvargun nga shkronja #";
Blockly.Msg["TEXT_GET_SUBSTRING_TAIL"] = "";  // untranslated
Blockly.Msg["TEXT_GET_SUBSTRING_TOOLTIP"] = "Pergjigjet me nje pjese te caktuar teksti.";
Blockly.Msg["TEXT_INDEXOF_HELPURL"] = "https://github.com/google/blockly/wiki/Text#finding-text";  // untranslated
Blockly.Msg["TEXT_INDEXOF_OPERATOR_FIRST"] = "gjej rastisjen e pare te tekstit";
Blockly.Msg["TEXT_INDEXOF_OPERATOR_LAST"] = "gjej rastisjen e fundit te tekstit";
Blockly.Msg["TEXT_INDEXOF_TITLE"] = "ne tekst %1 %2 %3";
Blockly.Msg["TEXT_INDEXOF_TOOLTIP"] = "Pergjigjet me indeksin e pare/fundit te rastisjes se tekstit te pare ne tekstin e dyte. Pergjigjet me %1 ne qofte se teksti nuk u gjet.";
Blockly.Msg["TEXT_ISEMPTY_HELPURL"] = "https://github.com/google/blockly/wiki/Text#checking-for-empty-text";  // untranslated
Blockly.Msg["TEXT_ISEMPTY_TITLE"] = "%1 eshte bosh";
Blockly.Msg["TEXT_ISEMPTY_TOOLTIP"] = "Kthehet e vertete neqoftese teksti i dhene eshte bosh.";
Blockly.Msg["TEXT_JOIN_HELPURL"] = "https://github.com/google/blockly/wiki/Text#text-creation";  // untranslated
Blockly.Msg["TEXT_JOIN_TITLE_CREATEWITH"] = "krijo tekst me";
Blockly.Msg["TEXT_JOIN_TOOLTIP"] = "Krijo nje pjese te tekstit duke bashkuar se bashku disa sende";
Blockly.Msg["TEXT_LENGTH_HELPURL"] = "https://github.com/google/blockly/wiki/Text#text-modification";  // untranslated
Blockly.Msg["TEXT_LENGTH_TITLE"] = "gjatesi %1";
Blockly.Msg["TEXT_LENGTH_TOOLTIP"] = "Pergjigjet me nje numer shkronjash (duke perfshire hapesire) ne tekstin e dhene.";
Blockly.Msg["TEXT_PRINT_HELPURL"] = "https://github.com/google/blockly/wiki/Text#printing-text";  // untranslated
Blockly.Msg["TEXT_PRINT_TITLE"] = "printo %1";
Blockly.Msg["TEXT_PRINT_TOOLTIP"] = "Printo tekstin e caktuar, numer ose vlere tjeter.";
Blockly.Msg["TEXT_PROMPT_HELPURL"] = "https://github.com/google/blockly/wiki/Text#getting-input-from-the-user";  // untranslated
Blockly.Msg["TEXT_PROMPT_TOOLTIP_NUMBER"] = "Kerkoji perdoruesit nje numer.";
Blockly.Msg["TEXT_PROMPT_TOOLTIP_TEXT"] = "Kerkoji perdoruesit ca tekst.";
Blockly.Msg["TEXT_PROMPT_TYPE_NUMBER"] = "kerko nje numer me njoftim";
Blockly.Msg["TEXT_PROMPT_TYPE_TEXT"] = "kerko tekst me njoftim";
Blockly.Msg["TEXT_REPLACE_HELPURL"] = "https://github.com/google/blockly/wiki/Text#replacing-substrings";  // untranslated
Blockly.Msg["TEXT_REPLACE_MESSAGE0"] = "zëvendëso %1 me %2 në %3";
Blockly.Msg["TEXT_REPLACE_TOOLTIP"] = "Zëvendëso të gjitha paraqitjet e një teksti brenda një teksti tjetër.";
Blockly.Msg["TEXT_REVERSE_HELPURL"] = "https://github.com/google/blockly/wiki/Text#reversing-text";  // untranslated
Blockly.Msg["TEXT_REVERSE_MESSAGE0"] = "kthe %1";
Blockly.Msg["TEXT_REVERSE_TOOLTIP"] = "Kthen renditjen e karaktereve në tekst.";
Blockly.Msg["TEXT_TEXT_HELPURL"] = "http://en.wikipedia.org/wiki/String_(computer_science)";
Blockly.Msg["TEXT_TEXT_TOOLTIP"] = "Nje shkronje, fjale, ose rresht teksti.";
Blockly.Msg["TEXT_TRIM_HELPURL"] = "https://github.com/google/blockly/wiki/Text#trimming-removing-spaces";  // untranslated
Blockly.Msg["TEXT_TRIM_OPERATOR_BOTH"] = "prit hapesirat nga te dyja anet";
Blockly.Msg["TEXT_TRIM_OPERATOR_LEFT"] = "prit hapesirat nga ana e majte";
Blockly.Msg["TEXT_TRIM_OPERATOR_RIGHT"] = "prit hapesirat nga ana e djathte";
Blockly.Msg["TEXT_TRIM_TOOLTIP"] = "Pergjigju me nje kopje te tekstit me hapesira te fshira nga njera ane ose te dyja anet.";
Blockly.Msg["TODAY"] = "Sot";
Blockly.Msg["UNDO"] = "Zhbëj";
Blockly.Msg["UNNAMED_KEY"] = "pa emër";
Blockly.Msg["VARIABLES_DEFAULT_NAME"] = "send";
Blockly.Msg["VARIABLES_GET_CREATE_SET"] = "Krijo 'vendos %1";
Blockly.Msg["VARIABLES_GET_HELPURL"] = "https://github.com/google/blockly/wiki/Variables#get";  // untranslated
Blockly.Msg["VARIABLES_GET_TOOLTIP"] = "Pergjigjet me nje vlere te kesaj variable.";
Blockly.Msg["VARIABLES_SET"] = "vendos %1 ne %2";
Blockly.Msg["VARIABLES_SET_CREATE_GET"] = "Krijo 'merr %1";
Blockly.Msg["VARIABLES_SET_HELPURL"] = "https://github.com/google/blockly/wiki/Variables#set";  // untranslated
Blockly.Msg["VARIABLES_SET_TOOLTIP"] = "Vendos kete variable te jete e barabarte me te dhenat ne hyrje.";
Blockly.Msg["VARIABLE_ALREADY_EXISTS"] = "Një variabël e quajtur '%1' tashmë ekziston.";
Blockly.Msg["VARIABLE_ALREADY_EXISTS_FOR_ANOTHER_TYPE"] = "Një variabël me emrin '%1' veç ekziston për një tip tjetër: '%2'.";
Blockly.Msg["WORKSPACE_ARIA_LABEL"] = "Hapësira e punës e Blockly";
Blockly.Msg["WORKSPACE_COMMENT_DEFAULT_TEXT"] = "Thuaj dicka...";
Blockly.Msg["CONTROLS_FOREACH_INPUT_DO"] = Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"];
Blockly.Msg["CONTROLS_FOR_INPUT_DO"] = Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"];
Blockly.Msg["CONTROLS_IF_ELSEIF_TITLE_ELSEIF"] = Blockly.Msg["CONTROLS_IF_MSG_ELSEIF"];
Blockly.Msg["CONTROLS_IF_ELSE_TITLE_ELSE"] = Blockly.Msg["CONTROLS_IF_MSG_ELSE"];
Blockly.Msg["CONTROLS_IF_IF_TITLE_IF"] = Blockly.Msg["CONTROLS_IF_MSG_IF"];
Blockly.Msg["CONTROLS_IF_MSG_THEN"] = Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"];
Blockly.Msg["CONTROLS_WHILEUNTIL_INPUT_DO"] = Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"];
Blockly.Msg["LISTS_CREATE_WITH_ITEM_TITLE"] = Blockly.Msg["VARIABLES_DEFAULT_NAME"];
Blockly.Msg["LISTS_GET_INDEX_HELPURL"] = Blockly.Msg["LISTS_INDEX_OF_HELPURL"];
Blockly.Msg["LISTS_GET_INDEX_INPUT_IN_LIST"] = Blockly.Msg["LISTS_INLIST"];
Blockly.Msg["LISTS_GET_SUBLIST_INPUT_IN_LIST"] = Blockly.Msg["LISTS_INLIST"];
Blockly.Msg["LISTS_INDEX_OF_INPUT_IN_LIST"] = Blockly.Msg["LISTS_INLIST"];
Blockly.Msg["LISTS_SET_INDEX_INPUT_IN_LIST"] = Blockly.Msg["LISTS_INLIST"];
Blockly.Msg["MATH_CHANGE_TITLE_ITEM"] = Blockly.Msg["VARIABLES_DEFAULT_NAME"];
Blockly.Msg["PROCEDURES_DEFRETURN_COMMENT"] = Blockly.Msg["PROCEDURES_DEFNORETURN_COMMENT"];
Blockly.Msg["PROCEDURES_DEFRETURN_DO"] = Blockly.Msg["PROCEDURES_DEFNORETURN_DO"];
Blockly.Msg["PROCEDURES_DEFRETURN_PROCEDURE"] = Blockly.Msg["PROCEDURES_DEFNORETURN_PROCEDURE"];
Blockly.Msg["PROCEDURES_DEFRETURN_TITLE"] = Blockly.Msg["PROCEDURES_DEFNORETURN_TITLE"];
Blockly.Msg["TEXT_APPEND_VARIABLE"] = Blockly.Msg["VARIABLES_DEFAULT_NAME"];
Blockly.Msg["TEXT_CREATE_JOIN_ITEM_TITLE_ITEM"] = Blockly.Msg["VARIABLES_DEFAULT_NAME"];

Blockly.Msg["MATH_HUE"] = "230";
Blockly.Msg["LOOPS_HUE"] = "120";
Blockly.Msg["LISTS_HUE"] = "260";
Blockly.Msg["LOGIC_HUE"] = "210";
Blockly.Msg["VARIABLES_HUE"] = "330";
Blockly.Msg["TEXTS_HUE"] = "160";
Blockly.Msg["PROCEDURES_HUE"] = "290";
Blockly.Msg["COLOUR_HUE"] = "20";
Blockly.Msg["VARIABLES_DYNAMIC_HUE"] = "310";