// This file was automatically generated.  Do not modify.

'use strict';

Blockly.Msg["ADD_COMMENT"] = "Dodaj komentar";
Blockly.Msg["CANNOT_DELETE_VARIABLE_PROCEDURE"] = "Ne mogu da obrišem varijablu ’%1’ jer je deo definicije funkcije ’%2’";
Blockly.Msg["CHANGE_VALUE_TITLE"] = "Promeni vrednost:";
Blockly.Msg["CLEAN_UP"] = "Ukloni blokove";
Blockly.Msg["COLLAPSED_WARNINGS_WARNING"] = "Collapsed blocks contain warnings.";  // untranslated
Blockly.Msg["COLLAPSE_ALL"] = "Skupi blokove";
Blockly.Msg["COLLAPSE_BLOCK"] = "Skupi blok";
Blockly.Msg["COLOUR_BLEND_COLOUR1"] = "boja 1";
Blockly.Msg["COLOUR_BLEND_COLOUR2"] = "boja 2";
Blockly.Msg["COLOUR_BLEND_HELPURL"] = "http://meyerweb.com/eric/tools/color-blend/";
Blockly.Msg["COLOUR_BLEND_RATIO"] = "odnos";
Blockly.Msg["COLOUR_BLEND_TITLE"] = "pomešaj";
Blockly.Msg["COLOUR_BLEND_TOOLTIP"] = "Pomešati dve boje zajedno sa datim odnosom (0.0 - 1.0).";
Blockly.Msg["COLOUR_PICKER_HELPURL"] = "https://sr.wikipedia.org/wiki/Boja";
Blockly.Msg["COLOUR_PICKER_TOOLTIP"] = "Izaberite boju sa palete.";
Blockly.Msg["COLOUR_RANDOM_HELPURL"] = "http://randomcolour.com";  // untranslated
Blockly.Msg["COLOUR_RANDOM_TITLE"] = "slučajna boja";
Blockly.Msg["COLOUR_RANDOM_TOOLTIP"] = "Izaberite boju nasumice.";
Blockly.Msg["COLOUR_RGB_BLUE"] = "plava";
Blockly.Msg["COLOUR_RGB_GREEN"] = "zelena";
Blockly.Msg["COLOUR_RGB_HELPURL"] = "http://www.december.com/html/spec/colorper.html";
Blockly.Msg["COLOUR_RGB_RED"] = "crvena";
Blockly.Msg["COLOUR_RGB_TITLE"] = "boja sa";
Blockly.Msg["COLOUR_RGB_TOOLTIP"] = "Kreiraj boju sa određenom količinom crvene,zelene, i plave. Sve vrednosti moraju biti između 0 i 100.";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_HELPURL"] = "https://github.com/google/blockly/wiki/Loops#loop-termination-blocks";  // untranslated
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_OPERATOR_BREAK"] = "Izađite iz petlje";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_OPERATOR_CONTINUE"] = "nastavi sa sledećom iteracijom petlje";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_TOOLTIP_BREAK"] = "Napusti sadržaj petlje.";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_TOOLTIP_CONTINUE"] = "Preskoči ostatak ove petlje, i nastavi sa sledećom iteracijom(ponavljanjem).";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_WARNING"] = "Upozorenje: Ovaj blok može da se upotrebi samo unutar petlje.";
Blockly.Msg["CONTROLS_FOREACH_HELPURL"] = "https://github.com/google/blockly/wiki/Loops#for-each";  // untranslated
Blockly.Msg["CONTROLS_FOREACH_TITLE"] = "za svaku stavku %1 na spisku %2";
Blockly.Msg["CONTROLS_FOREACH_TOOLTIP"] = "Za svaku stavku unutar liste, podesi promenjivu '%1' po stavci, i onda načini neke izjave-naredbe.";
Blockly.Msg["CONTROLS_FOR_HELPURL"] = "https://github.com/google/blockly/wiki/Loops#count-with";  // untranslated
Blockly.Msg["CONTROLS_FOR_TITLE"] = "prebroj sa %1 od %2 do %3 od %4";
Blockly.Msg["CONTROLS_FOR_TOOLTIP"] = "Imaj promenjivu \"%1\" uzmi vrednosti od početnog broja do zadnjeg broja, brojeći po određenom intervalu, i izvrši određene blokove.";
Blockly.Msg["CONTROLS_IF_ELSEIF_TOOLTIP"] = "Dodajte uslov bloku „ako“.";
Blockly.Msg["CONTROLS_IF_ELSE_TOOLTIP"] = "Dodaj konačni, catch-all  (uhvati sve) uslove if bloka.";
Blockly.Msg["CONTROLS_IF_HELPURL"] = "https://github.com/google/blockly/wiki/IfElse";  // untranslated
Blockly.Msg["CONTROLS_IF_IF_TOOLTIP"] = "Dodaj, ukloni, ili preuredi delove kako bih rekonfigurisali ovaj if blok.";
Blockly.Msg["CONTROLS_IF_MSG_ELSE"] = "inače";
Blockly.Msg["CONTROLS_IF_MSG_ELSEIF"] = "inače-ako";
Blockly.Msg["CONTROLS_IF_MSG_IF"] = "ako";
Blockly.Msg["CONTROLS_IF_TOOLTIP_1"] = "ako je vrednost tačna, onda izvrši neke naredbe-izjave.";
Blockly.Msg["CONTROLS_IF_TOOLTIP_2"] = "ako je vrednost tačna, onda izvrši prvi blok naredbi, U suprotnom, izvrši drugi blok naredbi.";
Blockly.Msg["CONTROLS_IF_TOOLTIP_3"] = "Ako je prva vrednost tačna, onda izvrši prvi blok naredbi, u suprotnom, ako je druga vrednost tačna , izvrši drugi blok naredbi.";
Blockly.Msg["CONTROLS_IF_TOOLTIP_4"] = "Ako je prva vrednost tačna, onda izvrši prvi blok naredbi, u suprotnom, ako je druga vrednost tačna , izvrši drugi blok naredbi. Ako ni jedna od vrednosti nije tačna, izvrši poslednji blok naredbi.";
Blockly.Msg["CONTROLS_REPEAT_HELPURL"] = "https://sr.wikipedia.org/wiki/For_petlja";
Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"] = "izvrši";
Blockly.Msg["CONTROLS_REPEAT_TITLE"] = "ponovi %1 puta";
Blockly.Msg["CONTROLS_REPEAT_TOOLTIP"] = "Odraditi neke naredbe nekoliko puta.";
Blockly.Msg["CONTROLS_WHILEUNTIL_HELPURL"] = "https://github.com/google/blockly/wiki/Loops#repeat";  // untranslated
Blockly.Msg["CONTROLS_WHILEUNTIL_OPERATOR_UNTIL"] = "ponavljati do";
Blockly.Msg["CONTROLS_WHILEUNTIL_OPERATOR_WHILE"] = "ponavljati dok";
Blockly.Msg["CONTROLS_WHILEUNTIL_TOOLTIP_UNTIL"] = "Dok vrednost nije tačna, onda izvršiti neke naredbe.";
Blockly.Msg["CONTROLS_WHILEUNTIL_TOOLTIP_WHILE"] = "Dok je vrednost tačna, onda izvršite neke naredbe.";
Blockly.Msg["DELETE_ALL_BLOCKS"] = "Da obrišem svih %1 blokova?";
Blockly.Msg["DELETE_BLOCK"] = "Obriši blok";
Blockly.Msg["DELETE_VARIABLE"] = "Obriši promenljivu '%1'";
Blockly.Msg["DELETE_VARIABLE_CONFIRMATION"] = "Da obrišem %1 upotreba promenljive '%2'?";
Blockly.Msg["DELETE_X_BLOCKS"] = "Obriši %1 blokova";
Blockly.Msg["DIALOG_CANCEL"] = "Otkaži";
Blockly.Msg["DIALOG_OK"] = "U redu";
Blockly.Msg["DISABLE_BLOCK"] = "Onemogući blok";
Blockly.Msg["DUPLICATE_BLOCK"] = "Dupliraj";
Blockly.Msg["DUPLICATE_COMMENT"] = "Duplicate Comment";  // untranslated
Blockly.Msg["ENABLE_BLOCK"] = "Omogući blok";
Blockly.Msg["EXPAND_ALL"] = "Proširi blokove";
Blockly.Msg["EXPAND_BLOCK"] = "Proširi blok";
Blockly.Msg["EXTERNAL_INPUTS"] = "Spoljni ulazi";
Blockly.Msg["HELP"] = "Pomoć";
Blockly.Msg["INLINE_INPUTS"] = "Unutrašnji ulazi";
Blockly.Msg["LISTS_CREATE_EMPTY_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#create-empty-list";
Blockly.Msg["LISTS_CREATE_EMPTY_TITLE"] = "napravi prazan spisak";
Blockly.Msg["LISTS_CREATE_EMPTY_TOOLTIP"] = "vraća listu, dužine 0, ne sadržavajući  evidenciju podataka";
Blockly.Msg["LISTS_CREATE_WITH_CONTAINER_TITLE_ADD"] = "spisak";
Blockly.Msg["LISTS_CREATE_WITH_CONTAINER_TOOLTIP"] = "Dodajte, izbrišite, ili preuredite delove kako bi se reorganizovali ovaj blok liste.";
Blockly.Msg["LISTS_CREATE_WITH_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#create-list-with";  // untranslated
Blockly.Msg["LISTS_CREATE_WITH_INPUT_WITH"] = "napravi spisak sa";
Blockly.Msg["LISTS_CREATE_WITH_ITEM_TOOLTIP"] = "Dodajte stavku na spisak.";
Blockly.Msg["LISTS_CREATE_WITH_TOOLTIP"] = "Kreiraj listu sa bilo kojim brojem stavki.";
Blockly.Msg["LISTS_GET_INDEX_FIRST"] = "prva";
Blockly.Msg["LISTS_GET_INDEX_FROM_END"] = "# sa kraja";
Blockly.Msg["LISTS_GET_INDEX_FROM_START"] = "#";
Blockly.Msg["LISTS_GET_INDEX_GET"] = "preuzmi";
Blockly.Msg["LISTS_GET_INDEX_GET_REMOVE"] = "preuzmi i ukloni";
Blockly.Msg["LISTS_GET_INDEX_LAST"] = "poslednja";
Blockly.Msg["LISTS_GET_INDEX_RANDOM"] = "slučajna";
Blockly.Msg["LISTS_GET_INDEX_REMOVE"] = "ukloni";
Blockly.Msg["LISTS_GET_INDEX_TAIL"] = "";  // untranslated
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_FIRST"] = "Vraća prvu stavku na spisku.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_FROM"] = "Vraća stavku na određenu poziciju na listi.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_LAST"] = "Vraća poslednju stavku na spisku.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_RANDOM"] = "Vraća slučajnu stavku sa spiska.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_FIRST"] = "Uklanja i vraća prvu stavku sa spiska.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_FROM"] = "Uklanja i vraća stavku sa određenog položaja na spisku.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_LAST"] = "Uklanja i vraća poslednju stavku sa spiska.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_RANDOM"] = "Uklanja i vraća slučajnu stavku sa spiska.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_REMOVE_FIRST"] = "Uklanja prvu stavku sa spiska.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_REMOVE_FROM"] = "Uklanja stavku sa određenog položaja na spisku.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_REMOVE_LAST"] = "Uklanja poslednju stavku sa spiska.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_REMOVE_RANDOM"] = "Uklanja slučajnu stavku sa spiska.";
Blockly.Msg["LISTS_GET_SUBLIST_END_FROM_END"] = "do # od kraja";
Blockly.Msg["LISTS_GET_SUBLIST_END_FROM_START"] = "do #";
Blockly.Msg["LISTS_GET_SUBLIST_END_LAST"] = "do poslednje";
Blockly.Msg["LISTS_GET_SUBLIST_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#getting-a-sublist";  // untranslated
Blockly.Msg["LISTS_GET_SUBLIST_START_FIRST"] = "preuzmi podspisak od prve";
Blockly.Msg["LISTS_GET_SUBLIST_START_FROM_END"] = "preuzmi podspisak iz # sa kraja";
Blockly.Msg["LISTS_GET_SUBLIST_START_FROM_START"] = "preuzmi podspisak od #";
Blockly.Msg["LISTS_GET_SUBLIST_TAIL"] = "";  // untranslated
Blockly.Msg["LISTS_GET_SUBLIST_TOOLTIP"] = "Pravi kopiju određenog dela liste.";
Blockly.Msg["LISTS_INDEX_FROM_END_TOOLTIP"] = "%1 je poslednja stavka.";
Blockly.Msg["LISTS_INDEX_FROM_START_TOOLTIP"] = "%1 je prva stavka.";
Blockly.Msg["LISTS_INDEX_OF_FIRST"] = "pronađi prvo pojavljivanje stavke";
Blockly.Msg["LISTS_INDEX_OF_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#getting-items-from-a-list";  // untranslated
Blockly.Msg["LISTS_INDEX_OF_LAST"] = "pronađi poslednje pojavljivanje stavke";
Blockly.Msg["LISTS_INDEX_OF_TOOLTIP"] = "Vraća broj prvog i/poslednjeg ulaska elementa u listu. Vraća %1 Ako element nije pronađen.";
Blockly.Msg["LISTS_INLIST"] = "na spisku";
Blockly.Msg["LISTS_ISEMPTY_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#is-empty";  // untranslated
Blockly.Msg["LISTS_ISEMPTY_TITLE"] = "%1 je prazan";
Blockly.Msg["LISTS_ISEMPTY_TOOLTIP"] = "Vraća vrednost tačno ako je lista prazna.";
Blockly.Msg["LISTS_LENGTH_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#length-of";  // untranslated
Blockly.Msg["LISTS_LENGTH_TITLE"] = "dužina spiska %1";
Blockly.Msg["LISTS_LENGTH_TOOLTIP"] = "Vraća dužinu spiska.";
Blockly.Msg["LISTS_REPEAT_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#create-list-with";  // untranslated
Blockly.Msg["LISTS_REPEAT_TITLE"] = "Napraviti listu sa stavkom %1 koja se ponavlja %2 puta";
Blockly.Msg["LISTS_REPEAT_TOOLTIP"] = "Pravi listu koja se sastoji od zadane vrednosti koju ponavljamo određeni broj šuta.";
Blockly.Msg["LISTS_REVERSE_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#reversing-a-list";
Blockly.Msg["LISTS_REVERSE_MESSAGE0"] = "obrnuto %1";
Blockly.Msg["LISTS_REVERSE_TOOLTIP"] = "Obrni kopiju spiska.";
Blockly.Msg["LISTS_SET_INDEX_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#in-list--set";  // untranslated
Blockly.Msg["LISTS_SET_INDEX_INPUT_TO"] = "kao";
Blockly.Msg["LISTS_SET_INDEX_INSERT"] = "ubaci na";
Blockly.Msg["LISTS_SET_INDEX_SET"] = "postavi";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_INSERT_FIRST"] = "Ubacuje stavku na početak spiska.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_INSERT_FROM"] = "Ubacuje stavku na određeni položaj na spisku.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_INSERT_LAST"] = "Dodajte stavku na kraj spiska.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_INSERT_RANDOM"] = "Ubacuje stavku na slučajno mesto na spisku.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_SET_FIRST"] = "Postavlja prvu stavku na spisku.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_SET_FROM"] = "Postavlja stavku na određeni položaj na spisku.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_SET_LAST"] = "Postavlja poslednju stavku na spisku.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_SET_RANDOM"] = "Postavlja slučajnu stavku na spisku.";
Blockly.Msg["LISTS_SORT_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#sorting-a-list";
Blockly.Msg["LISTS_SORT_ORDER_ASCENDING"] = "rastuće";
Blockly.Msg["LISTS_SORT_ORDER_DESCENDING"] = "opadajuće";
Blockly.Msg["LISTS_SORT_TITLE"] = "sortiraj %1 %2 %3";
Blockly.Msg["LISTS_SORT_TOOLTIP"] = "Sortirajte kopiju spiska.";
Blockly.Msg["LISTS_SORT_TYPE_IGNORECASE"] = "azbučno, ignoriši mala i velika slova";
Blockly.Msg["LISTS_SORT_TYPE_NUMERIC"] = "kao brojeve";
Blockly.Msg["LISTS_SORT_TYPE_TEXT"] = "azbučno";
Blockly.Msg["LISTS_SPLIT_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#splitting-strings-and-joining-lists";  // untranslated
Blockly.Msg["LISTS_SPLIT_LIST_FROM_TEXT"] = "napravite listu sa teksta";
Blockly.Msg["LISTS_SPLIT_TEXT_FROM_LIST"] = "da tekst iz liste";
Blockly.Msg["LISTS_SPLIT_TOOLTIP_JOIN"] = "Da se pridruži listu tekstova u jedan tekst, podeljenih za razdvajanje.";
Blockly.Msg["LISTS_SPLIT_TOOLTIP_SPLIT"] = "Podeliti tekst u listu tekstova, razbijanje na svakom graničnik.";
Blockly.Msg["LISTS_SPLIT_WITH_DELIMITER"] = "sa razdvajanje";
Blockly.Msg["LOGIC_BOOLEAN_FALSE"] = "netačno";
Blockly.Msg["LOGIC_BOOLEAN_HELPURL"] = "https://github.com/google/blockly/wiki/Logic#values";  // untranslated
Blockly.Msg["LOGIC_BOOLEAN_TOOLTIP"] = "Vraća ili tačno ili netačno.";
Blockly.Msg["LOGIC_BOOLEAN_TRUE"] = "tačno";
Blockly.Msg["LOGIC_COMPARE_HELPURL"] = "https://sr.wikipedia.org/wiki/Nejednakost";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_EQ"] = "Vraća vrednost „tačno“ ako su oba ulaza jednaka.";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_GT"] = "Vraća vrednost „tačno“ ako je prvi ulaz veći od drugog.";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_GTE"] = "Vraća vrednost „tačno“ ako je prvi ulaz veći ili jednak drugom.";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_LT"] = "Vraća vrednost „tačno“ ako je prvi ulaz manji od drugog.";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_LTE"] = "Vraća vrednost „tačno“ ako je prvi ulaz manji ili jednak drugom.";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_NEQ"] = "Vraća vrednost „tačno“ ako su oba ulaza nejednaka.";
Blockly.Msg["LOGIC_NEGATE_HELPURL"] = "https://github.com/google/blockly/wiki/Logic#not";  // untranslated
Blockly.Msg["LOGIC_NEGATE_TITLE"] = "nije %1";
Blockly.Msg["LOGIC_NEGATE_TOOLTIP"] = "Vraća vrednost „tačno“ ako je ulaz netačan. Vraća vrednost „netačno“ ako je ulaz tačan.";
Blockly.Msg["LOGIC_NULL"] = "bez vrednosti";
Blockly.Msg["LOGIC_NULL_HELPURL"] = "https://en.wikipedia.org/wiki/Nullable_type";
Blockly.Msg["LOGIC_NULL_TOOLTIP"] = "Vraća „bez vrednosti“.";
Blockly.Msg["LOGIC_OPERATION_AND"] = "i";
Blockly.Msg["LOGIC_OPERATION_HELPURL"] = "https://github.com/google/blockly/wiki/Logic#logical-operations";  // untranslated
Blockly.Msg["LOGIC_OPERATION_OR"] = "ili";
Blockly.Msg["LOGIC_OPERATION_TOOLTIP_AND"] = "Vraća vrednost „tačno“ ako su oba ulaza tačna.";
Blockly.Msg["LOGIC_OPERATION_TOOLTIP_OR"] = "Vraća vrednost „tačno“ ako je bar jedan od ulaza tačan.";
Blockly.Msg["LOGIC_TERNARY_CONDITION"] = "proba";
Blockly.Msg["LOGIC_TERNARY_HELPURL"] = "https://en.wikipedia.org/wiki/%3F:";
Blockly.Msg["LOGIC_TERNARY_IF_FALSE"] = "ako je netačno";
Blockly.Msg["LOGIC_TERNARY_IF_TRUE"] = "ako je tačno";
Blockly.Msg["LOGIC_TERNARY_TOOLTIP"] = "Proveri uslov u 'test'. Ako je uslov tačan, tada vraća 'if true' vrednost; u drugom slučaju vraća 'if false' vrednost.";
Blockly.Msg["MATH_ADDITION_SYMBOL"] = "+";
Blockly.Msg["MATH_ARITHMETIC_HELPURL"] = "https://en.wikipedia.org/wiki/Arithmetic";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_ADD"] = "Vratite zbir dva broja.";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_DIVIDE"] = "Vraća količnik dva broja.";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_MINUS"] = "Vraća razliku dva broja.";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_MULTIPLY"] = "Vraća proizvod dva broja.";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_POWER"] = "Vraća prvi broj stepenovan drugim.";
Blockly.Msg["MATH_ATAN2_HELPURL"] = "https://en.wikipedia.org/wiki/Atan2";  // untranslated
Blockly.Msg["MATH_ATAN2_TITLE"] = "atan2 of X:%1 Y:%2";  // untranslated
Blockly.Msg["MATH_ATAN2_TOOLTIP"] = "Return the arctangent of point (X, Y) in degrees from -180 to 180.";  // untranslated
Blockly.Msg["MATH_CHANGE_HELPURL"] = "https://en.wikipedia.org/wiki/Programming_idiom#Incrementing_a_counter";
Blockly.Msg["MATH_CHANGE_TITLE"] = "promeni %1 za %2";
Blockly.Msg["MATH_CHANGE_TOOLTIP"] = "Dodajte broj promenljivoj „%1“.";
Blockly.Msg["MATH_CONSTANT_HELPURL"] = "https://sr.wikipedia.org/wiki/Matematička_konstanta";
Blockly.Msg["MATH_CONSTANT_TOOLTIP"] = "vrati jednu od zajedničkih konstanti: π (3.141…), e (2.718…), φ (1.618…), sqrt(2) (1.414…), sqrt(½) (0.707…), ili ∞ (infinity).";
Blockly.Msg["MATH_CONSTRAIN_HELPURL"] = "https://en.wikipedia.org/wiki/Clamping_(graphics)";  // untranslated
Blockly.Msg["MATH_CONSTRAIN_TITLE"] = "ograniči %1 nisko %2 visoko %3";
Blockly.Msg["MATH_CONSTRAIN_TOOLTIP"] = "Ograničava broj na donje i gornje granice (uključivo).";
Blockly.Msg["MATH_DIVISION_SYMBOL"] = "÷";
Blockly.Msg["MATH_IS_DIVISIBLE_BY"] = "je deljiv sa";
Blockly.Msg["MATH_IS_EVEN"] = "je paran";
Blockly.Msg["MATH_IS_NEGATIVE"] = "je negativan";
Blockly.Msg["MATH_IS_ODD"] = "je neparan";
Blockly.Msg["MATH_IS_POSITIVE"] = "je pozitivan";
Blockly.Msg["MATH_IS_PRIME"] = "je prost";
Blockly.Msg["MATH_IS_TOOLTIP"] = "Provjerava da li je broj paran, neparan, prost, cio, pozitivan, negativan, ili da li je deljiv sa određenim brojem. Vraća tačno ili netačno.";
Blockly.Msg["MATH_IS_WHOLE"] = "je ceo";
Blockly.Msg["MATH_MODULO_HELPURL"] = "https://sr.wikipedia.org/wiki/Kongruencija";
Blockly.Msg["MATH_MODULO_TITLE"] = "podsetnik od %1 ÷ %2";
Blockly.Msg["MATH_MODULO_TOOLTIP"] = "Vraća podsetnik od deljenja dva broja.";
Blockly.Msg["MATH_MULTIPLICATION_SYMBOL"] = "×";
Blockly.Msg["MATH_NUMBER_HELPURL"] = "https://en.wikipedia.org/wiki/Number";
Blockly.Msg["MATH_NUMBER_TOOLTIP"] = "Neki broj.";
Blockly.Msg["MATH_ONLIST_HELPURL"] = "";  // untranslated
Blockly.Msg["MATH_ONLIST_OPERATOR_AVERAGE"] = "prosek spiska";
Blockly.Msg["MATH_ONLIST_OPERATOR_MAX"] = "maks. spiska";
Blockly.Msg["MATH_ONLIST_OPERATOR_MEDIAN"] = "medijana spiska";
Blockly.Msg["MATH_ONLIST_OPERATOR_MIN"] = "min. spiska";
Blockly.Msg["MATH_ONLIST_OPERATOR_MODE"] = "modus spiska";
Blockly.Msg["MATH_ONLIST_OPERATOR_RANDOM"] = "slučajna stavka spiska";
Blockly.Msg["MATH_ONLIST_OPERATOR_STD_DEV"] = "standardna devijacija spiska";
Blockly.Msg["MATH_ONLIST_OPERATOR_SUM"] = "zbir spiska";
Blockly.Msg["MATH_ONLIST_TOOLTIP_AVERAGE"] = "Vraća prosek numeričkih vrednosti sa spiska.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_MAX"] = "Vraća najveći broj sa spiska.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_MEDIAN"] = "Vraća medijanu sa spiska.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_MIN"] = "Vraća najmanji broj sa spiska.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_MODE"] = "Vraća najčešće stavke sa spiska.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_RANDOM"] = "Vraća slučajni element sa spiska.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_STD_DEV"] = "Vraća standardnu devijaciju spiska.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_SUM"] = "Vraća zbir svih brojeva sa spiska.";
Blockly.Msg["MATH_POWER_SYMBOL"] = "^";
Blockly.Msg["MATH_RANDOM_FLOAT_HELPURL"] = "https://sr.wikipedia.org/wiki/Generator_slučajnih_brojeva";
Blockly.Msg["MATH_RANDOM_FLOAT_TITLE_RANDOM"] = "slučajni razlomak";
Blockly.Msg["MATH_RANDOM_FLOAT_TOOLTIP"] = "Vraća slučajni razlomak između 0.0 (uključivo) i 1.0 (isključivo).";
Blockly.Msg["MATH_RANDOM_INT_HELPURL"] = "https://sr.wikipedia.org/wiki/Generator_slučajnih_brojeva";
Blockly.Msg["MATH_RANDOM_INT_TITLE"] = "sličajno odabrani cijeli broj od %1 do %2";
Blockly.Msg["MATH_RANDOM_INT_TOOLTIP"] = "Vraća slučajno odabrani celi broj između dve određene granice, uključivo.";
Blockly.Msg["MATH_ROUND_HELPURL"] = "https://sr.wikipedia.org/wiki/Zaokruživanje";
Blockly.Msg["MATH_ROUND_OPERATOR_ROUND"] = "zaokruži";
Blockly.Msg["MATH_ROUND_OPERATOR_ROUNDDOWN"] = "zaokruži naniže";
Blockly.Msg["MATH_ROUND_OPERATOR_ROUNDUP"] = "zaokruži naviše";
Blockly.Msg["MATH_ROUND_TOOLTIP"] = "Zaokružite broj na veću ili manju vrednost.";
Blockly.Msg["MATH_SINGLE_HELPURL"] = "https://sr.wikipedia.org/wiki/Kvadratni_koren";
Blockly.Msg["MATH_SINGLE_OP_ABSOLUTE"] = "apsolutan";
Blockly.Msg["MATH_SINGLE_OP_ROOT"] = "kvadratni koren";
Blockly.Msg["MATH_SINGLE_TOOLTIP_ABS"] = "Vraća apsolutnu vrednost broja.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_EXP"] = "vratiti e na vlasti broja.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_LN"] = "Vraća prirodni logaritam broja.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_LOG10"] = "Vraća logaritam broja sa osnovom 10.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_NEG"] = "Vraća negaciju broja.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_POW10"] = "Vraća 10-ti stepen broja.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_ROOT"] = "Vraća kvadratni koren broja.";
Blockly.Msg["MATH_SUBTRACTION_SYMBOL"] = "-";
Blockly.Msg["MATH_TRIG_ACOS"] = "arc cos";
Blockly.Msg["MATH_TRIG_ASIN"] = "arc sin";
Blockly.Msg["MATH_TRIG_ATAN"] = "arc tan";
Blockly.Msg["MATH_TRIG_COS"] = "cos";
Blockly.Msg["MATH_TRIG_HELPURL"] = "https://sr.wikipedia.org/wiki/Trigonometrijske_funkcije";
Blockly.Msg["MATH_TRIG_SIN"] = "sin";
Blockly.Msg["MATH_TRIG_TAN"] = "tan";
Blockly.Msg["MATH_TRIG_TOOLTIP_ACOS"] = "Vraća arkus kosinus broja.";
Blockly.Msg["MATH_TRIG_TOOLTIP_ASIN"] = "Vraća arkus broja.";
Blockly.Msg["MATH_TRIG_TOOLTIP_ATAN"] = "Vraća arkus tangens broja.";
Blockly.Msg["MATH_TRIG_TOOLTIP_COS"] = "Vraća kosinus stepena (ne radijan).";
Blockly.Msg["MATH_TRIG_TOOLTIP_SIN"] = "Vraća sinus stepena (ne radijan).";
Blockly.Msg["MATH_TRIG_TOOLTIP_TAN"] = "Vraća tangens stepena (ne radijan).";
Blockly.Msg["NEW_COLOUR_VARIABLE"] = "Create colour variable...";  // untranslated
Blockly.Msg["NEW_NUMBER_VARIABLE"] = "Create number variable...";  // untranslated
Blockly.Msg["NEW_STRING_VARIABLE"] = "Create string variable...";  // untranslated
Blockly.Msg["NEW_VARIABLE"] = "Napravi promenljivu…";
Blockly.Msg["NEW_VARIABLE_TITLE"] = "Ime nove promenljive:";
Blockly.Msg["NEW_VARIABLE_TYPE_TITLE"] = "New variable type:";  // untranslated
Blockly.Msg["ORDINAL_NUMBER_SUFFIX"] = "";  // untranslated
Blockly.Msg["PROCEDURES_ALLOW_STATEMENTS"] = "dozvoliti izreke";
Blockly.Msg["PROCEDURES_BEFORE_PARAMS"] = "sa:";
Blockly.Msg["PROCEDURES_CALLNORETURN_HELPURL"] = "https://en.wikipedia.org/wiki/Subroutine";
Blockly.Msg["PROCEDURES_CALLNORETURN_TOOLTIP"] = "Pokrenite prilagođenu funkciju „%1“.";
Blockly.Msg["PROCEDURES_CALLRETURN_HELPURL"] = "https://en.wikipedia.org/wiki/Subroutine";
Blockly.Msg["PROCEDURES_CALLRETURN_TOOLTIP"] = "Pokrenite prilagođenu funkciju „%1“ i koristi njen izlaz.";
Blockly.Msg["PROCEDURES_CALL_BEFORE_PARAMS"] = "sa:";
Blockly.Msg["PROCEDURES_CREATE_DO"] = "Napravi „%1“";
Blockly.Msg["PROCEDURES_DEFNORETURN_COMMENT"] = "Opisati ovu funkciju...";
Blockly.Msg["PROCEDURES_DEFNORETURN_DO"] = "";  // untranslated
Blockly.Msg["PROCEDURES_DEFNORETURN_HELPURL"] = "https://en.wikipedia.org/wiki/Procedure_%28computer_science%29";
Blockly.Msg["PROCEDURES_DEFNORETURN_PROCEDURE"] = "uradite nešto";
Blockly.Msg["PROCEDURES_DEFNORETURN_TITLE"] = "da";
Blockly.Msg["PROCEDURES_DEFNORETURN_TOOLTIP"] = "Pravi funkciju bez izlaza.";
Blockly.Msg["PROCEDURES_DEFRETURN_HELPURL"] = "https://en.wikipedia.org/wiki/Procedure_%28computer_science%29";
Blockly.Msg["PROCEDURES_DEFRETURN_RETURN"] = "vrati";
Blockly.Msg["PROCEDURES_DEFRETURN_TOOLTIP"] = "Pravi funkciju sa izlazom.";
Blockly.Msg["PROCEDURES_DEF_DUPLICATE_WARNING"] = "Upozorenje: Ova funkcija ima duplikate parametara.";
Blockly.Msg["PROCEDURES_HIGHLIGHT_DEF"] = "Istakni definiciju funkcije";
Blockly.Msg["PROCEDURES_IFRETURN_HELPURL"] = "http://c2.com/cgi/wiki?GuardClause";  // untranslated
Blockly.Msg["PROCEDURES_IFRETURN_TOOLTIP"] = "Ukoliko je vrednost tačna, vrati drugu vrednost.";
Blockly.Msg["PROCEDURES_IFRETURN_WARNING"] = "Upozorenje: Ovaj blok se može koristiti jedino u definiciji funkcije.";
Blockly.Msg["PROCEDURES_MUTATORARG_TITLE"] = "naziv ulaza:";
Blockly.Msg["PROCEDURES_MUTATORARG_TOOLTIP"] = "Dodajte ulazna funkcija.";
Blockly.Msg["PROCEDURES_MUTATORCONTAINER_TITLE"] = "ulazi";
Blockly.Msg["PROCEDURES_MUTATORCONTAINER_TOOLTIP"] = "Da dodate, uklonite ili pereuporяdočitь ulaza za ovu funkciju.";
Blockly.Msg["REDO"] = "Ponovi";
Blockly.Msg["REMOVE_COMMENT"] = "Ukloni komentar";
Blockly.Msg["RENAME_VARIABLE"] = "Preimenuj promenljivu…";
Blockly.Msg["RENAME_VARIABLE_TITLE"] = "Preimenuj sve „%1“ promenljive u:";
Blockly.Msg["TEXT_APPEND_HELPURL"] = "https://github.com/google/blockly/wiki/Text#text-modification";  // untranslated
Blockly.Msg["TEXT_APPEND_TITLE"] = "na %1 dodaj tekst %2";
Blockly.Msg["TEXT_APPEND_TOOLTIP"] = "Dodajte tekst na promenljivu „%1“.";
Blockly.Msg["TEXT_CHANGECASE_HELPURL"] = "https://github.com/google/blockly/wiki/Text#adjusting-text-case";  // untranslated
Blockly.Msg["TEXT_CHANGECASE_OPERATOR_LOWERCASE"] = "malim slovima";
Blockly.Msg["TEXT_CHANGECASE_OPERATOR_TITLECASE"] = "svaka reč velikim slovom";
Blockly.Msg["TEXT_CHANGECASE_OPERATOR_UPPERCASE"] = "velikim slovima";
Blockly.Msg["TEXT_CHANGECASE_TOOLTIP"] = "Vraća primerak teksta sa drugačijom veličinom slova.";
Blockly.Msg["TEXT_CHARAT_FIRST"] = "preuzmi prvo slovo";
Blockly.Msg["TEXT_CHARAT_FROM_END"] = "preuzmi slovo # sa kraja";
Blockly.Msg["TEXT_CHARAT_FROM_START"] = "preuzmi slovo #";
Blockly.Msg["TEXT_CHARAT_HELPURL"] = "https://github.com/google/blockly/wiki/Text#extracting-text";  // untranslated
Blockly.Msg["TEXT_CHARAT_LAST"] = "preuzmi poslednje slovo";
Blockly.Msg["TEXT_CHARAT_RANDOM"] = "preuzmi slučajno slovo";
Blockly.Msg["TEXT_CHARAT_TAIL"] = "";  // untranslated
Blockly.Msg["TEXT_CHARAT_TITLE"] = "u tekstu %1 %2";
Blockly.Msg["TEXT_CHARAT_TOOLTIP"] = "Vraća slovo na određeni položaj.";
Blockly.Msg["TEXT_COUNT_HELPURL"] = "https://github.com/google/blockly/wiki/Text#counting-substrings";
Blockly.Msg["TEXT_COUNT_MESSAGE0"] = "broj %1 u %2";
Blockly.Msg["TEXT_COUNT_TOOLTIP"] = "Broji koliko puta se neki tekst pojavljuje unutar nekog drugog teksta.";
Blockly.Msg["TEXT_CREATE_JOIN_ITEM_TOOLTIP"] = "Dodajte stavku u tekst.";
Blockly.Msg["TEXT_CREATE_JOIN_TITLE_JOIN"] = "spajanjem";
Blockly.Msg["TEXT_CREATE_JOIN_TOOLTIP"] = "Dodaj, ukloni, ili drugačije poredaj odjelke kako bi iznova postavili ovaj tekst blok.";
Blockly.Msg["TEXT_GET_SUBSTRING_END_FROM_END"] = "slovu # sa kraja";
Blockly.Msg["TEXT_GET_SUBSTRING_END_FROM_START"] = "slovu #";
Blockly.Msg["TEXT_GET_SUBSTRING_END_LAST"] = "poslednjem slovu";
Blockly.Msg["TEXT_GET_SUBSTRING_HELPURL"] = "https://github.com/google/blockly/wiki/Text#extracting-a-region-of-text";  // untranslated
Blockly.Msg["TEXT_GET_SUBSTRING_INPUT_IN_TEXT"] = "u tekstu";
Blockly.Msg["TEXT_GET_SUBSTRING_START_FIRST"] = "preuzmi podnisku iz prvog slova";
Blockly.Msg["TEXT_GET_SUBSTRING_START_FROM_END"] = "preuzmi podnisku iz slova # sa kraja";
Blockly.Msg["TEXT_GET_SUBSTRING_START_FROM_START"] = "preuzmi podnisku iz slova #";
Blockly.Msg["TEXT_GET_SUBSTRING_TAIL"] = "";  // untranslated
Blockly.Msg["TEXT_GET_SUBSTRING_TOOLTIP"] = "Vraća određeni deo teksta.";
Blockly.Msg["TEXT_INDEXOF_HELPURL"] = "https://github.com/google/blockly/wiki/Text#finding-text";  // untranslated
Blockly.Msg["TEXT_INDEXOF_OPERATOR_FIRST"] = "pronađi prvo pojavljivanje teksta";
Blockly.Msg["TEXT_INDEXOF_OPERATOR_LAST"] = "pronađi poslednje pojavljivanje teksta";
Blockly.Msg["TEXT_INDEXOF_TITLE"] = "u tekstu %1 %2 %3";
Blockly.Msg["TEXT_INDEXOF_TOOLTIP"] = "Vraća odnos prvog/zadnjeg pojavljivanja teksta u drugom tekstu. Vrađa %1 ako tekst nije pronađen.";
Blockly.Msg["TEXT_ISEMPTY_HELPURL"] = "https://github.com/google/blockly/wiki/Text#checking-for-empty-text";  // untranslated
Blockly.Msg["TEXT_ISEMPTY_TITLE"] = "%1 je prazan";
Blockly.Msg["TEXT_ISEMPTY_TOOLTIP"] = "Vraća tačno ako je dostavljeni tekst prazan.";
Blockly.Msg["TEXT_JOIN_HELPURL"] = "https://github.com/google/blockly/wiki/Text#text-creation";  // untranslated
Blockly.Msg["TEXT_JOIN_TITLE_CREATEWITH"] = "napiši tekst sa";
Blockly.Msg["TEXT_JOIN_TOOLTIP"] = "Napraviti dio teksta spajajući različite stavke.";
Blockly.Msg["TEXT_LENGTH_HELPURL"] = "https://github.com/google/blockly/wiki/Text#text-modification";  // untranslated
Blockly.Msg["TEXT_LENGTH_TITLE"] = "dužina teksta %1";
Blockly.Msg["TEXT_LENGTH_TOOLTIP"] = "Vraća broj slova (uključujući razmake) u datom tekstu.";
Blockly.Msg["TEXT_PRINT_HELPURL"] = "https://github.com/google/blockly/wiki/Text#printing-text";  // untranslated
Blockly.Msg["TEXT_PRINT_TITLE"] = "prikaži %1";
Blockly.Msg["TEXT_PRINT_TOOLTIP"] = "Prikažite određeni tekst, broj ili drugu vrednost na ekranu.";
Blockly.Msg["TEXT_PROMPT_HELPURL"] = "https://github.com/google/blockly/wiki/Text#getting-input-from-the-user";  // untranslated
Blockly.Msg["TEXT_PROMPT_TOOLTIP_NUMBER"] = "Pitajte korisnika za broj.";
Blockly.Msg["TEXT_PROMPT_TOOLTIP_TEXT"] = "Pitajte korisnika za unos teksta.";
Blockly.Msg["TEXT_PROMPT_TYPE_NUMBER"] = "pitaj za broj sa porukom";
Blockly.Msg["TEXT_PROMPT_TYPE_TEXT"] = "pitaj za tekst sa porukom";
Blockly.Msg["TEXT_REPLACE_HELPURL"] = "https://github.com/google/blockly/wiki/Text#replacing-substrings";
Blockly.Msg["TEXT_REPLACE_MESSAGE0"] = "zamena %1 sa %2 u %3";
Blockly.Msg["TEXT_REPLACE_TOOLTIP"] = "Zamena svih pojava nekog teksta unutar nekog drugog teksta.";
Blockly.Msg["TEXT_REVERSE_HELPURL"] = "https://github.com/google/blockly/wiki/Text#reversing-text";
Blockly.Msg["TEXT_REVERSE_MESSAGE0"] = "obrnuto %1";
Blockly.Msg["TEXT_REVERSE_TOOLTIP"] = "Obrće redosled karaktera u tekstu.";
Blockly.Msg["TEXT_TEXT_HELPURL"] = "https://sr.wikipedia.org/wiki/Niska";
Blockly.Msg["TEXT_TEXT_TOOLTIP"] = "Slovo, reč ili red teksta.";
Blockly.Msg["TEXT_TRIM_HELPURL"] = "https://github.com/google/blockly/wiki/Text#trimming-removing-spaces";  // untranslated
Blockly.Msg["TEXT_TRIM_OPERATOR_BOTH"] = "trim praznine sa obe strane";
Blockly.Msg["TEXT_TRIM_OPERATOR_LEFT"] = "skratiti prostor sa leve strane";
Blockly.Msg["TEXT_TRIM_OPERATOR_RIGHT"] = "skratiti prostor sa desne strane";
Blockly.Msg["TEXT_TRIM_TOOLTIP"] = "Vraća kopiju teksta sa uklonjenim prostorom sa jednog od dva kraja.";
Blockly.Msg["TODAY"] = "Danas";
Blockly.Msg["UNDO"] = "Opozovi";
Blockly.Msg["UNNAMED_KEY"] = "unnamed";  // untranslated
Blockly.Msg["VARIABLES_DEFAULT_NAME"] = "stavka";
Blockly.Msg["VARIABLES_GET_CREATE_SET"] = "Napravi „postavi %1“";
Blockly.Msg["VARIABLES_GET_HELPURL"] = "https://github.com/google/blockly/wiki/Variables#get";  // untranslated
Blockly.Msg["VARIABLES_GET_TOOLTIP"] = "Vraća vrednost ove promenljive.";
Blockly.Msg["VARIABLES_SET"] = "postavi %1 u %2";
Blockly.Msg["VARIABLES_SET_CREATE_GET"] = "Napravi „preuzmi %1“";
Blockly.Msg["VARIABLES_SET_HELPURL"] = "https://github.com/google/blockly/wiki/Variables#set";  // untranslated
Blockly.Msg["VARIABLES_SET_TOOLTIP"] = "Postavlja promenljivu tako da bude jednaka ulazu.";
Blockly.Msg["VARIABLE_ALREADY_EXISTS"] = "Promenljiva pod imenom '%1' već postoji.";
Blockly.Msg["VARIABLE_ALREADY_EXISTS_FOR_ANOTHER_TYPE"] = "Varijabla po imenu '%1' već postoji za drugu varijablu tipa '%2'.";
Blockly.Msg["WORKSPACE_ARIA_LABEL"] = "Blockly Workspace";  // untranslated
Blockly.Msg["WORKSPACE_COMMENT_DEFAULT_TEXT"] = "Say something...";  // untranslated
Blockly.Msg["CONTROLS_FOREACH_INPUT_DO"] = Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"];
Blockly.Msg["CONTROLS_FOR_INPUT_DO"] = Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"];
Blockly.Msg["CONTROLS_IF_ELSEIF_TITLE_ELSEIF"] = Blockly.Msg["CONTROLS_IF_MSG_ELSEIF"];
Blockly.Msg["CONTROLS_IF_ELSE_TITLE_ELSE"] = Blockly.Msg["CONTROLS_IF_MSG_ELSE"];
Blockly.Msg["CONTROLS_IF_IF_TITLE_IF"] = Blockly.Msg["CONTROLS_IF_MSG_IF"];
Blockly.Msg["CONTROLS_IF_MSG_THEN"] = Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"];
Blockly.Msg["CONTROLS_WHILEUNTIL_INPUT_DO"] = Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"];
Blockly.Msg["LISTS_CREATE_WITH_ITEM_TITLE"] = Blockly.Msg["VARIABLES_DEFAULT_NAME"];
Blockly.Msg["LISTS_GET_INDEX_HELPURL"] = Blockly.Msg["LISTS_INDEX_OF_HELPURL"];
Blockly.Msg["LISTS_GET_INDEX_INPUT_IN_LIST"] = Blockly.Msg["LISTS_INLIST"];
Blockly.Msg["LISTS_GET_SUBLIST_INPUT_IN_LIST"] = Blockly.Msg["LISTS_INLIST"];
Blockly.Msg["LISTS_INDEX_OF_INPUT_IN_LIST"] = Blockly.Msg["LISTS_INLIST"];
Blockly.Msg["LISTS_SET_INDEX_INPUT_IN_LIST"] = Blockly.Msg["LISTS_INLIST"];
Blockly.Msg["MATH_CHANGE_TITLE_ITEM"] = Blockly.Msg["VARIABLES_DEFAULT_NAME"];
Blockly.Msg["PROCEDURES_DEFRETURN_COMMENT"] = Blockly.Msg["PROCEDURES_DEFNORETURN_COMMENT"];
Blockly.Msg["PROCEDURES_DEFRETURN_DO"] = Blockly.Msg["PROCEDURES_DEFNORETURN_DO"];
Blockly.Msg["PROCEDURES_DEFRETURN_PROCEDURE"] = Blockly.Msg["PROCEDURES_DEFNORETURN_PROCEDURE"];
Blockly.Msg["PROCEDURES_DEFRETURN_TITLE"] = Blockly.Msg["PROCEDURES_DEFNORETURN_TITLE"];
Blockly.Msg["TEXT_APPEND_VARIABLE"] = Blockly.Msg["VARIABLES_DEFAULT_NAME"];
Blockly.Msg["TEXT_CREATE_JOIN_ITEM_TITLE_ITEM"] = Blockly.Msg["VARIABLES_DEFAULT_NAME"];

Blockly.Msg["MATH_HUE"] = "230";
Blockly.Msg["LOOPS_HUE"] = "120";
Blockly.Msg["LISTS_HUE"] = "260";
Blockly.Msg["LOGIC_HUE"] = "210";
Blockly.Msg["VARIABLES_HUE"] = "330";
Blockly.Msg["TEXTS_HUE"] = "160";
Blockly.Msg["PROCEDURES_HUE"] = "290";
Blockly.Msg["COLOUR_HUE"] = "20";
Blockly.Msg["VARIABLES_DYNAMIC_HUE"] = "310";