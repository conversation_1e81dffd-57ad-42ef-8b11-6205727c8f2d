// This file was automatically generated.  Do not modify.

'use strict';

Blockly.Msg["ADD_COMMENT"] = "Lägg till kommentar";
Blockly.Msg["CANNOT_DELETE_VARIABLE_PROCEDURE"] = "Kan inte radera variabeln '%1' eftersom den är en del av definition för funktionen '%2'";
Blockly.Msg["CHANGE_VALUE_TITLE"] = "Ändra värde:";
Blockly.Msg["CLEAN_UP"] = "Städa upp block";
Blockly.Msg["COLLAPSED_WARNINGS_WARNING"] = "Hopfällda block innehåller varningar.";
Blockly.Msg["COLLAPSE_ALL"] = "Fäll ihop block";
Blockly.Msg["COLLAPSE_BLOCK"] = "Fäll ihop block";
Blockly.Msg["COLOUR_BLEND_COLOUR1"] = "färg 1";
Blockly.Msg["COLOUR_BLEND_COLOUR2"] = "färg 2";
Blockly.Msg["COLOUR_BLEND_HELPURL"] = "https://meyerweb.com/eric/tools/color-blend/#:::rgbp";
Blockly.Msg["COLOUR_BLEND_RATIO"] = "förhållande";
Blockly.Msg["COLOUR_BLEND_TITLE"] = "blanda";
Blockly.Msg["COLOUR_BLEND_TOOLTIP"] = "Blandar ihop två färger med ett bestämt förhållande (0.0 - 1.0).";
Blockly.Msg["COLOUR_PICKER_HELPURL"] = "https://sv.wikipedia.org/wiki/Färg";
Blockly.Msg["COLOUR_PICKER_TOOLTIP"] = "Välj en färg från paletten.";
Blockly.Msg["COLOUR_RANDOM_HELPURL"] = "http://randomcolour.com";  // untranslated
Blockly.Msg["COLOUR_RANDOM_TITLE"] = "slumpfärg";
Blockly.Msg["COLOUR_RANDOM_TOOLTIP"] = "Slumpa fram en färg.";
Blockly.Msg["COLOUR_RGB_BLUE"] = "blå";
Blockly.Msg["COLOUR_RGB_GREEN"] = "grön";
Blockly.Msg["COLOUR_RGB_HELPURL"] = "https://www.december.com/html/spec/colorpercompact.html";
Blockly.Msg["COLOUR_RGB_RED"] = "röd";
Blockly.Msg["COLOUR_RGB_TITLE"] = "färg med";
Blockly.Msg["COLOUR_RGB_TOOLTIP"] = "Skapa en färg med det angivna mängden röd, grön och blå. Alla värden måste vara mellan 0 och 100.";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_HELPURL"] = "https://github.com/google/blockly/wiki/Loops#loop-termination-blocks";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_OPERATOR_BREAK"] = "bryt ut ur loop";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_OPERATOR_CONTINUE"] = "fortsätta med nästa upprepning av loop";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_TOOLTIP_BREAK"] = "Bryt ut ur den innehållande upprepningen.";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_TOOLTIP_CONTINUE"] = "Hoppa över resten av denna loop och fortsätt med nästa loop.";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_WARNING"] = "Varning: Detta block kan endast användas i en loop.";
Blockly.Msg["CONTROLS_FOREACH_HELPURL"] = "https://github.com/google/blockly/wiki/Loops#for-each";
Blockly.Msg["CONTROLS_FOREACH_TITLE"] = "för varje föremål %1 i listan %2";
Blockly.Msg["CONTROLS_FOREACH_TOOLTIP"] = "För varje objekt i en lista, ange variabeln '%1' till objektet, och utför sedan några kommandon.";
Blockly.Msg["CONTROLS_FOR_HELPURL"] = "https://github.com/google/blockly/wiki/Loops#count-with";
Blockly.Msg["CONTROLS_FOR_TITLE"] = "räkna med %1 från %2 till %3 med %4";
Blockly.Msg["CONTROLS_FOR_TOOLTIP"] = "Låt variabeln \"%1\" anta värden från starttalet till sluttalet, beräknat med det angivna intervallet, och utför de angivna blocken.";
Blockly.Msg["CONTROLS_IF_ELSEIF_TOOLTIP"] = "Lägg till ett villkor blocket \"om\".";
Blockly.Msg["CONTROLS_IF_ELSE_TOOLTIP"] = "Lägg till ett sista villkor som täcker alla alternativ som är kvar för \"if\"-blocket.";
Blockly.Msg["CONTROLS_IF_HELPURL"] = "https://github.com/google/blockly/wiki/IfElse";
Blockly.Msg["CONTROLS_IF_IF_TOOLTIP"] = "Lägg till, ta bort eller ändra ordningen för sektioner för att omkonfigurera blocket \"om\".";
Blockly.Msg["CONTROLS_IF_MSG_ELSE"] = "annars";
Blockly.Msg["CONTROLS_IF_MSG_ELSEIF"] = "annars om";
Blockly.Msg["CONTROLS_IF_MSG_IF"] = "om";
Blockly.Msg["CONTROLS_IF_TOOLTIP_1"] = "Om ett värde är sant, utför några kommandon.";
Blockly.Msg["CONTROLS_IF_TOOLTIP_2"] = "Om värdet är sant, utför det första kommandoblocket. Utför annars det andra kommandoblocket.";
Blockly.Msg["CONTROLS_IF_TOOLTIP_3"] = "Om det första värdet är sant, utför det första kommandoblocket. Annars, om det andra värdet är sant, utför det andra kommandoblocket.";
Blockly.Msg["CONTROLS_IF_TOOLTIP_4"] = "Om det första värdet är sant, utför det första kommandoblocket. Annars, om det andra värdet är sant, utför det andra kommandoblocket. Om ingen av värdena är sanna, utför det sista kommandoblocket.";
Blockly.Msg["CONTROLS_REPEAT_HELPURL"] = "https://en.wikipedia.org/wiki/For_loop";
Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"] = "utför";
Blockly.Msg["CONTROLS_REPEAT_TITLE"] = "upprepa %1 gånger";
Blockly.Msg["CONTROLS_REPEAT_TOOLTIP"] = "Utför några kommandon flera gånger.";
Blockly.Msg["CONTROLS_WHILEUNTIL_HELPURL"] = "https://github.com/google/blockly/wiki/Loops#repeat";
Blockly.Msg["CONTROLS_WHILEUNTIL_OPERATOR_UNTIL"] = "upprepa tills";
Blockly.Msg["CONTROLS_WHILEUNTIL_OPERATOR_WHILE"] = "upprepa så länge";
Blockly.Msg["CONTROLS_WHILEUNTIL_TOOLTIP_UNTIL"] = "Medan ett värde är falskt, utför några kommandon.";
Blockly.Msg["CONTROLS_WHILEUNTIL_TOOLTIP_WHILE"] = "Medan ett värde är sant, utför några kommandon.";
Blockly.Msg["DELETE_ALL_BLOCKS"] = "Radera alla %1 block?";
Blockly.Msg["DELETE_BLOCK"] = "Radera block";
Blockly.Msg["DELETE_VARIABLE"] = "Radera variabeln \"%1\"";
Blockly.Msg["DELETE_VARIABLE_CONFIRMATION"] = "Radera %1 användningar av variabeln \"%2\"?";
Blockly.Msg["DELETE_X_BLOCKS"] = "Radera %1 block";
Blockly.Msg["DIALOG_CANCEL"] = "Avbryt";
Blockly.Msg["DIALOG_OK"] = "OK";
Blockly.Msg["DISABLE_BLOCK"] = "Inaktivera block";
Blockly.Msg["DUPLICATE_BLOCK"] = "Duplicera";
Blockly.Msg["DUPLICATE_COMMENT"] = "Duplicera kommentar";
Blockly.Msg["ENABLE_BLOCK"] = "Aktivera block";
Blockly.Msg["EXPAND_ALL"] = "Fäll ut block";
Blockly.Msg["EXPAND_BLOCK"] = "Fäll ut block";
Blockly.Msg["EXTERNAL_INPUTS"] = "Externa inmatningar";
Blockly.Msg["HELP"] = "Hjälp";
Blockly.Msg["INLINE_INPUTS"] = "Radinmatning";
Blockly.Msg["LISTS_CREATE_EMPTY_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#create-empty-list";
Blockly.Msg["LISTS_CREATE_EMPTY_TITLE"] = "skapa tom lista";
Blockly.Msg["LISTS_CREATE_EMPTY_TOOLTIP"] = "Ger tillbaka en lista utan någon data, alltså med längden 0";
Blockly.Msg["LISTS_CREATE_WITH_CONTAINER_TITLE_ADD"] = "lista";
Blockly.Msg["LISTS_CREATE_WITH_CONTAINER_TOOLTIP"] = "Lägg till, ta bort eller ändra ordningen på objekten för att göra om det här \"list\"-blocket.";
Blockly.Msg["LISTS_CREATE_WITH_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#create-list-with";
Blockly.Msg["LISTS_CREATE_WITH_INPUT_WITH"] = "skapa lista med";
Blockly.Msg["LISTS_CREATE_WITH_ITEM_TOOLTIP"] = "Lägg till ett föremål till listan.";
Blockly.Msg["LISTS_CREATE_WITH_TOOLTIP"] = "Skapa en lista med valfritt antal föremål.";
Blockly.Msg["LISTS_GET_INDEX_FIRST"] = "första";
Blockly.Msg["LISTS_GET_INDEX_FROM_END"] = "# från slutet";
Blockly.Msg["LISTS_GET_INDEX_FROM_START"] = "#";
Blockly.Msg["LISTS_GET_INDEX_GET"] = "hämta";
Blockly.Msg["LISTS_GET_INDEX_GET_REMOVE"] = "hämta och ta bort";
Blockly.Msg["LISTS_GET_INDEX_LAST"] = "sista";
Blockly.Msg["LISTS_GET_INDEX_RANDOM"] = "slumpad";
Blockly.Msg["LISTS_GET_INDEX_REMOVE"] = "ta bort";
Blockly.Msg["LISTS_GET_INDEX_TAIL"] = "";  // untranslated
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_FIRST"] = "Returnerar det första objektet i en lista.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_FROM"] = "Ger tillbaka objektet på den efterfrågade positionen i en lista.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_LAST"] = "Returnerar det sista objektet i en lista.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_RANDOM"] = "Returnerar ett slumpmässigt objekt i en lista.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_FIRST"] = "Tar bort och återställer det första objektet i en lista.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_FROM"] = "Tar bort och återställer objektet på den specificerade positionen i en lista.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_LAST"] = "Tar bort och återställer det sista objektet i en lista.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_RANDOM"] = "Tar bort och återställer ett slumpmässigt objekt i en lista.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_REMOVE_FIRST"] = "Tar bort det första objektet i en lista.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_REMOVE_FROM"] = "Tar bort objektet på den specificerade positionen i en lista.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_REMOVE_LAST"] = "Tar bort det sista objektet i en lista.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_REMOVE_RANDOM"] = "Tar bort en slumpmässig post i en lista.";
Blockly.Msg["LISTS_GET_SUBLIST_END_FROM_END"] = "till # från slutet";
Blockly.Msg["LISTS_GET_SUBLIST_END_FROM_START"] = "till #";
Blockly.Msg["LISTS_GET_SUBLIST_END_LAST"] = "till sista";
Blockly.Msg["LISTS_GET_SUBLIST_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#getting-a-sublist";  // untranslated
Blockly.Msg["LISTS_GET_SUBLIST_START_FIRST"] = "få underlista från första";
Blockly.Msg["LISTS_GET_SUBLIST_START_FROM_END"] = "få underlista från # från slutet";
Blockly.Msg["LISTS_GET_SUBLIST_START_FROM_START"] = "få underlista från #";
Blockly.Msg["LISTS_GET_SUBLIST_TAIL"] = "";  // untranslated
Blockly.Msg["LISTS_GET_SUBLIST_TOOLTIP"] = "Skapar en kopia av den specificerade delen av en lista.";
Blockly.Msg["LISTS_INDEX_FROM_END_TOOLTIP"] = "%1 är det sista objektet.";
Blockly.Msg["LISTS_INDEX_FROM_START_TOOLTIP"] = "%1 är det första objektet.";
Blockly.Msg["LISTS_INDEX_OF_FIRST"] = "hitta första förekomsten av objektet";
Blockly.Msg["LISTS_INDEX_OF_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#getting-items-from-a-list";  // untranslated
Blockly.Msg["LISTS_INDEX_OF_LAST"] = "hitta sista förekomsten av objektet";
Blockly.Msg["LISTS_INDEX_OF_TOOLTIP"] = "Ger tillbaka den första/sista förekomsten av objektet i listan. Returnerar %1 om objektet inte hittas.";
Blockly.Msg["LISTS_INLIST"] = "i listan";
Blockly.Msg["LISTS_ISEMPTY_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#is-empty";  // untranslated
Blockly.Msg["LISTS_ISEMPTY_TITLE"] = "%1 är tom";
Blockly.Msg["LISTS_ISEMPTY_TOOLTIP"] = "Returnerar sant om listan är tom.";
Blockly.Msg["LISTS_LENGTH_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#length-of";  // untranslated
Blockly.Msg["LISTS_LENGTH_TITLE"] = "längden på %1";
Blockly.Msg["LISTS_LENGTH_TOOLTIP"] = "Returnerar längden på en lista.";
Blockly.Msg["LISTS_REPEAT_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#create-list-with";  // untranslated
Blockly.Msg["LISTS_REPEAT_TITLE"] = "skapa lista med föremålet %1 upprepat %2 gånger";
Blockly.Msg["LISTS_REPEAT_TOOLTIP"] = "Skapar en lista som innehåller ett valt värde upprepat ett bestämt antalet gånger.";
Blockly.Msg["LISTS_REVERSE_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#reversing-a-list";
Blockly.Msg["LISTS_REVERSE_MESSAGE0"] = "vänd på %1";
Blockly.Msg["LISTS_REVERSE_TOOLTIP"] = "Vänd på en kopia av en lista.";
Blockly.Msg["LISTS_SET_INDEX_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#in-list--set";  // untranslated
Blockly.Msg["LISTS_SET_INDEX_INPUT_TO"] = "som";
Blockly.Msg["LISTS_SET_INDEX_INSERT"] = "Sätt in vid";
Blockly.Msg["LISTS_SET_INDEX_SET"] = "ange";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_INSERT_FIRST"] = "sätter in objektet i början av en lista.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_INSERT_FROM"] = "Sätter in objektet vid en specificerad position i en lista.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_INSERT_LAST"] = "Lägg till objektet i slutet av en lista.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_INSERT_RANDOM"] = "sätter in objektet på en slumpad position i en lista.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_SET_FIRST"] = "Anger det första objektet i en lista.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_SET_FROM"] = "Sätter in objektet vid en specificerad position i en lista.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_SET_LAST"] = "Anger det sista elementet i en lista.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_SET_RANDOM"] = "Sätter in ett slumpat objekt i en lista.";
Blockly.Msg["LISTS_SORT_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#sorting-a-list";
Blockly.Msg["LISTS_SORT_ORDER_ASCENDING"] = "stigande";
Blockly.Msg["LISTS_SORT_ORDER_DESCENDING"] = "fallande";
Blockly.Msg["LISTS_SORT_TITLE"] = "sortera %1 %2 %3";
Blockly.Msg["LISTS_SORT_TOOLTIP"] = "Sortera en kopia av en lista.";
Blockly.Msg["LISTS_SORT_TYPE_IGNORECASE"] = "alfabetiskt, ignorera skiftläge";
Blockly.Msg["LISTS_SORT_TYPE_NUMERIC"] = "numeriskt";
Blockly.Msg["LISTS_SORT_TYPE_TEXT"] = "alfabetiskt";
Blockly.Msg["LISTS_SPLIT_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#splitting-strings-and-joining-lists";
Blockly.Msg["LISTS_SPLIT_LIST_FROM_TEXT"] = "skapa lista från text";
Blockly.Msg["LISTS_SPLIT_TEXT_FROM_LIST"] = "skapa text från lista";
Blockly.Msg["LISTS_SPLIT_TOOLTIP_JOIN"] = "Sammanfoga en textlista till en text, som separeras av en avgränsare.";
Blockly.Msg["LISTS_SPLIT_TOOLTIP_SPLIT"] = "Dela upp text till en textlista och bryt vid varje avgränsare.";
Blockly.Msg["LISTS_SPLIT_WITH_DELIMITER"] = "med avgränsare";
Blockly.Msg["LOGIC_BOOLEAN_FALSE"] = "falskt";
Blockly.Msg["LOGIC_BOOLEAN_HELPURL"] = "https://github.com/google/blockly/wiki/Logic#values";  // untranslated
Blockly.Msg["LOGIC_BOOLEAN_TOOLTIP"] = "Returnerar antingen sant eller falskt.";
Blockly.Msg["LOGIC_BOOLEAN_TRUE"] = "sant";
Blockly.Msg["LOGIC_COMPARE_HELPURL"] = "https://sv.wikipedia.org/wiki/Olikhet";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_EQ"] = "Ger tillbaka sant om båda värdena är lika med varandra.";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_GT"] = "Ger tillbaka sant om det första värdet är större än det andra.";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_GTE"] = "Ger tillbaka sant om det första värdet är större än eller lika med det andra.";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_LT"] = "Ger tillbaka sant om det första värdet är mindre än det andra.";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_LTE"] = "Ger tillbaka sant om det första värdet är mindre än eller lika med det andra.";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_NEQ"] = "Ger tillbaka sant om båda värdena inte är lika med varandra.";
Blockly.Msg["LOGIC_NEGATE_HELPURL"] = "https://github.com/google/blockly/wiki/Logic#not";
Blockly.Msg["LOGIC_NEGATE_TITLE"] = "inte %1";
Blockly.Msg["LOGIC_NEGATE_TOOLTIP"] = "Ger tillbaka sant om inmatningen är falsk. Ger tillbaka falskt och inmatningen är sann.";
Blockly.Msg["LOGIC_NULL"] = "null";
Blockly.Msg["LOGIC_NULL_HELPURL"] = "https://sv.wikipedia.org/wiki/Null";
Blockly.Msg["LOGIC_NULL_TOOLTIP"] = "Returnerar null.";
Blockly.Msg["LOGIC_OPERATION_AND"] = "och";
Blockly.Msg["LOGIC_OPERATION_HELPURL"] = "https://github.com/google/blockly/wiki/Logic#logical-operations";
Blockly.Msg["LOGIC_OPERATION_OR"] = "eller";
Blockly.Msg["LOGIC_OPERATION_TOOLTIP_AND"] = "Ger tillbaka sant om båda värdena är sanna.";
Blockly.Msg["LOGIC_OPERATION_TOOLTIP_OR"] = "Ger tillbaka sant om minst ett av värdena är sant.";
Blockly.Msg["LOGIC_TERNARY_CONDITION"] = "test";
Blockly.Msg["LOGIC_TERNARY_HELPURL"] = "https://en.wikipedia.org/wiki/%3F:";
Blockly.Msg["LOGIC_TERNARY_IF_FALSE"] = "om falskt";
Blockly.Msg["LOGIC_TERNARY_IF_TRUE"] = "om sant";
Blockly.Msg["LOGIC_TERNARY_TOOLTIP"] = "Kontrollera villkoret i \"test\". Om villkoret är sant, ge tillbaka \"om sant\"-värdet; annars ge tillbaka \"om falskt\"-värdet.";
Blockly.Msg["MATH_ADDITION_SYMBOL"] = "+";
Blockly.Msg["MATH_ARITHMETIC_HELPURL"] = "https://sv.wikipedia.org/wiki/Aritmetik";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_ADD"] = "Returnerar summan av de två talen.";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_DIVIDE"] = "Returnerar kvoten av de två talen.";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_MINUS"] = "Returnerar differensen mellan de två talen.";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_MULTIPLY"] = "Returnerar produkten av de två talen.";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_POWER"] = "Ger tillbaka det första talet upphöjt till det andra talet.";
Blockly.Msg["MATH_ATAN2_HELPURL"] = "https://en.wikipedia.org/wiki/Atan2";
Blockly.Msg["MATH_ATAN2_TITLE"] = "atan2 av X:%1 Y:%2";
Blockly.Msg["MATH_ATAN2_TOOLTIP"] = "Returnerar arcustangens av punkt (X, Y) i grader från -180 till 180.";
Blockly.Msg["MATH_CHANGE_HELPURL"] = "https://en.wikipedia.org/wiki/Programming_idiom#Incrementing_a_counter";
Blockly.Msg["MATH_CHANGE_TITLE"] = "ändra %1 med %2";
Blockly.Msg["MATH_CHANGE_TOOLTIP"] = "Lägg till ett tal till variabeln '%1'.";
Blockly.Msg["MATH_CONSTANT_HELPURL"] = "https://sv.wikipedia.org/wiki/Matematisk_konstant";
Blockly.Msg["MATH_CONSTANT_TOOLTIP"] = "Returnerar en av de vanliga konstanterna: π (3.141…), e (2.718…), φ (1.618…), sqrt(2) (1.414…), sqrt(½) (0.707…) eller ∞ (oändligt).";
Blockly.Msg["MATH_CONSTRAIN_HELPURL"] = "https://en.wikipedia.org/wiki/Clamping_(graphics)";  // untranslated
Blockly.Msg["MATH_CONSTRAIN_TITLE"] = "begränsa %1 till mellan %2 och %3";
Blockly.Msg["MATH_CONSTRAIN_TOOLTIP"] = "Begränsa ett tal till att mellan de angivna gränsvärden (inkluderande).";
Blockly.Msg["MATH_DIVISION_SYMBOL"] = "÷";
Blockly.Msg["MATH_IS_DIVISIBLE_BY"] = "är delbart med";
Blockly.Msg["MATH_IS_EVEN"] = "är jämnt";
Blockly.Msg["MATH_IS_NEGATIVE"] = "är negativt";
Blockly.Msg["MATH_IS_ODD"] = "är ojämnt";
Blockly.Msg["MATH_IS_POSITIVE"] = "är positivt";
Blockly.Msg["MATH_IS_PRIME"] = "är ett primtal";
Blockly.Msg["MATH_IS_TOOLTIP"] = "Kontrollera om ett tal är jämnt, ojämnt, helt, positivt, negativt eller det är delbart med ett bestämt tal. Returnerar med sant eller falskt.";
Blockly.Msg["MATH_IS_WHOLE"] = "är helt";
Blockly.Msg["MATH_MODULO_HELPURL"] = "https://sv.wikipedia.org/wiki/Modulär_aritmetik";
Blockly.Msg["MATH_MODULO_TITLE"] = "resten av %1 ÷ %2";
Blockly.Msg["MATH_MODULO_TOOLTIP"] = "Returnerar kvoten från divisionen av de två talen.";
Blockly.Msg["MATH_MULTIPLICATION_SYMBOL"] = "×";
Blockly.Msg["MATH_NUMBER_HELPURL"] = "https://sv.wikipedia.org/wiki/Tal";
Blockly.Msg["MATH_NUMBER_TOOLTIP"] = "Ett tal.";
Blockly.Msg["MATH_ONLIST_HELPURL"] = "";  // untranslated
Blockly.Msg["MATH_ONLIST_OPERATOR_AVERAGE"] = "medelvärdet av listan";
Blockly.Msg["MATH_ONLIST_OPERATOR_MAX"] = "högsta talet i listan";
Blockly.Msg["MATH_ONLIST_OPERATOR_MEDIAN"] = "medianen av listan";
Blockly.Msg["MATH_ONLIST_OPERATOR_MIN"] = "minsta talet i listan";
Blockly.Msg["MATH_ONLIST_OPERATOR_MODE"] = "typvärdet i listan";
Blockly.Msg["MATH_ONLIST_OPERATOR_RANDOM"] = "slumpmässigt objekt i listan";
Blockly.Msg["MATH_ONLIST_OPERATOR_STD_DEV"] = "standardavvikelsen i listan";
Blockly.Msg["MATH_ONLIST_OPERATOR_SUM"] = "summan av listan";
Blockly.Msg["MATH_ONLIST_TOOLTIP_AVERAGE"] = "Ger tillbaka medelvärdet (aritmetiskt) av de numeriska värdena i listan.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_MAX"] = "Ger tillbaka det största talet i listan.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_MEDIAN"] = "Returnerar medianen av talen i listan.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_MIN"] = "Ger tillbaka det minsta talet i listan.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_MODE"] = "Ger tillbaka en lista med de(t) vanligaste objekte(t/n) i listan.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_RANDOM"] = "Returnerar ett slumpmässigt element från listan.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_STD_DEV"] = "Ger tillbaka standardavvikelsen i listan.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_SUM"] = "Ger tillbaka summan av alla talen i listan.";
Blockly.Msg["MATH_POWER_SYMBOL"] = "^";
Blockly.Msg["MATH_RANDOM_FLOAT_HELPURL"] = "https://sv.wikipedia.org/wiki/Slumptalsgenerator";
Blockly.Msg["MATH_RANDOM_FLOAT_TITLE_RANDOM"] = "slumpat decimaltal";
Blockly.Msg["MATH_RANDOM_FLOAT_TOOLTIP"] = "Ger tillbaka ett slumpat decimaltal mellan 0.0 (inkluderat) och 1.0 (exkluderat).";
Blockly.Msg["MATH_RANDOM_INT_HELPURL"] = "https://sv.wikipedia.org/wiki/Slumptalsgenerator";
Blockly.Msg["MATH_RANDOM_INT_TITLE"] = "slumpartat heltal från %1 till %2";
Blockly.Msg["MATH_RANDOM_INT_TOOLTIP"] = "Ger tillbaka ett slumpat heltal mellan två värden, inkluderande.";
Blockly.Msg["MATH_ROUND_HELPURL"] = "https://sv.wikipedia.org/wiki/Avrundning";
Blockly.Msg["MATH_ROUND_OPERATOR_ROUND"] = "avrunda";
Blockly.Msg["MATH_ROUND_OPERATOR_ROUNDDOWN"] = "avrunda nedåt";
Blockly.Msg["MATH_ROUND_OPERATOR_ROUNDUP"] = "avrunda uppåt";
Blockly.Msg["MATH_ROUND_TOOLTIP"] = "Avrunda ett tal uppåt eller nedåt.";
Blockly.Msg["MATH_SINGLE_HELPURL"] = "https://sv.wikipedia.org/wiki/Kvadratrot";
Blockly.Msg["MATH_SINGLE_OP_ABSOLUTE"] = "absolut";
Blockly.Msg["MATH_SINGLE_OP_ROOT"] = "kvadratrot";
Blockly.Msg["MATH_SINGLE_TOOLTIP_ABS"] = "Returnerar absolutvärdet av ett tal.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_EXP"] = "Ger tillbaka e upphöjt i ett tal.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_LN"] = "Returnera den naturliga logaritmen av ett tal.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_LOG10"] = "Returnerar logaritmen för bas 10 av ett tal.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_NEG"] = "Returnerar negationen av ett tal.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_POW10"] = "Ger tillbaka 10 upphöjt i ett tal.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_ROOT"] = "Returnerar kvadratroten av ett tal.";
Blockly.Msg["MATH_SUBTRACTION_SYMBOL"] = "-";
Blockly.Msg["MATH_TRIG_ACOS"] = "arccos";
Blockly.Msg["MATH_TRIG_ASIN"] = "arcsin";
Blockly.Msg["MATH_TRIG_ATAN"] = "arctan";
Blockly.Msg["MATH_TRIG_COS"] = "cos";
Blockly.Msg["MATH_TRIG_HELPURL"] = "https://sv.wikipedia.org/wiki/Trigonometrisk_funktion";
Blockly.Msg["MATH_TRIG_SIN"] = "sin";
Blockly.Msg["MATH_TRIG_TAN"] = "tan";
Blockly.Msg["MATH_TRIG_TOOLTIP_ACOS"] = "Ger tillbaka arcus cosinus (arccos) för ett tal.";
Blockly.Msg["MATH_TRIG_TOOLTIP_ASIN"] = "Ger tillbaka arcus sinus (arcsin) för ett tal.";
Blockly.Msg["MATH_TRIG_TOOLTIP_ATAN"] = "Ger tillbaka arcus tangens (arctan) av ett tal.";
Blockly.Msg["MATH_TRIG_TOOLTIP_COS"] = "Ger tillbaka cosinus för en grad (inte radian).";
Blockly.Msg["MATH_TRIG_TOOLTIP_SIN"] = "Ger tillbaka sinus för en grad (inte radian).";
Blockly.Msg["MATH_TRIG_TOOLTIP_TAN"] = "Ger tillbaka tangens för en grad (inte radian).";
Blockly.Msg["NEW_COLOUR_VARIABLE"] = "Skapa färgvariabel...";
Blockly.Msg["NEW_NUMBER_VARIABLE"] = "Skapa siffervariabel...";
Blockly.Msg["NEW_STRING_VARIABLE"] = "Skapa strängvariabel...";
Blockly.Msg["NEW_VARIABLE"] = "Skapa variabel...";
Blockly.Msg["NEW_VARIABLE_TITLE"] = "Nytt variabelnamn:";
Blockly.Msg["NEW_VARIABLE_TYPE_TITLE"] = "Ny variabeltyp:";
Blockly.Msg["ORDINAL_NUMBER_SUFFIX"] = "";  // untranslated
Blockly.Msg["PROCEDURES_ALLOW_STATEMENTS"] = "tillåta uttalanden";
Blockly.Msg["PROCEDURES_BEFORE_PARAMS"] = "med:";
Blockly.Msg["PROCEDURES_CALLNORETURN_HELPURL"] = "https://en.wikipedia.org/wiki/Subroutine";
Blockly.Msg["PROCEDURES_CALLNORETURN_TOOLTIP"] = "Kör den användardefinierade funktionen \"%1\".";
Blockly.Msg["PROCEDURES_CALLRETURN_HELPURL"] = "https://en.wikipedia.org/wiki/Subroutine";
Blockly.Msg["PROCEDURES_CALLRETURN_TOOLTIP"] = "Kör den användardefinierade funktionen \"%1\" och använd resultatet av den.";
Blockly.Msg["PROCEDURES_CALL_BEFORE_PARAMS"] = "med:";
Blockly.Msg["PROCEDURES_CREATE_DO"] = "Skapa '%1'";
Blockly.Msg["PROCEDURES_DEFNORETURN_COMMENT"] = "Beskriv denna funktion...";
Blockly.Msg["PROCEDURES_DEFNORETURN_DO"] = "";  // untranslated
Blockly.Msg["PROCEDURES_DEFNORETURN_HELPURL"] = "https://sv.wikipedia.org/wiki/Funktion_(programmering)";
Blockly.Msg["PROCEDURES_DEFNORETURN_PROCEDURE"] = "göra något";
Blockly.Msg["PROCEDURES_DEFNORETURN_TITLE"] = "för att";
Blockly.Msg["PROCEDURES_DEFNORETURN_TOOLTIP"] = "Skapar en funktion utan output.";
Blockly.Msg["PROCEDURES_DEFRETURN_HELPURL"] = "https://sv.wikipedia.org/wiki/Funktion_(programmering)";
Blockly.Msg["PROCEDURES_DEFRETURN_RETURN"] = "returnera";
Blockly.Msg["PROCEDURES_DEFRETURN_TOOLTIP"] = "Skapar en funktion med output.";
Blockly.Msg["PROCEDURES_DEF_DUPLICATE_WARNING"] = "Varning: Denna funktion har dubbla parametrar.";
Blockly.Msg["PROCEDURES_HIGHLIGHT_DEF"] = "Markera funktionsdefinition";
Blockly.Msg["PROCEDURES_IFRETURN_HELPURL"] = "http://c2.com/cgi/wiki?GuardClause";
Blockly.Msg["PROCEDURES_IFRETURN_TOOLTIP"] = "Om ett värde är sant returneras ett andra värde.";
Blockly.Msg["PROCEDURES_IFRETURN_WARNING"] = "Varning: Detta block får användas endast i en funktionsdefinition.";
Blockly.Msg["PROCEDURES_MUTATORARG_TITLE"] = "inmatningsnamn:";
Blockly.Msg["PROCEDURES_MUTATORARG_TOOLTIP"] = "Lägg till en inmatning till funktionen.";
Blockly.Msg["PROCEDURES_MUTATORCONTAINER_TITLE"] = "inmatningar";
Blockly.Msg["PROCEDURES_MUTATORCONTAINER_TOOLTIP"] = "Lägg till, ta bort och ändra ordningen för inmatningar till denna funktion.";
Blockly.Msg["REDO"] = "Gör om";
Blockly.Msg["REMOVE_COMMENT"] = "Radera kommentar";
Blockly.Msg["RENAME_VARIABLE"] = "Byt namn på variabel...";
Blockly.Msg["RENAME_VARIABLE_TITLE"] = "Byt namn på alla'%1'-variabler till:";
Blockly.Msg["TEXT_APPEND_HELPURL"] = "https://github.com/google/blockly/wiki/Text#text-modification";  // untranslated
Blockly.Msg["TEXT_APPEND_TITLE"] = "för att %1 lägga till text %2";
Blockly.Msg["TEXT_APPEND_TOOLTIP"] = "Lägg till lite text till variabeln '%1'.";
Blockly.Msg["TEXT_CHANGECASE_HELPURL"] = "https://github.com/google/blockly/wiki/Text#adjusting-text-case";  // untranslated
Blockly.Msg["TEXT_CHANGECASE_OPERATOR_LOWERCASE"] = "till gemener";
Blockly.Msg["TEXT_CHANGECASE_OPERATOR_TITLECASE"] = "till Versala Initialer";
Blockly.Msg["TEXT_CHANGECASE_OPERATOR_UPPERCASE"] = "till VERSALER";
Blockly.Msg["TEXT_CHANGECASE_TOOLTIP"] = "Returnerar en kopia av texten i ett annat skiftläge.";
Blockly.Msg["TEXT_CHARAT_FIRST"] = "hämta första bokstaven";
Blockly.Msg["TEXT_CHARAT_FROM_END"] = "hämta bokstaven # från slutet";
Blockly.Msg["TEXT_CHARAT_FROM_START"] = "hämta bokstaven #";
Blockly.Msg["TEXT_CHARAT_HELPURL"] = "https://github.com/google/blockly/wiki/Text#extracting-text";  // untranslated
Blockly.Msg["TEXT_CHARAT_LAST"] = "hämta sista bokstaven";
Blockly.Msg["TEXT_CHARAT_RANDOM"] = "hämta slumpad bokstav";
Blockly.Msg["TEXT_CHARAT_TAIL"] = "";  // untranslated
Blockly.Msg["TEXT_CHARAT_TITLE"] = "i texten %1 %2";
Blockly.Msg["TEXT_CHARAT_TOOLTIP"] = "Ger tillbaka bokstaven på den specificerade positionen.";
Blockly.Msg["TEXT_COUNT_HELPURL"] = "https://github.com/google/blockly/wiki/Text#counting-substrings";
Blockly.Msg["TEXT_COUNT_MESSAGE0"] = "räkna %1 i %2";
Blockly.Msg["TEXT_COUNT_TOOLTIP"] = "Räkna hur många gånger en text förekommer inom en annan text.";
Blockly.Msg["TEXT_CREATE_JOIN_ITEM_TOOLTIP"] = "Lägg till ett föremål till texten.";
Blockly.Msg["TEXT_CREATE_JOIN_TITLE_JOIN"] = "sammanfoga";
Blockly.Msg["TEXT_CREATE_JOIN_TOOLTIP"] = "Lägg till, ta bort eller ändra ordningen för sektioner för att omkonfigurera detta textblock.";
Blockly.Msg["TEXT_GET_SUBSTRING_END_FROM_END"] = "till bokstav # från slutet";
Blockly.Msg["TEXT_GET_SUBSTRING_END_FROM_START"] = "till bokstav #";
Blockly.Msg["TEXT_GET_SUBSTRING_END_LAST"] = "till sista bokstaven";
Blockly.Msg["TEXT_GET_SUBSTRING_HELPURL"] = "https://github.com/google/blockly/wiki/Text#extracting-a-region-of-text";  // untranslated
Blockly.Msg["TEXT_GET_SUBSTRING_INPUT_IN_TEXT"] = "i texten";
Blockly.Msg["TEXT_GET_SUBSTRING_START_FIRST"] = "få textdel från första bokstaven";
Blockly.Msg["TEXT_GET_SUBSTRING_START_FROM_END"] = "få textdel från bokstav # från slutet";
Blockly.Msg["TEXT_GET_SUBSTRING_START_FROM_START"] = "få textdel från bokstav #";
Blockly.Msg["TEXT_GET_SUBSTRING_TAIL"] = "";  // untranslated
Blockly.Msg["TEXT_GET_SUBSTRING_TOOLTIP"] = "Ger tillbaka en viss del av texten.";
Blockly.Msg["TEXT_INDEXOF_HELPURL"] = "https://github.com/google/blockly/wiki/Text#finding-text";  // untranslated
Blockly.Msg["TEXT_INDEXOF_OPERATOR_FIRST"] = "hitta första förekomsten av texten";
Blockly.Msg["TEXT_INDEXOF_OPERATOR_LAST"] = "hitta sista förekomsten av texten";
Blockly.Msg["TEXT_INDEXOF_TITLE"] = "i texten %1 %2 %3";
Blockly.Msg["TEXT_INDEXOF_TOOLTIP"] = "Ger tillbaka indexet för den första/sista förekomsten av första texten i den andra texten.  Ger tillbaka %1 om texten inte hittas.";
Blockly.Msg["TEXT_ISEMPTY_HELPURL"] = "https://github.com/google/blockly/wiki/Text#checking-for-empty-text";  // untranslated
Blockly.Msg["TEXT_ISEMPTY_TITLE"] = "%1 är tom";
Blockly.Msg["TEXT_ISEMPTY_TOOLTIP"] = "Returnerar sant om den angivna texten är tom.";
Blockly.Msg["TEXT_JOIN_HELPURL"] = "https://github.com/google/blockly/wiki/Text#text-creation";  // untranslated
Blockly.Msg["TEXT_JOIN_TITLE_CREATEWITH"] = "skapa text med";
Blockly.Msg["TEXT_JOIN_TOOLTIP"] = "Skapa en textbit genom att sammanfoga ett valfritt antal föremål.";
Blockly.Msg["TEXT_LENGTH_HELPURL"] = "https://github.com/google/blockly/wiki/Text#text-modification";  // untranslated
Blockly.Msg["TEXT_LENGTH_TITLE"] = "längden på %1";
Blockly.Msg["TEXT_LENGTH_TOOLTIP"] = "Ger tillbaka antalet bokstäver (inklusive mellanslag) i den angivna texten.";
Blockly.Msg["TEXT_PRINT_HELPURL"] = "https://github.com/google/blockly/wiki/Text#printing-text";  // untranslated
Blockly.Msg["TEXT_PRINT_TITLE"] = "skriv %1";
Blockly.Msg["TEXT_PRINT_TOOLTIP"] = "Skriv den angivna texten, talet eller annat värde.";
Blockly.Msg["TEXT_PROMPT_HELPURL"] = "https://github.com/google/blockly/wiki/Text#getting-input-from-the-user";  // untranslated
Blockly.Msg["TEXT_PROMPT_TOOLTIP_NUMBER"] = "Fråga användaren efter ett tal.";
Blockly.Msg["TEXT_PROMPT_TOOLTIP_TEXT"] = "Fråga användaren efter lite text.";
Blockly.Msg["TEXT_PROMPT_TYPE_NUMBER"] = "fråga efter ett tal med meddelande";
Blockly.Msg["TEXT_PROMPT_TYPE_TEXT"] = "fråga efter text med meddelande";
Blockly.Msg["TEXT_REPLACE_HELPURL"] = "https://github.com/google/blockly/wiki/Text#replacing-substrings";
Blockly.Msg["TEXT_REPLACE_MESSAGE0"] = "ersätt %1 med %2 i %3";
Blockly.Msg["TEXT_REPLACE_TOOLTIP"] = "Ersätt alla förekomster av en text inom en annan text.";
Blockly.Msg["TEXT_REVERSE_HELPURL"] = "https://github.com/google/blockly/wiki/Text#reversing-text";
Blockly.Msg["TEXT_REVERSE_MESSAGE0"] = "vänd på %1";
Blockly.Msg["TEXT_REVERSE_TOOLTIP"] = "Vänder på teckenordningen i texten.";
Blockly.Msg["TEXT_TEXT_HELPURL"] = "https://sv.wikipedia.org/wiki/Str%C3%A4ng_%28data%29";
Blockly.Msg["TEXT_TEXT_TOOLTIP"] = "En bokstav, ord eller textrad.";
Blockly.Msg["TEXT_TRIM_HELPURL"] = "https://github.com/google/blockly/wiki/Text#trimming-removing-spaces";  // untranslated
Blockly.Msg["TEXT_TRIM_OPERATOR_BOTH"] = "ta bort mellanrum från båda sidorna av";
Blockly.Msg["TEXT_TRIM_OPERATOR_LEFT"] = "ta bort mellanrum från vänstra sidan av";
Blockly.Msg["TEXT_TRIM_OPERATOR_RIGHT"] = "ta bort mellanrum från högra sidan av";
Blockly.Msg["TEXT_TRIM_TOOLTIP"] = "Returnerar en kopia av texten med borttagna mellanrum från en eller båda ändar.";
Blockly.Msg["TODAY"] = "Idag";
Blockly.Msg["UNDO"] = "Ångra";
Blockly.Msg["UNNAMED_KEY"] = "namnlös";
Blockly.Msg["VARIABLES_DEFAULT_NAME"] = "föremål";
Blockly.Msg["VARIABLES_GET_CREATE_SET"] = "Skapa \"välj %1\"";
Blockly.Msg["VARIABLES_GET_HELPURL"] = "https://github.com/google/blockly/wiki/Variables#get";  // untranslated
Blockly.Msg["VARIABLES_GET_TOOLTIP"] = "Returnerar värdet av denna variabel.";
Blockly.Msg["VARIABLES_SET"] = "ange %1 till %2";
Blockly.Msg["VARIABLES_SET_CREATE_GET"] = "Skapa 'hämta %1'";
Blockly.Msg["VARIABLES_SET_HELPURL"] = "https://github.com/google/blockly/wiki/Variables#set";  // untranslated
Blockly.Msg["VARIABLES_SET_TOOLTIP"] = "Gör så att den här variabeln blir lika med inputen.";
Blockly.Msg["VARIABLE_ALREADY_EXISTS"] = "En variabel med namnet \"%1\" finns redan.";
Blockly.Msg["VARIABLE_ALREADY_EXISTS_FOR_ANOTHER_TYPE"] = "En variabel med namnet \"%1\" finns redan för en annan typ: \"%2\".";
Blockly.Msg["WORKSPACE_ARIA_LABEL"] = "Blocklys arbetsyta";
Blockly.Msg["WORKSPACE_COMMENT_DEFAULT_TEXT"] = "Säg någonting...";
Blockly.Msg["CONTROLS_FOREACH_INPUT_DO"] = Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"];
Blockly.Msg["CONTROLS_FOR_INPUT_DO"] = Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"];
Blockly.Msg["CONTROLS_IF_ELSEIF_TITLE_ELSEIF"] = Blockly.Msg["CONTROLS_IF_MSG_ELSEIF"];
Blockly.Msg["CONTROLS_IF_ELSE_TITLE_ELSE"] = Blockly.Msg["CONTROLS_IF_MSG_ELSE"];
Blockly.Msg["CONTROLS_IF_IF_TITLE_IF"] = Blockly.Msg["CONTROLS_IF_MSG_IF"];
Blockly.Msg["CONTROLS_IF_MSG_THEN"] = Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"];
Blockly.Msg["CONTROLS_WHILEUNTIL_INPUT_DO"] = Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"];
Blockly.Msg["LISTS_CREATE_WITH_ITEM_TITLE"] = Blockly.Msg["VARIABLES_DEFAULT_NAME"];
Blockly.Msg["LISTS_GET_INDEX_HELPURL"] = Blockly.Msg["LISTS_INDEX_OF_HELPURL"];
Blockly.Msg["LISTS_GET_INDEX_INPUT_IN_LIST"] = Blockly.Msg["LISTS_INLIST"];
Blockly.Msg["LISTS_GET_SUBLIST_INPUT_IN_LIST"] = Blockly.Msg["LISTS_INLIST"];
Blockly.Msg["LISTS_INDEX_OF_INPUT_IN_LIST"] = Blockly.Msg["LISTS_INLIST"];
Blockly.Msg["LISTS_SET_INDEX_INPUT_IN_LIST"] = Blockly.Msg["LISTS_INLIST"];
Blockly.Msg["MATH_CHANGE_TITLE_ITEM"] = Blockly.Msg["VARIABLES_DEFAULT_NAME"];
Blockly.Msg["PROCEDURES_DEFRETURN_COMMENT"] = Blockly.Msg["PROCEDURES_DEFNORETURN_COMMENT"];
Blockly.Msg["PROCEDURES_DEFRETURN_DO"] = Blockly.Msg["PROCEDURES_DEFNORETURN_DO"];
Blockly.Msg["PROCEDURES_DEFRETURN_PROCEDURE"] = Blockly.Msg["PROCEDURES_DEFNORETURN_PROCEDURE"];
Blockly.Msg["PROCEDURES_DEFRETURN_TITLE"] = Blockly.Msg["PROCEDURES_DEFNORETURN_TITLE"];
Blockly.Msg["TEXT_APPEND_VARIABLE"] = Blockly.Msg["VARIABLES_DEFAULT_NAME"];
Blockly.Msg["TEXT_CREATE_JOIN_ITEM_TITLE_ITEM"] = Blockly.Msg["VARIABLES_DEFAULT_NAME"];

Blockly.Msg["MATH_HUE"] = "230";
Blockly.Msg["LOOPS_HUE"] = "120";
Blockly.Msg["LISTS_HUE"] = "260";
Blockly.Msg["LOGIC_HUE"] = "210";
Blockly.Msg["VARIABLES_HUE"] = "330";
Blockly.Msg["TEXTS_HUE"] = "160";
Blockly.Msg["PROCEDURES_HUE"] = "290";
Blockly.Msg["COLOUR_HUE"] = "20";
Blockly.Msg["VARIABLES_DYNAMIC_HUE"] = "310";