// This file was automatically generated.  Do not modify.

'use strict';

Blockly.Msg["ADD_COMMENT"] = "ಟಿಪ್ಪಣಿ ಸೇರ್ಸಲೆ";
Blockly.Msg["CANNOT_DELETE_VARIABLE_PROCEDURE"] = "Can't delete the variable '%1' because it's part of the definition of the function '%2'";  // untranslated
Blockly.Msg["CHANGE_VALUE_TITLE"] = "ಮೌಲ್ಯೊನು ಬದಲ್ ಮಲ್ಪು";
Blockly.Msg["CLEAN_UP"] = "ಬ್ಲಾಕ್‍ಲೆನ್ ಸ್ವೊಚ್ಚೊ ಮಲ್ಪುಲೆ";
Blockly.Msg["COLLAPSED_WARNINGS_WARNING"] = "Collapsed blocks contain warnings.";  // untranslated
Blockly.Msg["COLLAPSE_ALL"] = "ಮಾತಾ ತಡೆಕ್ಲೆನ ಮಾಹಿತಿನ್ ಎಲ್ಯ ಮಲ್ಪು";
Blockly.Msg["COLLAPSE_BLOCK"] = "ಎಲ್ಯೆ ಮಲ್ತ್‌ದ್ ತಡೆಲೆ";
Blockly.Msg["COLOUR_BLEND_COLOUR1"] = "ಬಣ್ಣೊ ೧(ಒಂಜಿ)";
Blockly.Msg["COLOUR_BLEND_COLOUR2"] = "ಬಣ್ಣೊ ೨(ರಡ್ಡ್)";
Blockly.Msg["COLOUR_BLEND_HELPURL"] = "https://meyerweb.com/eric/tools/color-blend/#:::rgbp";  // untranslated
Blockly.Msg["COLOUR_BLEND_RATIO"] = "ಅನುಪಾತೊ";
Blockly.Msg["COLOUR_BLEND_TITLE"] = "ಬೆರಕ್ಕೆ ಮಲ್ಪು";
Blockly.Msg["COLOUR_BLEND_TOOLTIP"] = "ಕೊರಿನ ಅನುಪಾತೊಡು (0.0- 1.0) ರಡ್ಡ್ ಬಣ್ಣೊಲೆನ್ ಬೆರಕೆ ಮಲ್ಪುಂಡು.";
Blockly.Msg["COLOUR_PICKER_HELPURL"] = "https://en.wikipedia.org/wiki/ಬಣ್ಣೊ";
Blockly.Msg["COLOUR_PICKER_TOOLTIP"] = "ಬಣ್ಣೊ ಪಟೊಡ್ದು ಒಂಜಿ ಬಣ್ಣೊನು ಆಯ್ಕೆ ಮಲ್ಪುಲೆ.";
Blockly.Msg["COLOUR_RANDOM_HELPURL"] = "http://randomcolour.com";  // untranslated
Blockly.Msg["COLOUR_RANDOM_TITLE"] = "ಒವ್ವೇ ಒಂಜಿ ಬಣ್ಣೊ";
Blockly.Msg["COLOUR_RANDOM_TOOLTIP"] = "ಒವ್ವಾಂಡಲ ಒಂಜಿ ಬಣ್ಣೊನು ಆಯ್ಕೆ ಮಲ್ಪುಲೆ";
Blockly.Msg["COLOUR_RGB_BLUE"] = "ನೀಲಿ";
Blockly.Msg["COLOUR_RGB_GREEN"] = "ಪಚ್ಚೆ";
Blockly.Msg["COLOUR_RGB_HELPURL"] = "https://www.december.com/html/spec/colorpercompact.html";  // untranslated
Blockly.Msg["COLOUR_RGB_RED"] = "ಕೆಂಪು";
Blockly.Msg["COLOUR_RGB_TITLE"] = "ಬಣ್ಣೊದ";
Blockly.Msg["COLOUR_RGB_TOOLTIP"] = "ತೊಜಪಾಯಿನ ಪ್ರಮಾಣೊದ ಕೆಂಪು, ಪಚ್ಚೆ ಬೊಕ್ಕ ನೀಲಿ ಬಣ್ಣೊಡ್ದು ಒಂಜಿ ಬಣ್ಣೊನು ಉಂಡು ಮಲ್ಪುಲೆ. ಮಾತಾ ಮೌಲ್ಯೊಲು 0 ಬುಕ್ಕೊ 100 ತ ನಡುಟೆ ಇಪ್ಪೊಡು.";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_HELPURL"] = "https://github.com/google/blockly/wiki/Loops#loop-termination-blocks";  // untranslated
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_OPERATOR_BREAK"] = "ಲೂಪ್ ಕಡಿಯುನಿ";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_OPERATOR_CONTINUE"] = "ಬೊಕ್ಕದ ಲೂಪ್ ಪುನರಾವರ್ತನೆದೊಟ್ಟುಗು ದುಂಬರಿಲೆ";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_TOOLTIP_BREAK"] = "ಇತ್ತಿನ ಲೂಪ್‍ಡ್ದ್ ಪದಿಯಿ ಬಲೆ";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_TOOLTIP_CONTINUE"] = "ಈ ಲೂಪುನು ಅರ್ದೊಡೆ ಬುಡುದ್ ಬೊಕ್ಕ ನನತ್ತ ಪುನರಾವರ್ತನೆಗ್ ದುಂಬರಿಲೆ";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_WARNING"] = "ಎಚ್ಚರೊ: ಈ ತಡೆನ್ ಕಾಲಿ ಒಂಜಿ ಲೂಪುದುಲಯಿ ಮಾತ್ರ ಗಳಸೊಲಿ.";
Blockly.Msg["CONTROLS_FOREACH_HELPURL"] = "https://github.com/google/blockly/wiki/Loops#for-each";  // untranslated
Blockly.Msg["CONTROLS_FOREACH_TITLE"] = "%2 ಪಟ್ಟಿಡ್ ಪ್ರತಿ ಒಂಜಿ ವಿಸಯ %1 ಗ್";
Blockly.Msg["CONTROLS_FOREACH_TOOLTIP"] = "ಒಂಜಿ ಪಟ್ಟಿಡ್ ಪ್ರತಿ ವಿಸಯೊಗು, '%1' ವ್ಯತ್ಯಾಯೊನು ವಿಸಯೊಗು ಜೋಡಾಲೆ, ಬೊಕ್ಕ ಕೆಲವು ಪಾತೆರೊಲೆನ್ ಮಲ್ಪುಲೆ.";
Blockly.Msg["CONTROLS_FOR_HELPURL"] = "https://github.com/google/blockly/wiki/Loops#count-with";  // untranslated
Blockly.Msg["CONTROLS_FOR_TITLE"] = "%2 ಡ್ದ್ %3 ಮುಟ %4 ಸರ್ತಿ %1 ದ ಒಟ್ಟುಗು ಗೆನ್ಪು";
Blockly.Msg["CONTROLS_FOR_TOOLTIP"] = "ನಿರ್ದಿಸ್ಟೊ ಮದ್ಯಂತರೊದ ಮೂಲಕೊ ದೆತೊಂದು '%1' ವ್ಯತ್ಯಯೊಡ್ ಸುರುತ್ತ ಅಂಕೆಡ್ದ್ ಕಡೆತ್ತ ಅಂಕೆ ಮುಟ್ಟದ ಮೌಲ್ಯೊನು ದೆತ್ತೊನಾವ್ ಬೊಕ್ಕ ನಿಗಂಟ್ ಮಲ್ತಿನ ತಡೆಕ್ಲೆನ್ ಮಲ್ಪು";
Blockly.Msg["CONTROLS_IF_ELSEIF_TOOLTIP"] = "'ಒಂಜಿ ವೇಲೆ' ತಡೆಕ್ಕ್ ಒಂಜಿ ಶರ್ತನ್ ಸೇರಾವ್";
Blockly.Msg["CONTROLS_IF_ELSE_TOOLTIP"] = "'ಒಂಜಿ ವೇಲೆ' ತಡೆಕ್ಕ್ ಒಂಜಿ ಕಡೆತ್ತ ಮಾತೆನ್ಲಾ-ಪತ್ತ್ (catch-all) ಶರ್ತನ್ ಸೇರಾವ್";
Blockly.Msg["CONTROLS_IF_HELPURL"] = "https://github.com/google/blockly/wiki/IfElse";  // untranslated
Blockly.Msg["CONTROLS_IF_IF_TOOLTIP"] = "ಸೇರಾವ್, ದೆತ್ತ್‌ ಬುಡು, ಅತ್ತಂಡ ಈ 'ಒಂಜಿ ವೇಲೆ' ತಡೆನ್ ಕುಡ ಸಂರಚಣೆ ಮಲ್ಪೆರೆ ವಿಭಾಗೊಲೆನ್ ಕುಡ ಒತ್ತರೆ ಮಲ್ಪುಲೆ.";
Blockly.Msg["CONTROLS_IF_MSG_ELSE"] = "ಅತ್ತಂಡ";
Blockly.Msg["CONTROLS_IF_MSG_ELSEIF"] = "ಅತ್ತಂಡ";
Blockly.Msg["CONTROLS_IF_MSG_IF"] = "ಒಂಜಿ ವೇಲೆ";
Blockly.Msg["CONTROLS_IF_TOOLTIP_1"] = "ಮೌಲ್ಯ ನಿಜ ಆದಿತ್ತ್ಂಡ ಕೆಲವು ಪಾತೆರೊಲೆನ್ ಮಲ್ಪು";
Blockly.Msg["CONTROLS_IF_TOOLTIP_2"] = "ಮೌಲ್ಯ ನಿಜವಾದಿತ್ತ್‌ಂಡ, ಪಾತೆರೊಲೆನ ಸುರುತ್ತ ತಡೆ ಮಲ್ಪು. ಇಜ್ಜಿಂಡ ಪಾತೆರೊಲೆನ ರಡ್ಡನೆ ತಡೆ ಮಲ್ಪು.";
Blockly.Msg["CONTROLS_IF_TOOLTIP_3"] = "ಸುರುತ್ತ ಮೌಲ್ಯ ನಿಜವಾದಿತ್ತ್‌ಂಡ, ಪಾತೆರೊಲೆನ ಸುರುತ್ತ ತಡೆ ಮಲ್ಪು. ಇಜ್ಜಿಂಡ, ರಡ್ಡನೆ ಮೌಲ್ಯ ನಿಜವಾದಿತ್ತ್ಂಡ, ಪಾತೆರೊಲೆನ ರಡ್ಡನೆ ತಡೆ ಮಲ್ಪು.";
Blockly.Msg["CONTROLS_IF_TOOLTIP_4"] = "ಸುರುತ್ತ ಮೌಲ್ಯೊ ನಿಜವಾದಿತ್ತ್‌ಂಡ, ಪಾತೆರೊಲೆನ ಸುರುತ್ತ ತಡೆ ಮಲ್ಪು. ಇಜ್ಜಿಂಡ, ರಡ್ಡನೆದ ಮೌಲ್ಯ ನಿಜವಾದಿತ್ತ್ಂಡ, ಪಾತೆರೊಲೆನ ರಡ್ಡನೆ ತಡೆ ಮಲ್ಪು. ಒಂಜೇಲೆ ಒವ್ವೇ ಮೌಲ್ಯ ನಿಜವಾದಿತ್ತಿಜಿಂಡ, ಪಾತೆರೊಲೆನ ಕಡೆತ್ತ ತಡೆ ಮಲ್ಪು.";
Blockly.Msg["CONTROLS_REPEAT_HELPURL"] = "https://en.wikipedia.org/wiki/For_loop";
Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"] = "ಮಲ್ಪುಲೆ";
Blockly.Msg["CONTROLS_REPEAT_TITLE"] = " %1 ಸರ್ತಿ ಕೂಡೊರ ಮಲ್ಪು";
Blockly.Msg["CONTROLS_REPEAT_TOOLTIP"] = "ಕೆಲವು ಪಾತೆರೊಲೆನ್ ಮಸ್ತ್ ಸರ್ತಿ ಮಲ್ಪು";
Blockly.Msg["CONTROLS_WHILEUNTIL_HELPURL"] = "https://github.com/google/blockly/wiki/Loops#repeat";  // untranslated
Blockly.Msg["CONTROLS_WHILEUNTIL_OPERATOR_UNTIL"] = "ಉಂದು ನಿಜ ಆಪಿಲೆಕೊ ಕುಡೊರ ಮಲ್ಪು:";
Blockly.Msg["CONTROLS_WHILEUNTIL_OPERATOR_WHILE"] = "ಉಂದು ನಿಜ ಆಂಡ ಕುಡೊರ ಮಲ್ಪು:";
Blockly.Msg["CONTROLS_WHILEUNTIL_TOOLTIP_UNTIL"] = "ಮೌಲ್ಯ ತಪ್ಪು ಆದಿತ್ತ್ಂಡ ಕೆಲವು ಪಾತೆರೊಲೆನ್ ಮಲ್ಪು";
Blockly.Msg["CONTROLS_WHILEUNTIL_TOOLTIP_WHILE"] = "ಮೌಲ್ಯ ನಿಜ ಆದಿತ್ತ್ಂಡ ಕೆಲವು ಪಾತೆರೊಲೆನ್ ಮಲ್ಪು";
Blockly.Msg["DELETE_ALL_BLOCKS"] = "ಮಾತ %1 ಬ್ಲಾಕ್‍ಲೆನ್ ದೆತ್ತ್‌ದ್ ಬುಡೊಡೆ?";
Blockly.Msg["DELETE_BLOCK"] = "ಬ್ಲಾಕ್‍ನ್ ಮಾಜಾವು";
Blockly.Msg["DELETE_VARIABLE"] = "'%1' ವ್ಯತ್ಯಯೊನು ಮಾಜಾಲೆ";
Blockly.Msg["DELETE_VARIABLE_CONFIRMATION"] = "'%2' ವ್ಯತ್ಯಯೊದ %1 ಉಪಯೋಗೊಲೆನ್ ಮಾಜಾವೊಡೆ?";
Blockly.Msg["DELETE_X_BLOCKS"] = "%1 ಬ್ಲಾಕ್‍ಲೆನ್ ಮಾಜಾವು";
Blockly.Msg["DIALOG_CANCEL"] = "ಉಂತಾಲೆ";
Blockly.Msg["DIALOG_OK"] = "ಅವು";
Blockly.Msg["DISABLE_BLOCK"] = "ಬ್ಲಾಕ್‍ನ್ ದೆತ್ತ್‌ಪಾಡ್";
Blockly.Msg["DUPLICATE_BLOCK"] = "ನಕಲ್";
Blockly.Msg["DUPLICATE_COMMENT"] = "Duplicate Comment";  // untranslated
Blockly.Msg["ENABLE_BLOCK"] = "ತಡೆನ್ ಸಕ್ರಿಯೊ ಮಲ್ಪು";
Blockly.Msg["EXPAND_ALL"] = "ಮಾತಾ ತಡೆಕ್ಲೆನ ಮಾಹಿತಿನ್ ಪರಡಾವು";
Blockly.Msg["EXPAND_BLOCK"] = "ಬ್ಲಾಕ್‍ದ ಮಾಹಿತಿನ್ ಪರಡಾವು";
Blockly.Msg["EXTERNAL_INPUTS"] = "ಪಿದಯಿದ ಪರಿಪು";
Blockly.Msg["HELP"] = "ಸಹಾಯೊ";
Blockly.Msg["INLINE_INPUTS"] = "ಉಳಸಾಲ್‍ದ ಉಳಪರಿಪು";
Blockly.Msg["LISTS_CREATE_EMPTY_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#create-empty-list";  // untranslated
Blockly.Msg["LISTS_CREATE_EMPTY_TITLE"] = "ಕಾಲಿ ಪಟ್ಟಿನ್ ಉಂಡುಮಲ್ಪುಲೆ";
Blockly.Msg["LISTS_CREATE_EMPTY_TOOLTIP"] = "ಒಂಜಿ ಪಟ್ಟಿ, ೦ ಉದ್ದೊದ, ಒವ್ವೇ ಮಾಹಿತಿ ದಾಂತಿನ ದಾಖಲೆ ಪಿರಕೊರು.";
Blockly.Msg["LISTS_CREATE_WITH_CONTAINER_TITLE_ADD"] = "ಪಟ್ಟಿ";
Blockly.Msg["LISTS_CREATE_WITH_CONTAINER_TOOLTIP"] = "ಈ ಪಟ್ಟಿ ತಡೆನ್ ಕುಡ ಸಂರಚನೆ ಮಲ್ಪೆರೆ ಸೇರಾಲೆ, ದೆತ್ತ್ ಬುಡುಲೆ, ಅತ್ತಂಡ ವಿಬಾಗೊಲೆನ್ ಕುಡ ಒತ್ತರೆ ಮಲ್ಪುಲೆ.";
Blockly.Msg["LISTS_CREATE_WITH_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#create-list-with";  // untranslated
Blockly.Msg["LISTS_CREATE_WITH_INPUT_WITH"] = "ಒಟ್ಟುಗು ಪಟ್ಟಿನ್ ಉಂಡುಮಲ್ಪುಲೆ";
Blockly.Msg["LISTS_CREATE_WITH_ITEM_TOOLTIP"] = "ಪಟ್ಟಿಗ್ ಒಂಜಿ ವಿಸಯೊನು ಸೇರಾಲೆ.";
Blockly.Msg["LISTS_CREATE_WITH_TOOLTIP"] = "ಏತೇ ವಿಸಯೊಲುಪ್ಪುನ ಒಂಜಿ ಪಟ್ಟಿನ್ ಉಂಡುಮಲ್ಪುಲೆ";
Blockly.Msg["LISTS_GET_INDEX_FIRST"] = "ಸುರುತ";
Blockly.Msg["LISTS_GET_INDEX_FROM_END"] = "ಅಕೇರಿಡ್ದ್ #";
Blockly.Msg["LISTS_GET_INDEX_FROM_START"] = "#";  // untranslated
Blockly.Msg["LISTS_GET_INDEX_GET"] = "ದೆತೊನು";
Blockly.Msg["LISTS_GET_INDEX_GET_REMOVE"] = "ದೆತ್ತೊನು ಬೊಕ್ಕ ದೆತ್ತ್ ಬುಡು";
Blockly.Msg["LISTS_GET_INDEX_LAST"] = "ಕಡೆತ";
Blockly.Msg["LISTS_GET_INDEX_RANDOM"] = "ಒವ್ವಾಂಡಲ";
Blockly.Msg["LISTS_GET_INDEX_REMOVE"] = "ದೆಪ್ಪುಲೆ";
Blockly.Msg["LISTS_GET_INDEX_TAIL"] = "";  // untranslated
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_FIRST"] = "ಒಂಜಿ ಪಟ್ಟಿಡ್ ಸುರುತ್ತ ವಿಸಯೊನು ಪಿರಕೊರ್ಪುಂಡು.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_FROM"] = "ಒಂಜಿ ಪಟ್ಟಿಡ್ ನಿರ್ದಿಷ್ಟ ಸ್ಥಿತಿಡ್ ವಿಸಯೊನು ಪಿರಕೊರ್ಪುಂಡು.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_LAST"] = "ಒಂಜಿ ಪಟ್ಟಿಡ್ ಅಕೇರಿದ ವಿಸಯೊನು ಪಿರಕೊರ್ಪುಂಡು.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_RANDOM"] = "ಒಂಜಿ ಪಟ್ಟಿಡ್ ಒವ್ವಾಂಡಲ ಒಂಜಿ ವಿಸಯೊನು ಪಿರಕೊರ್ಪುಂಡು.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_FIRST"] = "ಒಂಜಿ ಪಟ್ಟಿಡ್ ಸುರುತ್ತ ವಿಸಯೊನು ಪಿರಕೊರ್ಪುಂಡು ಬೊಕ್ಕ ದೆತ್ತ್ ಬುಡ್ಪುಂಡು.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_FROM"] = "ಒಂಜಿ ಪಟ್ಟಿಡ್ ನಿರ್ದಿಷ್ಟ ಸ್ಥಿತಿಡ್ ವಿಸಯೊನು ಪಿರಕೊರ್ಪುಂಡು ಬೊಕ್ಕ ದೆತ್ತ್ ಬುಡ್ಪುಂಡು.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_LAST"] = "ಒಂಜಿ ಪಟ್ಟಿಡ್ ಅಕೇರಿದ ವಿಸಯೊನು ಪಿರಕೊರ್ಪುಂಡು ಬೊಕ್ಕ ದೆತ್ತ್ ಬುಡ್ಪುಂಡು.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_RANDOM"] = "ಒಂಜಿ ಪಟ್ಟಿಡ್ ಒವ್ವಾಂಡಲ ಒಂಜಿ ವಿಸಯೊನು ಪಿರಕೊರ್ಪುಂಡು ಬೊಕ್ಕ ದೆತ್ತ್ ಬುಡ್ಪುಂಡು.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_REMOVE_FIRST"] = "ಒಂಜಿ ಪಟ್ಟಿಡ್ ಸುರುತ್ತ ವಿಸಯೊನು ದೆತ್ತ್ ಬುಡ್ಪುಂಡು.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_REMOVE_FROM"] = "ಒಂಜಿ ಪಟ್ಟಿಡ್ ನಿರ್ದಿಷ್ಟ ಸ್ಥಿತಿಡ್ ವಿಸಯೊನು ದೆತ್ತ್ ಬುಡ್ಪುಂಡು.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_REMOVE_LAST"] = "ಒಂಜಿ ಪಟ್ಟಿಡ್ ಅಕೇರಿದ ವಿಸಯೊನು ದೆತ್ತ್ ಬುಡ್ಪುಂಡು.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_REMOVE_RANDOM"] = "ಒಂಜಿ ಪಟ್ಟಿಡ್ ಒವ್ವಾಂಡಲ ಒಂಜಿ ವಿಸಯೊನು ದೆತ್ತ್ ಬುಡ್ಪುಂಡು.";
Blockly.Msg["LISTS_GET_SUBLIST_END_FROM_END"] = "ಅಕೇರಿಡ್ದ್ # ಗ್";
Blockly.Msg["LISTS_GET_SUBLIST_END_FROM_START"] = "# ಗ್";
Blockly.Msg["LISTS_GET_SUBLIST_END_LAST"] = "ಅಕೇರಿಗ್";
Blockly.Msg["LISTS_GET_SUBLIST_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#getting-a-sublist";  // untranslated
Blockly.Msg["LISTS_GET_SUBLIST_START_FIRST"] = "ಸುರುಡ್ದು ಉಪ-ಪಟ್ಟಿನ್ ದೆತ್ತೊನು";
Blockly.Msg["LISTS_GET_SUBLIST_START_FROM_END"] = "ಅಕೇರಿಡ್ದ್ # ಡ್ದ್ ಉಪ-ಪಟ್ಟಿನ್ ದೆತ್ತೊನು";
Blockly.Msg["LISTS_GET_SUBLIST_START_FROM_START"] = "# ಡ್ದ್ ಉಪ-ಪಟ್ಟಿನ್ ದೆತ್ತೊನು";
Blockly.Msg["LISTS_GET_SUBLIST_TAIL"] = "";  // untranslated
Blockly.Msg["LISTS_GET_SUBLIST_TOOLTIP"] = "ಪಟ್ಯೊದ ನಿರ್ದಿಷ್ಟ ಬಾಗೊದ ಪ್ರತಿನ್ ಉಂಡುಮಲ್ಪುಂಡು.";
Blockly.Msg["LISTS_INDEX_FROM_END_TOOLTIP"] = "%1 ಅಕೇರಿತ ವಿಸಯೊ";
Blockly.Msg["LISTS_INDEX_FROM_START_TOOLTIP"] = "%1 ಸುರುತ ವಿಸಯೊ";
Blockly.Msg["LISTS_INDEX_OF_FIRST"] = "ವಿಸಯೊ ಸುರುಕ್ಕು ಬತ್ತಿನೇನ್ ನಾಡ್";
Blockly.Msg["LISTS_INDEX_OF_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#getting-items-from-a-list";  // untranslated
Blockly.Msg["LISTS_INDEX_OF_LAST"] = "ವಿಸಯೊ ಅಕೇರಿಗ್ ಬತ್ತಿನೇನ್ ನಾಡ್";
Blockly.Msg["LISTS_INDEX_OF_TOOLTIP"] = "ಪಟ್ಟಿಡುಪ್ಪುನ ವಿಸಯೊ ಸುರುಕ್ಕು/ಅಕೇರಿಗ್ ಬತ್ತಿನೆತ್ತ ಸೂಚಿನ್ ಪಿರಕೊರ್ಪುಂಡು. ವಿಸಯೊ ತಿಕ್ಕಿಜ್ಜಾಂಡ %1 ನ್ ಪಿರಕೊರ್ಪುಂಡು.";
Blockly.Msg["LISTS_INLIST"] = "ಪಟ್ಟಿಡ್";
Blockly.Msg["LISTS_ISEMPTY_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#is-empty";  // untranslated
Blockly.Msg["LISTS_ISEMPTY_TITLE"] = "%1 ಕಾಲಿ";
Blockly.Msg["LISTS_ISEMPTY_TOOLTIP"] = "ಪಟ್ಯೊ ಖಾಲಿ ಇತ್ತ್ಂಡ 'ನಿಜ'ನ್ ಪಿರಕೊರು.";
Blockly.Msg["LISTS_LENGTH_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#length-of";  // untranslated
Blockly.Msg["LISTS_LENGTH_TITLE"] = "%1 ಉದ್ದೊ";
Blockly.Msg["LISTS_LENGTH_TOOLTIP"] = "ಪಟ್ಟಿದ ಉದ್ದೊನು ಪಿರಕೊರು.";
Blockly.Msg["LISTS_REPEAT_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#create-list-with";  // untranslated
Blockly.Msg["LISTS_REPEAT_TITLE"] = "%1 ವಿಸಯೊ %2 ಸರ್ತಿ ಪುನರಾವರ್ತನೆ ಆದುಪ್ಪುನ ಪಟ್ಟಿನ್ ಉಂಡುಮಲ್ಪುಲೆ.";
Blockly.Msg["LISTS_REPEAT_TOOLTIP"] = "ಕೊರಿನ ಮೌಲ್ಯೊ ಒಂಜಿ ನಿರ್ದಿಷ್ಟ ಸಂಕ್ಯೆದಾತ್ ಸರ್ತಿ ಪುನರಾವರ್ತನೆ ಆದುಪ್ಪುನ ಒಂಜಿ ಪಟ್ಟಿನ್ ಉಂಡುಮಲ್ಪುಲೆ.";
Blockly.Msg["LISTS_REVERSE_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#reversing-a-list";  // untranslated
Blockly.Msg["LISTS_REVERSE_MESSAGE0"] = "reverse %1";  // untranslated
Blockly.Msg["LISTS_REVERSE_TOOLTIP"] = "Reverse a copy of a list.";  // untranslated
Blockly.Msg["LISTS_SET_INDEX_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#in-list--set";  // untranslated
Blockly.Msg["LISTS_SET_INDEX_INPUT_TO"] = "ಲೆಕ";
Blockly.Msg["LISTS_SET_INDEX_INSERT"] = "ಸೇರಾವ್";
Blockly.Msg["LISTS_SET_INDEX_SET"] = "ಸೆಟ್ ಮಲ್ಪು";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_INSERT_FIRST"] = "ಒಂಜಿ ಪಟ್ಟಿದ ಸುರುಕ್ಕು ವಿಸಯೊನು ಸೇರಾವುಂಡು.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_INSERT_FROM"] = "ಒಂಜಿ ಪಟ್ಟಿಡ್ ನಿರ್ದಿಷ್ಟ ಸ್ಥಿತಿಡ್ ವಿಸಯೊನು ಸೇರಾವುಂಡು.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_INSERT_LAST"] = "ಒಂಜಿ ಪಟ್ಟಿದ ಅಕೇರಿಗ್ ವಿಸಯೊನು ಸೇರಾವುಂಡು.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_INSERT_RANDOM"] = "ಒಂಜಿ ಪಟ್ಟಿಡ್ ಓಲಾಂಡಲ ವಿಸಯೊನು ಸೇರಾವುಂಡು.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_SET_FIRST"] = "ಒಂಜಿ ಪಟ್ಟಿಡ್ ಸುರುತ್ತ ವಿಸಯೊನು ಸೆಟ್ ಮಲ್ಪುಂಡು.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_SET_FROM"] = "ಒಂಜಿ ಪಟ್ಟಿಡ್ ನಿರ್ದಿಷ್ಟ ಸ್ಥಿತಿಡ್ ವಿಸಯೊನು ಸೆಟ್ ಮಲ್ಪುಂಡು.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_SET_LAST"] = "ಒಂಜಿ ಪಟ್ಟಿಡ್ ಅಕೇರಿದ ವಿಸಯೊನು ಸೆಟ್ ಮಲ್ಪುಂಡು.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_SET_RANDOM"] = "ಒಂಜಿ ಪಟ್ಟಿಡ್ ಒವ್ವಾಂಡಲ ಒಂಜಿ ವಿಸಯೊನು ಸೆಟ್ ಮಲ್ಪುಂಡು.";
Blockly.Msg["LISTS_SORT_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#sorting-a-list";
Blockly.Msg["LISTS_SORT_ORDER_ASCENDING"] = "ಏರುನು";
Blockly.Msg["LISTS_SORT_ORDER_DESCENDING"] = "ಜಪ್ಪುನು";
Blockly.Msg["LISTS_SORT_TITLE"] = "%1 %2 %3 ಇಂಗಡಿಪು";
Blockly.Msg["LISTS_SORT_TOOLTIP"] = "ಒಂಜಿ ಪಟ್ಟಿದ ಒಂಜಿ ಪ್ರತಿನ್ ಇಂಗಡಿಪು";
Blockly.Msg["LISTS_SORT_TYPE_IGNORECASE"] = "ಅಕ್ಷರೊ, ನಮೂನೆ (case) ಅಲಕ್ಷ್ಯೊ ಮಲ್ಪುಲೆ";
Blockly.Msg["LISTS_SORT_TYPE_NUMERIC"] = "ಸಂಖ್ಯೆ";
Blockly.Msg["LISTS_SORT_TYPE_TEXT"] = "ಅಕ್ಷರೊ";
Blockly.Msg["LISTS_SPLIT_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#splitting-strings-and-joining-lists";  // untranslated
Blockly.Msg["LISTS_SPLIT_LIST_FROM_TEXT"] = "ಪಟ್ಯೊಲೆ ಪಟ್ಟಿನ್ ತಯಾರ್ ಮಲ್ಪುಲೆ";
Blockly.Msg["LISTS_SPLIT_TEXT_FROM_LIST"] = "ಪಟ್ಟಿದ ಪಟ್ಯೊನು ತಯಾರ್ ಮಲ್ಪುಲೆ";
Blockly.Msg["LISTS_SPLIT_TOOLTIP_JOIN"] = "ಪಟ್ಯೊಲೆನ ಒಂಜಿ ಪಟ್ಟಿನ್ ಮಿತಿಸೂಚಕೊದ ಮೂಲಕೊ ಬೇತೆ ಮಲ್ತ್‌ದ್ ಒಂಜಿ ಪಟ್ಯೊಗು ಸೇರಾಲೆ.";
Blockly.Msg["LISTS_SPLIT_TOOLTIP_SPLIT"] = "ಪಟ್ಯೊಲೆನ್ ಪ್ರತಿ ಮಿತಿಸೂಚಕೊಡು ತುಂಡು ಮಲ್ತ್‌ದ್ ಪಟ್ಯೊಲೆನ ಒಂಜಿ ಪಟ್ಟಿಗ್ ಬಾಗೊ ಮಲ್ಪುಲೆ.";
Blockly.Msg["LISTS_SPLIT_WITH_DELIMITER"] = "ಮಿತಿಸೂಚಕೊದ ಒಟ್ಟುಗು";
Blockly.Msg["LOGIC_BOOLEAN_FALSE"] = "ಸುಲ್ಲು";
Blockly.Msg["LOGIC_BOOLEAN_HELPURL"] = "https://github.com/google/blockly/wiki/Logic#values";  // untranslated
Blockly.Msg["LOGIC_BOOLEAN_TOOLTIP"] = "ಒಂಜೆ ನಿಜ ಅತ್ತಂಡ ಸುಲ್ಲುನ್ ಪಿರಕೊರು";
Blockly.Msg["LOGIC_BOOLEAN_TRUE"] = "ಸತ್ಯೊ";
Blockly.Msg["LOGIC_COMPARE_HELPURL"] = "https://en.wikipedia.org/wiki/Inequality_(mathematics)";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_EQ"] = "ರಡ್ದ್ ಇನ್‌ಪುಟ್‌ಲಾ ಸಮ ಇತ್ತ್ಂಡ 'ನಿಜ'ನ್ ಪಿರಕೊರು";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_GT"] = "ಸುರುತ್ತ ಇನ್‌ಪುಟ್ ರಡ್ಡನೆ ಇನ್‌ಪುಟ್‌ಡ್ದ್ ಮಲ್ಲ ಆದಿತ್ತ್ಂಡ, 'ನಿಜ'ನ್ ಪಿರಕೊರು";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_GTE"] = "ಸುರುತ್ತ ಇನ್‌ಪುಟ್ ರಡ್ಡನೆ ಇನ್‌ಪುಟ್‌ಡ್ದ್ ಮಲ್ಲ ಅತ್ತಂಡ ಅಯಿಕ್ಕ್ ಸಮ ಆದಿತ್ತ್ಂಡ, 'ನಿಜ'ನ್ ಪಿರಕೊರು";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_LT"] = "ಸುರುತ್ತ ಇನ್‌ಪುಟ್ ರಡ್ಡನೆ ಇನ್‌ಪುಟ್‌ಡ್ದ್ ಎಲ್ಯ ಆದಿತ್ತ್ಂಡ, 'ನಿಜ'ನ್ ಪಿರಕೊರು";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_LTE"] = "ಸುರುತ್ತ ಇನ್‌ಪುಟ್ ರಡ್ಡನೆ ಇನ್‌ಪುಟ್‌ಡ್ದ್ ಎಲ್ಯ ಅತ್ತಂಡ ಅಯಿಕ್ಕ್ ಸಮ ಆದಿತ್ತ್ಂಡ, 'ನಿಜ'ನ್ ಪಿರಕೊರು";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_NEQ"] = "ರಡ್ದ್ ಇನ್‌ಪುಟ್‌ಲಾ ಸಮ ಅತ್ತಾಂಡ 'ನಿಜ'ನ್ ಪಿರಕೊರು";
Blockly.Msg["LOGIC_NEGATE_HELPURL"] = "https://github.com/google/blockly/wiki/Logic#not";  // untranslated
Blockly.Msg["LOGIC_NEGATE_TITLE"] = "%1 ಅತ್ತ್";
Blockly.Msg["LOGIC_NEGATE_TOOLTIP"] = "ಇನ್‌ಪುಟ್ ಸುಲ್ಲಾದಿತ್ತ್ಂಡ, 'ನಿಜ'ನ್ ಪಿರಕೊರು. ಇನ್‌ಪುಟ್ ನಿಜ ಆದಿತ್ತ್ಂಡ, 'ಸುಲ್ಲು'ನ್ ಪಿರಕೊರು.";
Blockly.Msg["LOGIC_NULL"] = "ಸೊನ್ನೆ";
Blockly.Msg["LOGIC_NULL_HELPURL"] = "https://en.wikipedia.org/wiki/Nullable_type";  // untranslated
Blockly.Msg["LOGIC_NULL_TOOLTIP"] = "ಸೊನ್ನೆನ್ ಪಿರಕೊರ್ಪುಂಡು";
Blockly.Msg["LOGIC_OPERATION_AND"] = "ಬುಕ್ಕೊ";
Blockly.Msg["LOGIC_OPERATION_HELPURL"] = "https://github.com/google/blockly/wiki/Logic#logical-operations";  // untranslated
Blockly.Msg["LOGIC_OPERATION_OR"] = "ಅತ್ತಂಡ";
Blockly.Msg["LOGIC_OPERATION_TOOLTIP_AND"] = "ರಡ್ಡ್‌ಲಾ ಇನ್‌ಪುಟ್ ನಿಜ ಆದಿತ್ತ್ಂಡ, 'ನಿಜ'ನ್ ಪಿರಕೊರು";
Blockly.Msg["LOGIC_OPERATION_TOOLTIP_OR"] = "ಒವ್ವಾಂಡಲ ಒಂಜಿ ಇನ್‌ಪುಟ್ ನಿಜ ಆಂಡಲಾ, 'ನಿಜ'ನ್ ಪಿರಕೊರು.";
Blockly.Msg["LOGIC_TERNARY_CONDITION"] = "ಪರೀಕ್ಷೆ";
Blockly.Msg["LOGIC_TERNARY_HELPURL"] = "https://en.wikipedia.org/wiki/%3F:";  // untranslated
Blockly.Msg["LOGIC_TERNARY_IF_FALSE"] = "ಒಂಜಿ ವೇಲೆ ಸುಲ್ಲಾಂಡ";
Blockly.Msg["LOGIC_TERNARY_IF_TRUE"] = "ಒಂಜಿ ವೇಲೆ ನಿಜ ಆಂಡ";
Blockly.Msg["LOGIC_TERNARY_TOOLTIP"] = "'ಪರೀಕ್ಷೆ'ಡ್ ಶರ್ತನ್ ಸರಿತೂಲೆ. ಶರ್ತ ನಿಜವಾದಿತ್ತ್ಂಡ, 'ಒಂಜಿ ವೇಲೆ ನಿಜ ಆಂಡ' ಮೌಲ್ಯೊನು ಪಿರಕೊರ್ಪುಂಡು; ಇಜ್ಜಿಂಡ 'ಒಂಜಿ ವೇಲೆ ಸುಲ್ಲಾಂಡ' ಮೌಲ್ಯೊನು ಪಿರಕೊರ್ಪುಂಡು.";
Blockly.Msg["MATH_ADDITION_SYMBOL"] = "+";  // untranslated
Blockly.Msg["MATH_ARITHMETIC_HELPURL"] = "https://en.wikipedia.org/wiki/ಅಂಕಗಣಿತ";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_ADD"] = "ರಡ್ಡ್ ಸಂಖ್ಯೆದ ಮೊತ್ತನ್ ಪಿರಕೊರು.";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_DIVIDE"] = "ಸಂಖ್ಯೆದ ಭಾಗಲಬ್ದೊನು ಪಿರಕೊರು.";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_MINUS"] = "ರಡ್ಡ ಸ್ಂಖ್ಯೆದ ವ್ಯತ್ಯಾಸೊನು ಪಿರಕೊರು.";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_MULTIPLY"] = "ಸಂಖ್ಯೆದ ಗುಣಲಬ್ಧೊನು ಪಿರಕೊರು.";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_POWER"] = "ಸುರುತ್ತ ಸಂಖ್ಯೆದ ಘಾತೊನು ರಡ್ಡನೆ ಸಂಖ್ಯೆಗ್ ಏರ್ಪಾದ್ ಪಿರಕೊರು.";
Blockly.Msg["MATH_ATAN2_HELPURL"] = "https://en.wikipedia.org/wiki/Atan2";
Blockly.Msg["MATH_ATAN2_TITLE"] = "atan2 of X:%1 Y:%2";  // untranslated
Blockly.Msg["MATH_ATAN2_TOOLTIP"] = "Return the arctangent of point (X, Y) in degrees from -180 to 180.";  // untranslated
Blockly.Msg["MATH_CHANGE_HELPURL"] = "https://en.wikipedia.org/wiki/Programming_idiom#Incrementing_a_counter";
Blockly.Msg["MATH_CHANGE_TITLE"] = "%1 ನ್ %2 ಟ್ ಬದಲ್ ಮಲ್ಪು";
Blockly.Msg["MATH_CHANGE_TOOLTIP"] = "'%1' ವ್ಯತ್ಯಯೊಗು ಒಂಜಿ ಸಂಖ್ಯೆನ್ ಸೇರಾವ್";
Blockly.Msg["MATH_CONSTANT_HELPURL"] = "https://en.wikipedia.org/wiki/ಗಣಿತ_ನಿರಂತರ";
Blockly.Msg["MATH_CONSTANT_TOOLTIP"] = "ಒಂಜಿ ಸಾಮಾನ್ಯ ಸ್ಥಿರಾಂಕೊನು ಪಿರಕೊರು: π (3.141…), e (2.718…), φ (1.618…), sqrt(2) (1.414…), sqrt(½) (0.707…), or ∞ (infinity).";
Blockly.Msg["MATH_CONSTRAIN_HELPURL"] = "https://en.wikipedia.org/wiki/Clamping_(graphics)";  // untranslated
Blockly.Msg["MATH_CONSTRAIN_TITLE"] = "%2 ಕಮ್ಮಿ %3 ಜಾಸ್ತಿ %1 ನಿರ್ಬಂಧ ಮಲ್ಪು";
Blockly.Msg["MATH_CONSTRAIN_TOOLTIP"] = "ನಿಗದಿತ ಮಿತಿತ ನಡುಟು ಒಂಜಿ ಸಂಖ್ಯೆನ್ ನಿರ್ಬಂಧ ಮಲ್ಪು";
Blockly.Msg["MATH_DIVISION_SYMBOL"] = "÷";  // untranslated
Blockly.Msg["MATH_IS_DIVISIBLE_BY"] = "ಭಾಗಿಪೊಲಿ";
Blockly.Msg["MATH_IS_EVEN"] = "ಸಮ ಸಂಖ್ಯೆ";
Blockly.Msg["MATH_IS_NEGATIVE"] = "ಋಣ ಸಂಖ್ಯೆ";
Blockly.Msg["MATH_IS_ODD"] = "ಬೆಸ ಸಂಖ್ಯೆ";
Blockly.Msg["MATH_IS_POSITIVE"] = "ಧನ ಸಂಖ್ಯೆ";
Blockly.Msg["MATH_IS_PRIME"] = "ಅವಿಭಾಜ್ಯ ಸಂಖ್ಯೆ";
Blockly.Msg["MATH_IS_TOOLTIP"] = "ಒಂಜಿ ಸಂಖ್ಯೆ ಸಮನಾ, ಬೆಸನಾ, ಅವಿಭಾಜ್ಯನಾ, ಪೂರ್ಣನಾ, ಧನನಾ, ಋಣನಾ, ಅತ್ತಂಡ ಅವೆನ್ ಬೇತೆ ಒಂಜಿ ನಿರ್ದಿಷ್ಟ ಸಂಖ್ಯೆಡ್ದ್ ಭಾಗಿಪೊಲಿಯಾ ಪಂದ್ ಪರೀಕ್ಷೆ ಮಲ್ಪು. ನಿಜ ಅತ್ತಂಡ ಸುಲ್ಲುನು ಪಿರಕೊರ್ಪುಂಡು.";
Blockly.Msg["MATH_IS_WHOLE"] = "ಪೂರ್ಣ ಸಂಖ್ಯೆ";
Blockly.Msg["MATH_MODULO_HELPURL"] = "https://en.wikipedia.org/wiki/ಮೋಡ್ಯುಲೊ_ಒಪರೇಶನ್";
Blockly.Msg["MATH_MODULO_TITLE"] = " %1 ÷ %2 ತ ಶೇಷ";
Blockly.Msg["MATH_MODULO_TOOLTIP"] = "ರಡ್ಡ್ ಸಂಖ್ಯೆಲೆನ್ ಭಾಗ ಮಲ್ತ್‌ದ್ ಶೇಷೊನು ಪಿರಕೊರು.";
Blockly.Msg["MATH_MULTIPLICATION_SYMBOL"] = "×";  // untranslated
Blockly.Msg["MATH_NUMBER_HELPURL"] = "https://en.wikipedia.org/wiki/ಸಂಖ್ಯೆ";
Blockly.Msg["MATH_NUMBER_TOOLTIP"] = "ಅ ನಂಬ್ರೊ.";
Blockly.Msg["MATH_ONLIST_HELPURL"] = "";  // untranslated
Blockly.Msg["MATH_ONLIST_OPERATOR_AVERAGE"] = "ಪಟ್ಟಿದ ಸರಾಸರಿ";
Blockly.Msg["MATH_ONLIST_OPERATOR_MAX"] = "ಪಟ್ಟಿಡ್ ಮಲ್ಲವ್";
Blockly.Msg["MATH_ONLIST_OPERATOR_MEDIAN"] = "ಪಟ್ಟಿದ ನಡುತ್ತವ್";
Blockly.Msg["MATH_ONLIST_OPERATOR_MIN"] = "ಪಟ್ಟಿಡ್ ಕಿಞ್ಞವ್";
Blockly.Msg["MATH_ONLIST_OPERATOR_MODE"] = "ಪಟ್ಟಿದ ಮಸ್ತ್ ಸಾಮಾನ್ಯ ಮೌಲ್ಯ";
Blockly.Msg["MATH_ONLIST_OPERATOR_RANDOM"] = "ಪಟ್ಟಿದ ಒವ್ವಾಂಡಲ ಒಂಜಿ ವಿಷಯ";
Blockly.Msg["MATH_ONLIST_OPERATOR_STD_DEV"] = "ಪಟ್ಟಿದ ಪ್ರಮಾಣಿತ ವಿಚಲನ";
Blockly.Msg["MATH_ONLIST_OPERATOR_SUM"] = "ಪಟ್ಟಿದ ಮೊತ್ತ";
Blockly.Msg["MATH_ONLIST_TOOLTIP_AVERAGE"] = "ಪಟ್ಟಿಡುಪ್ಪುನ ಮಾತಾ ಸಂಖ್ಯೆಲೆನ ಸರಾಸರಿನ್ ಪಿರಕೊರು";
Blockly.Msg["MATH_ONLIST_TOOLTIP_MAX"] = "ಪಟ್ಟಿಡುಪ್ಪುನ ಮಲ್ಲ ಸಂಖ್ಯೆನ್ ಪಿರಕೊರು";
Blockly.Msg["MATH_ONLIST_TOOLTIP_MEDIAN"] = "ಪಟ್ಟಿಡುಪ್ಪುನ ನಡುತ್ತ ಸಂಖ್ಯೆನ್ ಪಿರಕೊರು";
Blockly.Msg["MATH_ONLIST_TOOLTIP_MIN"] = "ಪಟ್ಟಿಡುಪ್ಪುನ ಕಿಞ್ಞ ಸಂಕ್ಯೆನ್ ಪಿರಕೊರು";
Blockly.Msg["MATH_ONLIST_TOOLTIP_MODE"] = "ಪಟ್ಟಿಡುಪ್ಪುನ ಮಸ್ತ್ ಸಾಮಾನ್ಯ ವಿಷಯೊನು ಪಿರಕೊರು";
Blockly.Msg["MATH_ONLIST_TOOLTIP_RANDOM"] = "ಪಟ್ಟಿದ ಒವ್ವಾಂಡಲ ಒಂಜಿ ಅಂಶೊನು ಪಿರಕೊರು.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_STD_DEV"] = "ಪಟ್ಟಿದ ಪ್ರಮಾಣಿತ ವಿಚಲನೊನು ಪಿರಕೊರು";
Blockly.Msg["MATH_ONLIST_TOOLTIP_SUM"] = "ಪಟ್ಟಿಡುಪ್ಪುನ ಮಾತಾ ಸಂಖ್ಯೆಲೆನ ಮೊತ್ತನ್ ಪಿರಕೊರು";
Blockly.Msg["MATH_POWER_SYMBOL"] = "^";  // untranslated
Blockly.Msg["MATH_RANDOM_FLOAT_HELPURL"] = "https://en.wikipedia.org/wiki/ರಾಂಡಮ್_ನಂಬರ್_ಜನರೇಶನ್";
Blockly.Msg["MATH_RANDOM_FLOAT_TITLE_RANDOM"] = "ಒವ್ವಾಂಡಲ ಒಂಜಿ ಭಿನ್ನರಾಶಿ";
Blockly.Msg["MATH_RANDOM_FLOAT_TOOLTIP"] = "0.0 (ಸೇರ್‌ದ್) ಬೊಕ್ಕ 1.0 (ಸೇರಂದೆ) ನಡುತ ಒವ್ವಾಂಡಲ ಒಂಜಿ ಭಿನ್ನರಾಶಿನ್ ಪಿರಕೊರು.";
Blockly.Msg["MATH_RANDOM_INT_HELPURL"] = "https://en.wikipedia.org/wiki/ರಾಂಡಮ್_ನಂಬರ್_ಜನರೇಶನ್";
Blockly.Msg["MATH_RANDOM_INT_TITLE"] = " %1 ಡ್ದ್ %2 ಯಾದೃಚ್ಛಿಕ ಪೂರ್ಣಾಂಕೊ";
Blockly.Msg["MATH_RANDOM_INT_TOOLTIP"] = "ರಡ್ಡ್ ನಿಗದಿತ ಮಿತಿತ ನಡುತ್ತ ಯಾದೃಚ್ಛಿಕ ಪೂರ್ಣಾಂಕೊನು ಪಿರಕೊರು";
Blockly.Msg["MATH_ROUND_HELPURL"] = "https://en.wikipedia.org/wiki/ಪೂರ್ಣಾಂಕೊ";
Blockly.Msg["MATH_ROUND_OPERATOR_ROUND"] = "ರೌಂಡ್";
Blockly.Msg["MATH_ROUND_OPERATOR_ROUNDDOWN"] = "ತಿರ್ತ್‌ಗ್ ರೌಂಡ್";
Blockly.Msg["MATH_ROUND_OPERATOR_ROUNDUP"] = "ಮಿತ್ತ್‌ಗ್ ರೌಂಡ್";
Blockly.Msg["MATH_ROUND_TOOLTIP"] = "ಒಂಜಿ ಸಂಖ್ಯೆನ್ ಮಿತ್ತ್‌ಗ್ ಅತ್ತಂಡ ತಿರ್ತ್‌ಗ್ ರೌಂಡ್ ಮಲ್ಪು";
Blockly.Msg["MATH_SINGLE_HELPURL"] = "https://en.wikipedia.org/wiki/ವರ್ಗಮೂಲೊ";
Blockly.Msg["MATH_SINGLE_OP_ABSOLUTE"] = "ಸಂಪೂರ್ನೊ";
Blockly.Msg["MATH_SINGLE_OP_ROOT"] = "ವರ್ಗಮೂಲೊ";
Blockly.Msg["MATH_SINGLE_TOOLTIP_ABS"] = "ಸಂಖ್ಯೆದ ಸರಿಯಾಯಿನ ಮೌಲ್ಯೊನು ಕೊರು";
Blockly.Msg["MATH_SINGLE_TOOLTIP_EXP"] = "ಒಂಜಿ ಸಂಖ್ಯೆದ ಘಾತೊಗು 'e'ನ್ ಪಿರಕೊರು.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_LN"] = "ಸಂಖ್ಯೆದ ಪ್ರಾಕೃತಿಕ ಲಘುಗಣಕನ್ ಪಿರಕೊರು";
Blockly.Msg["MATH_SINGLE_TOOLTIP_LOG10"] = "ಸಂಖ್ಯೆದ ದಶಮಾನ ಲಘುಗಣಕನ್ ಪಿರಕೊರು";
Blockly.Msg["MATH_SINGLE_TOOLTIP_NEG"] = "ಸಂಖ್ಯೆದ ನಿಷೇಧೊನು ಪಿರಕೊರು";
Blockly.Msg["MATH_SINGLE_TOOLTIP_POW10"] = "ಒಂಜಿ ಸಂಖ್ಯೆದ ಘಾತೊಗು ೧೦ನ್ ಪಿರಕೊರು";
Blockly.Msg["MATH_SINGLE_TOOLTIP_ROOT"] = "ಸಂಖ್ಯೆದ ವರ್ಗಮೂಲೊನು ಪಿರಕೊರು.";
Blockly.Msg["MATH_SUBTRACTION_SYMBOL"] = "-";  // untranslated
Blockly.Msg["MATH_TRIG_ACOS"] = "acos";  // untranslated
Blockly.Msg["MATH_TRIG_ASIN"] = "asin";  // untranslated
Blockly.Msg["MATH_TRIG_ATAN"] = "atan";  // untranslated
Blockly.Msg["MATH_TRIG_COS"] = "cos";  // untranslated
Blockly.Msg["MATH_TRIG_HELPURL"] = "https://en.wikipedia.org/wiki/ತ್ರಿಕೋನಮಿತಿದ_ಕಾರ್ಯೊಲು";
Blockly.Msg["MATH_TRIG_SIN"] = "sin";  // untranslated
Blockly.Msg["MATH_TRIG_TAN"] = "tan";  // untranslated
Blockly.Msg["MATH_TRIG_TOOLTIP_ACOS"] = "ಒಂಜಿ ಸಂಖ್ಯೆದ ಆರ್ಕ್‌‌ಕೊಸೈನ್ ಪಿರಕೊರು.";
Blockly.Msg["MATH_TRIG_TOOLTIP_ASIN"] = "ಒಂಜಿ ಸಂಖ್ಯೆದ ಆರ್ಕ್‌ಸೈನ್ ಪಿರಕೊರು.";
Blockly.Msg["MATH_TRIG_TOOLTIP_ATAN"] = "ಒಂಜಿ ಸಂಖ್ಯೆದ ಆರ್ಕ್‌ಟ್ಯಾನ್‌ಜ್ಂಟ್ ಪಿರಕೊರು.";
Blockly.Msg["MATH_TRIG_TOOLTIP_COS"] = "ಒಂಜಿ ಡಿಗ್ರಿದ ಕೊಸೈನ್ (cosine) ಪಿರಕೊರು (ರೇಡಿಯನ್ ಅತ್ತ್).";
Blockly.Msg["MATH_TRIG_TOOLTIP_SIN"] = "ಒಂಜಿ ಡಿಗ್ರಿದ ಸೈನ್ (sine) ಪಿರಕೊರು (ರೇಡಿಯನ್ ಅತ್ತ್).";
Blockly.Msg["MATH_TRIG_TOOLTIP_TAN"] = "ಒಂಜಿ ಡಿಗ್ರಿದ ಟ್ಯಾನ್‌ಜೆಂಟ್ (tangent) ಪಿರಕೊರು (ರೇಡಿಯನ್ ಅತ್ತ್).";
Blockly.Msg["NEW_COLOUR_VARIABLE"] = "Create colour variable...";  // untranslated
Blockly.Msg["NEW_NUMBER_VARIABLE"] = "Create number variable...";  // untranslated
Blockly.Msg["NEW_STRING_VARIABLE"] = "Create string variable...";  // untranslated
Blockly.Msg["NEW_VARIABLE"] = "ವ್ಯತ್ಯಯೊನು ಉಂಡು ಮಲ್ಪುಲೆ";
Blockly.Msg["NEW_VARIABLE_TITLE"] = "ಪೊಸ ವ್ಯತ್ಯಯೊದ ಪುದರ್:";
Blockly.Msg["NEW_VARIABLE_TYPE_TITLE"] = "New variable type:";  // untranslated
Blockly.Msg["ORDINAL_NUMBER_SUFFIX"] = "";  // untranslated
Blockly.Msg["PROCEDURES_ALLOW_STATEMENTS"] = "ಹೇಳಿಕೆಗ್ ಅವಕಾಸೊ";
Blockly.Msg["PROCEDURES_BEFORE_PARAMS"] = "ಒಟ್ಟುಗು:";
Blockly.Msg["PROCEDURES_CALLNORETURN_HELPURL"] = "https://en.wikipedia.org/wiki/ವರ್ಗಮೂಲೊ";
Blockly.Msg["PROCEDURES_CALLNORETURN_TOOLTIP"] = "'%1' ಬಳಕೆದಾರೆರೆ ಕಾರ್ಯೊನು ನಡಪಾಲೆ.";
Blockly.Msg["PROCEDURES_CALLRETURN_HELPURL"] = "https://en.wikipedia.org/wiki/ವರ್ಗಮೂಲೊ";
Blockly.Msg["PROCEDURES_CALLRETURN_TOOLTIP"] = " ಬಳಕೆದಾರೆರೆ ಕಾರ್ಯೊ '%1' ನು ನಡಪಾಲೆ ಬುಕ್ಕೊ ಅಯಿತ ಉತ್ಪಾದನೆನ್ ಗಲಸ್‌ಲೆ.";
Blockly.Msg["PROCEDURES_CALL_BEFORE_PARAMS"] = "ಒಟ್ಟುಗು:";
Blockly.Msg["PROCEDURES_CREATE_DO"] = " '%1'ನ್ ಉಂಡುಮಲ್ಪುಲೆ";
Blockly.Msg["PROCEDURES_DEFNORETURN_COMMENT"] = "ಈ ಕಾರ್ಯೊನು ಇವರಿಪುಲೆ...";
Blockly.Msg["PROCEDURES_DEFNORETURN_DO"] = "";  // untranslated
Blockly.Msg["PROCEDURES_DEFNORETURN_HELPURL"] = "https://en.wikipedia.org/wiki/Subroutine";  // untranslated
Blockly.Msg["PROCEDURES_DEFNORETURN_PROCEDURE"] = "ಎಂಚಿನಾಂಡಲ ಮಲ್ಪುಲೆ";
Blockly.Msg["PROCEDURES_DEFNORETURN_TITLE"] = "ಇಂದೆಕ್";
Blockly.Msg["PROCEDURES_DEFNORETURN_TOOLTIP"] = "ಔಟ್‌ಪುಟ್ ದಾಂತಿನ ಕಾರ್ಯೊನು ಉಂಡುಮಲ್ಪುಂಡು.";
Blockly.Msg["PROCEDURES_DEFRETURN_HELPURL"] = "https://en.wikipedia.org/wiki/Subroutine";  // untranslated
Blockly.Msg["PROCEDURES_DEFRETURN_RETURN"] = "ಪಿರಕೊರು";
Blockly.Msg["PROCEDURES_DEFRETURN_TOOLTIP"] = "ಔಟ್‌ಪುಟ್ ಇತ್ತಿನ ಕಾರ್ಯೊನು ಉಂಡುಮಲ್ಪುಂಡು.";
Blockly.Msg["PROCEDURES_DEF_DUPLICATE_WARNING"] = "ಎಚ್ಚರಿಕೆ: ಈ ಕಾರ್ಯೊಡು ನಕಲಿ ಮಾನದಂಡೊ ಉಂಡು.";
Blockly.Msg["PROCEDURES_HIGHLIGHT_DEF"] = "ದೆರ್ತ್ ತೋಜುನ ಕಾರ್ಯೊದ ವ್ಯಾಕ್ಯಾನೊ";
Blockly.Msg["PROCEDURES_IFRETURN_HELPURL"] = "http://c2.com/cgi/wiki?GuardClause";  // untranslated
Blockly.Msg["PROCEDURES_IFRETURN_TOOLTIP"] = "ಮೌಲ್ಯೊ ಸತ್ಯೊ ಆಂಡ, ರಡ್ಡನೆ ಮೌಲ್ಯೊನು ಪಿರಕೊರು.";
Blockly.Msg["PROCEDURES_IFRETURN_WARNING"] = "ಎಚ್ಚರಿಕೆ: ಒಂಜಿ ಕಾರ್ಯ ವ್ಯಾಕ್ಯಾನೊದುಲಯಿ ಮಾತ್ರ ಈ ತಡೆನ್ ಗಲಸೊಲಿ.";
Blockly.Msg["PROCEDURES_MUTATORARG_TITLE"] = "ಉಲಪರಿಪುದ ಪುದರ್:";
Blockly.Msg["PROCEDURES_MUTATORARG_TOOLTIP"] = "ಕಾರ್ಯೊಗು ಒಂಜಿ ಉಲಪರಿಪುನು ಸೇರಲೆ.";
Blockly.Msg["PROCEDURES_MUTATORCONTAINER_TITLE"] = "ಉಲಪರಿಪು";
Blockly.Msg["PROCEDURES_MUTATORCONTAINER_TOOLTIP"] = "ಸೇರಾಲೆ, ದೆತ್ತ್‌ ಬುಡುಲೆ, ಅತ್ತಂಡ ಈ ಕಾರ್ಯೊಗು ಉಲಪರಿಪುಲೆನ್ ಕುಡ ಒತ್ತರೆ ಮಲ್ಪುಲೆ.";
Blockly.Msg["REDO"] = "ಕುಡ ಮಲ್ಪು";
Blockly.Msg["REMOVE_COMMENT"] = "ಟಿಪ್ಪಣಿನ್ ದೆತ್ತ್‌ದ್ ಬುಡ್ಲೆ";
Blockly.Msg["RENAME_VARIABLE"] = "ವ್ಯತ್ಯಯೊಗು ಕುಡೊರ ಪುದರ್ ದೀಲೆ";
Blockly.Msg["RENAME_VARIABLE_TITLE"] = "ಮಾತಾ '%1' ವ್ಯತ್ಯಯೊಲೆನ ಪುದರ್‌ನ್ ನೆಕ್ಕ್ ಬದಲ್ ಮಲ್ಪುಲೆ:";
Blockly.Msg["TEXT_APPEND_HELPURL"] = "https://github.com/google/blockly/wiki/Text#text-modification";  // untranslated
Blockly.Msg["TEXT_APPEND_TITLE"] = "ಇಂದೆಕ್ %1 ಪಟ್ಯೊನು ಸೇರವೆ %2";
Blockly.Msg["TEXT_APPEND_TOOLTIP"] = "%1 ವ್ಯತ್ಯಯೊಗು ಕೆಲವು ಪಟ್ಯೊಲೆನ್ ಸೇರಾವ್";
Blockly.Msg["TEXT_CHANGECASE_HELPURL"] = "https://github.com/google/blockly/wiki/Text#adjusting-text-case";  // untranslated
Blockly.Msg["TEXT_CHANGECASE_OPERATOR_LOWERCASE"] = "ಎಲ್ಯ ಅಕ್ಷರೊಗು";
Blockly.Msg["TEXT_CHANGECASE_OPERATOR_TITLECASE"] = "ತರೆಬರವುದ ಅಕ್ಷರೊಗು";
Blockly.Msg["TEXT_CHANGECASE_OPERATOR_UPPERCASE"] = "ಮಲ್ಲ ಅಕ್ಷರೊಗು";
Blockly.Msg["TEXT_CHANGECASE_TOOLTIP"] = "ಪಟ್ಯೊದ ಒಂಜಿ ನಕಲ್‍ನ್ ಬೇತೆ ನಮೂನೆಡ್ (case) ಪಿರಕೊರು.";
Blockly.Msg["TEXT_CHARAT_FIRST"] = "ಸುರುತ್ತ ಅಕ್ಷರೊನು ದೆತ್ತೊನು";
Blockly.Msg["TEXT_CHARAT_FROM_END"] = "ಅಕೇರಿಡ್ದ್ ಅಕ್ಷರೊ #ನ್ ದೆತ್ತೊನು";
Blockly.Msg["TEXT_CHARAT_FROM_START"] = "ಅಕ್ಸರೊ #ನ್ ದೆತ್ತೊನು";
Blockly.Msg["TEXT_CHARAT_HELPURL"] = "https://github.com/google/blockly/wiki/Text#extracting-text";  // untranslated
Blockly.Msg["TEXT_CHARAT_LAST"] = "ಅಕೇರಿದ ಅಕ್ಷರೊನು ದೆತ್ತೊನು";
Blockly.Msg["TEXT_CHARAT_RANDOM"] = "ಒವ್ವಾಂಡಲ ಒಂಜಿ ಅಕ್ಷರೊನು ದೆತ್ತೊನು";
Blockly.Msg["TEXT_CHARAT_TAIL"] = "";  // untranslated
Blockly.Msg["TEXT_CHARAT_TITLE"] = "%1 %2 ಪದೊಟ್ಟು";
Blockly.Msg["TEXT_CHARAT_TOOLTIP"] = "ಅಕ್ಷರೊನು ನಿರ್ದಿಷ್ಟ ಸ್ಥಿತಿಡ್ ಪಿರಕೊರ್ಪುಂಡು.";
Blockly.Msg["TEXT_COUNT_HELPURL"] = "https://github.com/google/blockly/wiki/Text#counting-substrings";  // untranslated
Blockly.Msg["TEXT_COUNT_MESSAGE0"] = "count %1 in %2";  // untranslated
Blockly.Msg["TEXT_COUNT_TOOLTIP"] = "Count how many times some text occurs within some other text.";  // untranslated
Blockly.Msg["TEXT_CREATE_JOIN_ITEM_TOOLTIP"] = "ಪಟ್ಯೊಗು ಒಂಜಿ ವಿಷಯೊನು ಸೇರಾವ್";
Blockly.Msg["TEXT_CREATE_JOIN_TITLE_JOIN"] = "ಸೇರಾವ್";
Blockly.Msg["TEXT_CREATE_JOIN_TOOLTIP"] = "ಈ ಪಠ್ಯ ತಡೆನ್ ಕುಡ ಸಂರಚಣೆ ಮಲ್ಪೆರೆ, ಸೇರಾವ್, ದೆತ್ತ್ ಬುಡು, ಅತ್ತಂಡ ವಿಭಾಗೊಲೆನ್ ಕುಡ ಒತ್ತರೆ ಮಲ್ಪು.";
Blockly.Msg["TEXT_GET_SUBSTRING_END_FROM_END"] = "ಅಕೇರಿಡ್ದ್ ಅಕ್ಷರೊ #ಗು";
Blockly.Msg["TEXT_GET_SUBSTRING_END_FROM_START"] = "ಅಕ್ಷರೊ #ಗು";
Blockly.Msg["TEXT_GET_SUBSTRING_END_LAST"] = "ಅಕೇರಿದ ಅಕ್ಷರೊಗು";
Blockly.Msg["TEXT_GET_SUBSTRING_HELPURL"] = "https://github.com/google/blockly/wiki/Text#extracting-a-region-of-text";  // untranslated
Blockly.Msg["TEXT_GET_SUBSTRING_INPUT_IN_TEXT"] = "ಪಟ್ಯೊಡು";
Blockly.Msg["TEXT_GET_SUBSTRING_START_FIRST"] = "ಸುರುತ್ತ ಅಕ್ಷರೊ #ಡ್ದು ಉಪವಾಕ್ಯೊನು ದೆತ್ತೊನು";
Blockly.Msg["TEXT_GET_SUBSTRING_START_FROM_END"] = "ಅಕೇರಿಡ್ದ್ ಅಕ್ಷರೊ #ಡ್ದು ಉಪವಾಕ್ಯೊನು ದೆತ್ತೊನು";
Blockly.Msg["TEXT_GET_SUBSTRING_START_FROM_START"] = "ಅಕ್ಷರೊ #ಡ್ದು ಉಪವಾಕ್ಯೊ ದೆತ್ತೊನು";
Blockly.Msg["TEXT_GET_SUBSTRING_TAIL"] = "";  // untranslated
Blockly.Msg["TEXT_GET_SUBSTRING_TOOLTIP"] = "ಪಟ್ಯೊದ ಒಂಜಿ ನಿರ್ದಿಷ್ಟ ಬಾಗೊನು ಪಿರಕೊರ್ಪುಂಡು.";
Blockly.Msg["TEXT_INDEXOF_HELPURL"] = "https://github.com/google/blockly/wiki/Text#finding-text";  // untranslated
Blockly.Msg["TEXT_INDEXOF_OPERATOR_FIRST"] = "ಪಟ್ಯೊ ಸುರುಕ್ಕು ಬತ್ತಿನೇನ್ ನಾಡ್";
Blockly.Msg["TEXT_INDEXOF_OPERATOR_LAST"] = "ಪಟ್ಯೊ ಅಕೇರಿಗ್ ಬತ್ತಿನೇನ್ ನಾಡ್";
Blockly.Msg["TEXT_INDEXOF_TITLE"] = "ಪಟ್ಯೊಡು %1 %2 %3";
Blockly.Msg["TEXT_INDEXOF_TOOLTIP"] = "ರಡ್ಡನೆ ಪಟ್ಯೊಡು ಉಪ್ಪುನ ಸುರುತ ಪಟ್ಯೊ ಸುರುಕ್ಕು/ಅಕೇರಿಗ್ ಬತ್ತಿನೆತ್ತ ಸೂಚಿನ್ ಪಿರಕೊರು. ಪಟ್ಯೊ ತಿಕ್ಕಿಜ್ಜಾಂಡ %1 ನ್ ಪಿರಕೊರು.";
Blockly.Msg["TEXT_ISEMPTY_HELPURL"] = "https://github.com/google/blockly/wiki/Text#checking-for-empty-text";  // untranslated
Blockly.Msg["TEXT_ISEMPTY_TITLE"] = "%1 ಖಾಲಿ";
Blockly.Msg["TEXT_ISEMPTY_TOOLTIP"] = "ಕೊರಿನ ಪಟ್ಯೊ ಖಾಲಿ ಇತ್ತ್ಂಡ 'ನಿಜ'ನ್ ಪಿರಕೊರು.";
Blockly.Msg["TEXT_JOIN_HELPURL"] = "https://github.com/google/blockly/wiki/Text#text-creation";  // untranslated
Blockly.Msg["TEXT_JOIN_TITLE_CREATEWITH"] = "ನೆಡ್ದ್ ಪಟ್ಯೊನು ಉಂಡು ಮಲ್ಪು";
Blockly.Msg["TEXT_JOIN_TOOLTIP"] = "ಏತಾಂಡಲ ವಿಷಯಲೆನ್ ಒಟ್ಟುಗು ಸೇರಾದ್ ಒಂಜಿ ಪಟ್ಯೊದ ತುಂಡುನು ಉಂಡುಮಲ್ಪು.";
Blockly.Msg["TEXT_LENGTH_HELPURL"] = "https://github.com/google/blockly/wiki/Text#text-modification";  // untranslated
Blockly.Msg["TEXT_LENGTH_TITLE"] = "%1 ಉದ್ದೊ";
Blockly.Msg["TEXT_LENGTH_TOOLTIP"] = "ಕೊರಿನ ಪಟ್ಯೊದ ಅಕ್ಷರೊಲೆನ (ಅಂತರೊಲು ಸೇರ್‌ದ್) ಸಂಖ್ಯೆನ್ ಪಿರಕೊರು.";
Blockly.Msg["TEXT_PRINT_HELPURL"] = "https://github.com/google/blockly/wiki/Text#printing-text";  // untranslated
Blockly.Msg["TEXT_PRINT_TITLE"] = "%1 ಮುದ್ರಣ";
Blockly.Msg["TEXT_PRINT_TOOLTIP"] = "ನಿರ್ದಿಷ್ಟ ಪಟ್ಯೊ, ಸಂಖ್ಯೆ ಅತ್ತಂಡ ಬೇತೆ ಮೌಲ್ಯೊನು ಮುದ್ರಿಪುಲೆ.";
Blockly.Msg["TEXT_PROMPT_HELPURL"] = "https://github.com/google/blockly/wiki/Text#getting-input-from-the-user";  // untranslated
Blockly.Msg["TEXT_PROMPT_TOOLTIP_NUMBER"] = "ಒಂಜಿ ಸಂಖ್ಯೆಗ್ ಸದಸ್ಯೆರೆನ್ ಕೇನ್";
Blockly.Msg["TEXT_PROMPT_TOOLTIP_TEXT"] = "ಕೆಲವು ಪಟ್ಯೊಗು ಸದಸ್ಯೆರೆನ್ ಕೇನ್.";
Blockly.Msg["TEXT_PROMPT_TYPE_NUMBER"] = "ಸಂದೇಶೊದೊಟ್ಟುಗು ಸಂಕ್ಯೆನ್ ಕೇನ್";
Blockly.Msg["TEXT_PROMPT_TYPE_TEXT"] = "ಸಂದೇಶೊದೊಟ್ಟುಗು ಪಟ್ಯೊಗು ಕೇನ್.";
Blockly.Msg["TEXT_REPLACE_HELPURL"] = "https://github.com/google/blockly/wiki/Text#replacing-substrings";  // untranslated
Blockly.Msg["TEXT_REPLACE_MESSAGE0"] = "replace %1 with %2 in %3";  // untranslated
Blockly.Msg["TEXT_REPLACE_TOOLTIP"] = "Replace all occurances of some text within some other text.";  // untranslated
Blockly.Msg["TEXT_REVERSE_HELPURL"] = "https://github.com/google/blockly/wiki/Text#reversing-text";  // untranslated
Blockly.Msg["TEXT_REVERSE_MESSAGE0"] = "reverse %1";  // untranslated
Blockly.Msg["TEXT_REVERSE_TOOLTIP"] = "Reverses the order of the characters in the text.";  // untranslated
Blockly.Msg["TEXT_TEXT_HELPURL"] = "https://en.wikipedia.org/wiki/ಸ್ಟ್ರಿಂಗ್_(ಕಂಪ್ಯೂಟರ್_ಸೈನ್ಸ್)";
Blockly.Msg["TEXT_TEXT_TOOLTIP"] = "ಒಂಜಿ ಅಕ್ಷರೊ, ಪದೊ ಅತ್ತಂಡ ಪಾಟೊದ ಒಂಜಿ ಸಾಲ್";
Blockly.Msg["TEXT_TRIM_HELPURL"] = "https://github.com/google/blockly/wiki/Text#trimming-removing-spaces";  // untranslated
Blockly.Msg["TEXT_TRIM_OPERATOR_BOTH"] = "ರಡ್ಡ್ ಮೆಯಿತ್ತಲ ಅಂತರೊಲೆನ್ (space) ಕತ್ತೆರ್.";
Blockly.Msg["TEXT_TRIM_OPERATOR_LEFT"] = "ಎಡತ್ತ ಮೆಯಿತ್ತ ಅಂತರೊಲೆನ್ (space) ಕತ್ತೆರ್.";
Blockly.Msg["TEXT_TRIM_OPERATOR_RIGHT"] = "ಬಲತ್ತ ಮೆಯಿತ್ತ ಅಂತರೊಲೆನ್ (space) ಕತ್ತೆರ್.";
Blockly.Msg["TEXT_TRIM_TOOLTIP"] = "ಒಂಜಿ ಅತ್ತಂಡ ರಡ್ಡ್ ಕೊಡಿಡ್ದ್ ಅಂತರೊಲೆನ್ (space) ದೆತ್ತ್‌ದ್ ಪಟ್ಯೊದ ಪ್ರತಿನ್ ಪಿರಕೊರು.";
Blockly.Msg["TODAY"] = "ಇನಿ";
Blockly.Msg["UNDO"] = "ದುಂಬುದಲೆಕೊ";
Blockly.Msg["UNNAMED_KEY"] = "ಪುದರ್ ಇಜ್ಜಂತಿನವು";
Blockly.Msg["VARIABLES_DEFAULT_NAME"] = "ವಸ್ತು";
Blockly.Msg["VARIABLES_GET_CREATE_SET"] = "'ಸೆಟ್ %1' ಉಂಡುಮಲ್ಪುಲೆ";
Blockly.Msg["VARIABLES_GET_HELPURL"] = "https://github.com/google/blockly/wiki/Variables#get";  // untranslated
Blockly.Msg["VARIABLES_GET_TOOLTIP"] = "ಈ ವ್ಯತ್ಯಯೊದ ಮೌಲ್ಯೊನು ಪಿರಕೊರು.";
Blockly.Msg["VARIABLES_SET"] = "%1 ನ್ %2 ಕ್ಕ್ ಸೆಟ್ ಮಲ್ಪುಲೆ";
Blockly.Msg["VARIABLES_SET_CREATE_GET"] = "'ದೆತ್ತೊನು %1' ಉಂಡುಮಲ್ಪುಲೆ";
Blockly.Msg["VARIABLES_SET_HELPURL"] = "https://github.com/google/blockly/wiki/Variables#set";  // untranslated
Blockly.Msg["VARIABLES_SET_TOOLTIP"] = "ಈ ವ್ಯತ್ಯಯೊನು ಇನ್‌ಪುಟ್‌ಗ್ ಸಮ ಆಪಿಲೆಕ ಸೆಟ್ ಮಲ್ಪುಂಡು.";
Blockly.Msg["VARIABLE_ALREADY_EXISTS"] = "'%1' ಪನ್ಪಿ ಪುದರ್‌ದ ವ್ಯತ್ಯಯೊ ದುಂಬೆ ಅಸ್ತಿತ್ವೊಡು ಉಂಡು.";
Blockly.Msg["VARIABLE_ALREADY_EXISTS_FOR_ANOTHER_TYPE"] = "A variable named '%1' already exists for another type: '%2'.";  // untranslated
Blockly.Msg["WORKSPACE_ARIA_LABEL"] = "Blockly Workspace";  // untranslated
Blockly.Msg["WORKSPACE_COMMENT_DEFAULT_TEXT"] = "ದಾದಾಂಡಲ ಪನ್ಲೇ...";
Blockly.Msg["CONTROLS_FOREACH_INPUT_DO"] = Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"];
Blockly.Msg["CONTROLS_FOR_INPUT_DO"] = Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"];
Blockly.Msg["CONTROLS_IF_ELSEIF_TITLE_ELSEIF"] = Blockly.Msg["CONTROLS_IF_MSG_ELSEIF"];
Blockly.Msg["CONTROLS_IF_ELSE_TITLE_ELSE"] = Blockly.Msg["CONTROLS_IF_MSG_ELSE"];
Blockly.Msg["CONTROLS_IF_IF_TITLE_IF"] = Blockly.Msg["CONTROLS_IF_MSG_IF"];
Blockly.Msg["CONTROLS_IF_MSG_THEN"] = Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"];
Blockly.Msg["CONTROLS_WHILEUNTIL_INPUT_DO"] = Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"];
Blockly.Msg["LISTS_CREATE_WITH_ITEM_TITLE"] = Blockly.Msg["VARIABLES_DEFAULT_NAME"];
Blockly.Msg["LISTS_GET_INDEX_HELPURL"] = Blockly.Msg["LISTS_INDEX_OF_HELPURL"];
Blockly.Msg["LISTS_GET_INDEX_INPUT_IN_LIST"] = Blockly.Msg["LISTS_INLIST"];
Blockly.Msg["LISTS_GET_SUBLIST_INPUT_IN_LIST"] = Blockly.Msg["LISTS_INLIST"];
Blockly.Msg["LISTS_INDEX_OF_INPUT_IN_LIST"] = Blockly.Msg["LISTS_INLIST"];
Blockly.Msg["LISTS_SET_INDEX_INPUT_IN_LIST"] = Blockly.Msg["LISTS_INLIST"];
Blockly.Msg["MATH_CHANGE_TITLE_ITEM"] = Blockly.Msg["VARIABLES_DEFAULT_NAME"];
Blockly.Msg["PROCEDURES_DEFRETURN_COMMENT"] = Blockly.Msg["PROCEDURES_DEFNORETURN_COMMENT"];
Blockly.Msg["PROCEDURES_DEFRETURN_DO"] = Blockly.Msg["PROCEDURES_DEFNORETURN_DO"];
Blockly.Msg["PROCEDURES_DEFRETURN_PROCEDURE"] = Blockly.Msg["PROCEDURES_DEFNORETURN_PROCEDURE"];
Blockly.Msg["PROCEDURES_DEFRETURN_TITLE"] = Blockly.Msg["PROCEDURES_DEFNORETURN_TITLE"];
Blockly.Msg["TEXT_APPEND_VARIABLE"] = Blockly.Msg["VARIABLES_DEFAULT_NAME"];
Blockly.Msg["TEXT_CREATE_JOIN_ITEM_TITLE_ITEM"] = Blockly.Msg["VARIABLES_DEFAULT_NAME"];

Blockly.Msg["MATH_HUE"] = "230";
Blockly.Msg["LOOPS_HUE"] = "120";
Blockly.Msg["LISTS_HUE"] = "260";
Blockly.Msg["LOGIC_HUE"] = "210";
Blockly.Msg["VARIABLES_HUE"] = "330";
Blockly.Msg["TEXTS_HUE"] = "160";
Blockly.Msg["PROCEDURES_HUE"] = "290";
Blockly.Msg["COLOUR_HUE"] = "20";
Blockly.Msg["VARIABLES_DYNAMIC_HUE"] = "310";