// This file was automatically generated.  Do not modify.

'use strict';

Blockly.Msg["ADD_COMMENT"] = "ใส่คำอธิบาย";
Blockly.Msg["CANNOT_DELETE_VARIABLE_PROCEDURE"] = "ไม่สามารถลบตัวแปร '%1' ได้เนื่องจากเป็นส่วนหนึ่งของนิยามของฟังก์ชัน '%2'";
Blockly.Msg["CHANGE_VALUE_TITLE"] = "เปลี่ยนค่า:";
Blockly.Msg["CLEAN_UP"] = "จัดเรียงบล็อกให้เป็นแถว";
Blockly.Msg["COLLAPSED_WARNINGS_WARNING"] = "บล็อกที่ถูกพับมีคำเตือนอยู่ข้างใน.";
Blockly.Msg["COLLAPSE_ALL"] = "ย่อบล็อก";
Blockly.Msg["COLLAPSE_BLOCK"] = "ย่อบล็อก";
Blockly.Msg["COLOUR_BLEND_COLOUR1"] = "สีที่ 1";
Blockly.Msg["COLOUR_BLEND_COLOUR2"] = "สีที่ 2";
Blockly.Msg["COLOUR_BLEND_HELPURL"] = "https://meyerweb.com/eric/tools/color-blend/#:::rgbp";  // untranslated
Blockly.Msg["COLOUR_BLEND_RATIO"] = "อัตราส่วน";
Blockly.Msg["COLOUR_BLEND_TITLE"] = "ผสม";
Blockly.Msg["COLOUR_BLEND_TOOLTIP"] = "ผสมสองสีเข้าด้วยกันด้วยอัตราส่วน (0.0 - 1.0)";
Blockly.Msg["COLOUR_PICKER_HELPURL"] = "https://th.wikipedia.org/wiki/สี";
Blockly.Msg["COLOUR_PICKER_TOOLTIP"] = "เลือกสีจากจานสี";
Blockly.Msg["COLOUR_RANDOM_HELPURL"] = "http://randomcolour.com";  // untranslated
Blockly.Msg["COLOUR_RANDOM_TITLE"] = "สุ่มสี";
Blockly.Msg["COLOUR_RANDOM_TOOLTIP"] = "เลือกสีแบบสุ่ม";
Blockly.Msg["COLOUR_RGB_BLUE"] = "ค่าสีน้ำเงิน";
Blockly.Msg["COLOUR_RGB_GREEN"] = "ค่าสีเขียว";
Blockly.Msg["COLOUR_RGB_HELPURL"] = "https://www.december.com/html/spec/colorpercompact.html";  // untranslated
Blockly.Msg["COLOUR_RGB_RED"] = "ค่าสีแดง";
Blockly.Msg["COLOUR_RGB_TITLE"] = "สีที่ประกอบด้วย";
Blockly.Msg["COLOUR_RGB_TOOLTIP"] = "สร้างสีด้วยการกำหนดค่าสีแดง เขียว และน้ำเงิน ค่าทั้งหมดต้องอยู่ระหว่าง 0 ถึง 100";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_HELPURL"] = "https://github.com/google/blockly/wiki/Loops#loop-termination-blocks";  // untranslated
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_OPERATOR_BREAK"] = "ออกจากการวนซ้ำ";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_OPERATOR_CONTINUE"] = "เริ่มการวนซ้ำรอบต่อไป";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_TOOLTIP_BREAK"] = "ออกจากการวนซ้ำที่อยู่";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_TOOLTIP_CONTINUE"] = "ข้ามคำสั่งที่เหลืออยู่ และเริ่มต้นวนซ้ำรอบต่อไป";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_WARNING"] = "คำเตือน: บล็อกนี้ใช้งานได้ภายในการวนซ้ำเท่านั้น";
Blockly.Msg["CONTROLS_FOREACH_HELPURL"] = "https://github.com/google/blockly/wiki/Loops#for-each";  // untranslated
Blockly.Msg["CONTROLS_FOREACH_TITLE"] = "จากทุกรายการ %1 ในรายชื่อ %2";
Blockly.Msg["CONTROLS_FOREACH_TOOLTIP"] = "จากทุกรายการในรายชื่อ ตั้งค่าตัวแปร \"%1\" เป็นรายการ และทำตามคำสั่งที่กำหนดไว้";
Blockly.Msg["CONTROLS_FOR_HELPURL"] = "https://github.com/google/blockly/wiki/Loops#count-with";  // untranslated
Blockly.Msg["CONTROLS_FOR_TITLE"] = "นับด้วย %1 จาก %2 จนถึง %3 เปลี่ยนค่าทีละ %4";
Blockly.Msg["CONTROLS_FOR_TOOLTIP"] = "ตัวแปร '%1' จะเริ่มจากจำนวนเริ่มต้น ไปจนถึงจำนวนสุดท้าย ตามระยะที่กำหนด และ ทำบล็อกที่กำหนดไว้";
Blockly.Msg["CONTROLS_IF_ELSEIF_TOOLTIP"] = "กำหนดเงื่อนไขของบล็อก \"ถ้า\"";
Blockly.Msg["CONTROLS_IF_ELSE_TOOLTIP"] = "เพิ่มสิ่งสุดท้าย ที่จะตรวจจับความเป็นไปได้ทั้งหมดของบล็อก \"ถ้า\"";
Blockly.Msg["CONTROLS_IF_HELPURL"] = "https://github.com/google/blockly/wiki/IfElse";  // untranslated
Blockly.Msg["CONTROLS_IF_IF_TOOLTIP"] = "เพิ่ม ลบ หรือจัดเรียงบล็อก \"ถ้า\" นี้ใหม่";
Blockly.Msg["CONTROLS_IF_MSG_ELSE"] = "นอกเหนือจากนี้";
Blockly.Msg["CONTROLS_IF_MSG_ELSEIF"] = "นอกเหนือจากนี้ ถ้า";
Blockly.Msg["CONTROLS_IF_MSG_IF"] = "ถ้า";
Blockly.Msg["CONTROLS_IF_TOOLTIP_1"] = "ถ้าเงื่อนไขเป็นจริง ก็จะ \"ทำ\" ตามที่กำหนด";
Blockly.Msg["CONTROLS_IF_TOOLTIP_2"] = "ถ้าเงื่อนไขเป็นจริง ก็จะ \"ทำ\" ตามที่กำหนด แต่ถ้าเงื่อนไขเป็นเท็จก็จะทำ \"นอกเหนือจากนี้\"";
Blockly.Msg["CONTROLS_IF_TOOLTIP_3"] = "ถ้าเงื่อนไขแรกเป็นจริง ก็จะทำตามคำสั่งในบล็อกแรก แต่ถ้าไม่ก็จะไปตรวจเงื่อนไขที่สอง ถ้าเงื่อนไขที่สองเป็นจริง ก็จะทำตามเงื่อนไขในบล็อกที่สองนี้";
Blockly.Msg["CONTROLS_IF_TOOLTIP_4"] = "ถ้าเงื่อนไขแรกเป็นจริง ก็จะทำคำสั่งในบล็อกแรก จากนั้นจะข้ามคำสั่งในบล็อกที่เหลือ แต่ถ้าเงื่อนไขแรกเป็นเท็จ ก็จะทำการตรวจเงื่อนไขที่สอง ถ้าเงื่อนไขที่สองเป็นจริง ก็จะทำตามคำสั่งในบล็อกที่สอง จากนั้นจะข้ามคำสั่งในบล็อกที่เหลือ แต่ถ้าทั้งเงื่อนไขแรกและเงื่อนไขที่สองเป็นเท็จทั้งหมด ก็จะมาทำบล็อกที่สาม";
Blockly.Msg["CONTROLS_REPEAT_HELPURL"] = "https://en.wikipedia.org/wiki/For_loop";
Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"] = "ทำ";
Blockly.Msg["CONTROLS_REPEAT_TITLE"] = "ทำซ้ำ %1 ครั้ง";
Blockly.Msg["CONTROLS_REPEAT_TOOLTIP"] = "ทำซ้ำบางคำสั่งหลายครั้ง";
Blockly.Msg["CONTROLS_WHILEUNTIL_HELPURL"] = "https://github.com/google/blockly/wiki/Loops#repeat";  // untranslated
Blockly.Msg["CONTROLS_WHILEUNTIL_OPERATOR_UNTIL"] = "ทำซ้ำจนกระทั่ง";
Blockly.Msg["CONTROLS_WHILEUNTIL_OPERATOR_WHILE"] = "ทำซ้ำขณะที่";
Blockly.Msg["CONTROLS_WHILEUNTIL_TOOLTIP_UNTIL"] = "ขณะที่ค่าเป็นเท็จ ก็จะทำบางคำสั่ง";
Blockly.Msg["CONTROLS_WHILEUNTIL_TOOLTIP_WHILE"] = "ขณะที่ค่าเป็นจริง ก็จะทำบางคำสั่ง";
Blockly.Msg["DELETE_ALL_BLOCKS"] = "ลบ %1 บล็อกทั้งหมด?";
Blockly.Msg["DELETE_BLOCK"] = "ลบบล็อก";
Blockly.Msg["DELETE_VARIABLE"] = "ลบตัวแปร '%1'";
Blockly.Msg["DELETE_VARIABLE_CONFIRMATION"] = "ลบการใช้ตัวแปร %2 %1 ครั้งหรือไม่";
Blockly.Msg["DELETE_X_BLOCKS"] = "ลบ %1 บล็อก";
Blockly.Msg["DIALOG_CANCEL"] = "ยกเลิก";
Blockly.Msg["DIALOG_OK"] = "ตกลง";
Blockly.Msg["DISABLE_BLOCK"] = "ปิดใช้งานบล็อก";
Blockly.Msg["DUPLICATE_BLOCK"] = "ทำสำเนา";
Blockly.Msg["DUPLICATE_COMMENT"] = "ทำสำเนาความเห็น";
Blockly.Msg["ENABLE_BLOCK"] = "เปิดใช้งานบล็อก";
Blockly.Msg["EXPAND_ALL"] = "ขยายบล็อก";
Blockly.Msg["EXPAND_BLOCK"] = "ขยายบล็อก";
Blockly.Msg["EXTERNAL_INPUTS"] = "อินพุตภายนอก";
Blockly.Msg["HELP"] = "ช่วยเหลือ";
Blockly.Msg["INLINE_INPUTS"] = "อินพุตในบรรทัด";
Blockly.Msg["LISTS_CREATE_EMPTY_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#create-empty-list";  // untranslated
Blockly.Msg["LISTS_CREATE_EMPTY_TITLE"] = "สร้างรายการเปล่า";
Blockly.Msg["LISTS_CREATE_EMPTY_TOOLTIP"] = "สร้างรายการเปล่า (ความยาวเป็น 0) ยังไม่มีข้อมูลใดๆ อยู่";
Blockly.Msg["LISTS_CREATE_WITH_CONTAINER_TITLE_ADD"] = "รายการ";
Blockly.Msg["LISTS_CREATE_WITH_CONTAINER_TOOLTIP"] = "เพิ่ม ลบ หรือจัดเรียงบล็อกรายการนี้ใหม่";
Blockly.Msg["LISTS_CREATE_WITH_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#create-list-with";  // untranslated
Blockly.Msg["LISTS_CREATE_WITH_INPUT_WITH"] = "สร้างข้อความด้วย";
Blockly.Msg["LISTS_CREATE_WITH_ITEM_TOOLTIP"] = "เพิ่มไอเท็มเข้าไปในรายการ";
Blockly.Msg["LISTS_CREATE_WITH_TOOLTIP"] = "สร้างรายการพร้อมด้วยไอเท็ม";
Blockly.Msg["LISTS_GET_INDEX_FIRST"] = "แรกสุด";
Blockly.Msg["LISTS_GET_INDEX_FROM_END"] = "# จากท้าย";
Blockly.Msg["LISTS_GET_INDEX_FROM_START"] = "#";  // untranslated
Blockly.Msg["LISTS_GET_INDEX_GET"] = "เรียกดู";
Blockly.Msg["LISTS_GET_INDEX_GET_REMOVE"] = "เรียกดูและเอาออก";
Blockly.Msg["LISTS_GET_INDEX_LAST"] = "ท้ายสุด";
Blockly.Msg["LISTS_GET_INDEX_RANDOM"] = "สุ่ม";
Blockly.Msg["LISTS_GET_INDEX_REMOVE"] = "เอาออก";
Blockly.Msg["LISTS_GET_INDEX_TAIL"] = "";  // untranslated
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_FIRST"] = "คืนค่าไอเท็มอันแรกในรายการ";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_FROM"] = "คืนค่าเป็นไอเท็มตามตำแหน่งที่ระบุ";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_LAST"] = "คืนค่าไอเท็มอันสุดท้ายในรายการ";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_RANDOM"] = "คืนค่าไอเท็มแบบสุ่มจากรายการ";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_FIRST"] = "เอาออก และคืนค่าไอเท็มอันแรกในรายการ";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_FROM"] = "เอาออก และคืนค่าไอเท็มในตำแหน่งที่ระบุจากรายการ";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_LAST"] = "เอาออก และคืนค่าไอเท็มอันสุดท้ายในรายการ";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_RANDOM"] = "เอาออก และคืนค่าไอเท็มแบบสุ่มจากรายการ";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_REMOVE_FIRST"] = "เอาไอเท็มแรกสุดในรายการออก";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_REMOVE_FROM"] = "คืนค่าเป็นไอเท็มตามตำแหน่งที่ระบุ";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_REMOVE_LAST"] = "เอาไอเท็มอันท้ายสุดในรายการออก";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_REMOVE_RANDOM"] = "เอาไอเท็มแบบสุ่มจากรายการออก";
Blockly.Msg["LISTS_GET_SUBLIST_END_FROM_END"] = "ถึง #  จากท้ายสุด";
Blockly.Msg["LISTS_GET_SUBLIST_END_FROM_START"] = "จนถึง #";
Blockly.Msg["LISTS_GET_SUBLIST_END_LAST"] = "ถึง ท้ายสุด";
Blockly.Msg["LISTS_GET_SUBLIST_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#getting-a-sublist";  // untranslated
Blockly.Msg["LISTS_GET_SUBLIST_START_FIRST"] = "ดึงรายการย่อยทั้งแต่แรกสุด";
Blockly.Msg["LISTS_GET_SUBLIST_START_FROM_END"] = "ดึงรายการย่อยจาก # จากท้ายสุด";
Blockly.Msg["LISTS_GET_SUBLIST_START_FROM_START"] = "ดึงรายการย่อยจาก #";
Blockly.Msg["LISTS_GET_SUBLIST_TAIL"] = "";  // untranslated
Blockly.Msg["LISTS_GET_SUBLIST_TOOLTIP"] = "สร้างสำเนารายการในช่วงที่กำหนด";
Blockly.Msg["LISTS_INDEX_FROM_END_TOOLTIP"] = "%1 คือไอเท็มอันท้ายสุด";
Blockly.Msg["LISTS_INDEX_FROM_START_TOOLTIP"] = "%1 คือไอเท็มอันแรกสุด";
Blockly.Msg["LISTS_INDEX_OF_FIRST"] = "หาอันแรกที่พบ";
Blockly.Msg["LISTS_INDEX_OF_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#getting-items-from-a-list";  // untranslated
Blockly.Msg["LISTS_INDEX_OF_LAST"] = "หาอันสุดท้ายที่พบ";
Blockly.Msg["LISTS_INDEX_OF_TOOLTIP"] = "คืนค่าตำแหน่งของไอเท็มอันแรก/สุดท้ายที่พบในรายการ คืนค่า %1 ถ้าหาไม่พบ";
Blockly.Msg["LISTS_INLIST"] = "ในรายการ";
Blockly.Msg["LISTS_ISEMPTY_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#is-empty";  // untranslated
Blockly.Msg["LISTS_ISEMPTY_TITLE"] = "%1 ว่างเปล่า";
Blockly.Msg["LISTS_ISEMPTY_TOOLTIP"] = "คืนค่าเป็นจริง ถ้ารายการยังว่างเปล่า";
Blockly.Msg["LISTS_LENGTH_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#length-of";  // untranslated
Blockly.Msg["LISTS_LENGTH_TITLE"] = "ความยาวของ %1";
Blockly.Msg["LISTS_LENGTH_TOOLTIP"] = "ส่งคืนค่าความยาวของรายการ";
Blockly.Msg["LISTS_REPEAT_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#create-list-with";  // untranslated
Blockly.Msg["LISTS_REPEAT_TITLE"] = "สร้างรายการที่มีไอเท็ม %1 จำนวน %2";
Blockly.Msg["LISTS_REPEAT_TOOLTIP"] = "สร้างรายการที่ประกอบด้วยค่าตามที่ระบุในจำนวนตามที่ต้องการ";
Blockly.Msg["LISTS_REVERSE_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#reversing-a-list";  // untranslated
Blockly.Msg["LISTS_REVERSE_MESSAGE0"] = "เรียง %1 แบบย้อนกลับ";
Blockly.Msg["LISTS_REVERSE_TOOLTIP"] = "เรียงลำดับสำเนาของรายชื่อแบบย้อนกลับ";
Blockly.Msg["LISTS_SET_INDEX_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#in-list--set";  // untranslated
Blockly.Msg["LISTS_SET_INDEX_INPUT_TO"] = "เป็น";
Blockly.Msg["LISTS_SET_INDEX_INSERT"] = "แทรกที่";
Blockly.Msg["LISTS_SET_INDEX_SET"] = "กำหนด";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_INSERT_FIRST"] = "แทรกไอเท็มเข้าไปเป็นอันแรกสุดของรายการ";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_INSERT_FROM"] = "แทรกไอเท็มเข้าไปในตำแหน่งที่กำหนด";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_INSERT_LAST"] = "เพิ่มไอเท็มเข้าไปท้ายสุดของรายการ";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_INSERT_RANDOM"] = "เพิ่มไอเท็มเข้าไปในรายการแบบสุ่ม";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_SET_FIRST"] = "กำหนดไอเท็มอันแรกในรายการ";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_SET_FROM"] = "กำหนดไอเท็มในตำแหน่งที่ระบุในรายการ";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_SET_LAST"] = "กำหนดไอเท็มอันสุดท้ายในรายการ";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_SET_RANDOM"] = "กำหนดไอเท็มแบบสุ่มในรายการ";
Blockly.Msg["LISTS_SORT_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#sorting-a-list";
Blockly.Msg["LISTS_SORT_ORDER_ASCENDING"] = "น้อยไปหามาก";
Blockly.Msg["LISTS_SORT_ORDER_DESCENDING"] = "มากไปหาน้อย";
Blockly.Msg["LISTS_SORT_TITLE"] = "เรียงลำดับ %1 %2 %3";
Blockly.Msg["LISTS_SORT_TOOLTIP"] = "เรียงลำดับสำเนาของรายชื่อ";
Blockly.Msg["LISTS_SORT_TYPE_IGNORECASE"] = "ตัวอักษร";
Blockly.Msg["LISTS_SORT_TYPE_NUMERIC"] = "ตัวเลข";
Blockly.Msg["LISTS_SORT_TYPE_TEXT"] = "ตัวอักษร";
Blockly.Msg["LISTS_SPLIT_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#splitting-strings-and-joining-lists";  // untranslated
Blockly.Msg["LISTS_SPLIT_LIST_FROM_TEXT"] = "สร้างรายการจากข้อความ";
Blockly.Msg["LISTS_SPLIT_TEXT_FROM_LIST"] = "สร้างข้อความจากรายการ";
Blockly.Msg["LISTS_SPLIT_TOOLTIP_JOIN"] = "รวมรายการข้อความเป็นข้อความเดียว แบ่งด้วยตัวคั่น";
Blockly.Msg["LISTS_SPLIT_TOOLTIP_SPLIT"] = "แบ่งข้อความเป็นรายการข้อความ แยกแต่ละรายการด้วยตัวคั่น";
Blockly.Msg["LISTS_SPLIT_WITH_DELIMITER"] = "ด้วยตัวคั่น";
Blockly.Msg["LOGIC_BOOLEAN_FALSE"] = "เท็จ";
Blockly.Msg["LOGIC_BOOLEAN_HELPURL"] = "https://github.com/google/blockly/wiki/Logic#values";  // untranslated
Blockly.Msg["LOGIC_BOOLEAN_TOOLTIP"] = "คืนค่าเป็นจริงหรือเท็จ";
Blockly.Msg["LOGIC_BOOLEAN_TRUE"] = "จริง";
Blockly.Msg["LOGIC_COMPARE_HELPURL"] = "https://th.wikipedia.org/wiki/อสมการ";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_EQ"] = "คืนค่าเป็น \"จริง\" ถ้าค่าที่ใส่ทั้งสองค่านั้นเท่ากัน";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_GT"] = "คืนค่าเป็น \"จริง\" ถ้าค่าแรกมากกว่าค่าที่สอง";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_GTE"] = "คืนค่าเป็น \"จริง\" ถ้าค่าแรกมากกว่าหรือเท่ากับค่าที่สอง";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_LT"] = "คืนค่าเป็น \"จริง\" ถ้าค่าแรกน้อยกว่าค่าที่สอง";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_LTE"] = "คืนค่าเป็น \"จริง\" ถ้าค่าแรกน้อยกว่าหรือเท่ากับค่าที่สอง";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_NEQ"] = "คืนค่าเป็น \"จริง\" ถ้าค่าที่ใส่ทั้งสองค่านั้นไม่เท่ากัน";
Blockly.Msg["LOGIC_NEGATE_HELPURL"] = "https://github.com/google/blockly/wiki/Logic#not";  // untranslated
Blockly.Msg["LOGIC_NEGATE_TITLE"] = "ไม่ %1";
Blockly.Msg["LOGIC_NEGATE_TOOLTIP"] = "คืนค่าเป็น \"จริง\" ถ้าค่าที่ใส่เป็นเท็จ คืนค่าเป็น \"เท็จ\" ถ้าค่าที่ใส่เป็นจริง";
Blockly.Msg["LOGIC_NULL"] = "ไม่กำหนด";
Blockly.Msg["LOGIC_NULL_HELPURL"] = "https://en.wikipedia.org/wiki/Nullable_type";  // untranslated
Blockly.Msg["LOGIC_NULL_TOOLTIP"] = "คืนค่า \"ไม่กำหนด\"";
Blockly.Msg["LOGIC_OPERATION_AND"] = "และ";
Blockly.Msg["LOGIC_OPERATION_HELPURL"] = "https://github.com/google/blockly/wiki/Logic#logical-operations";  // untranslated
Blockly.Msg["LOGIC_OPERATION_OR"] = "หรือ";
Blockly.Msg["LOGIC_OPERATION_TOOLTIP_AND"] = "คืนค่าเป็น \"จริง\" ถ้าค่าทั้งสองค่าเป็นจริง";
Blockly.Msg["LOGIC_OPERATION_TOOLTIP_OR"] = "คืนค่าเป็น \"จริง\" ถ้ามีอย่างน้อยหนึ่งค่าที่เป็นจริง";
Blockly.Msg["LOGIC_TERNARY_CONDITION"] = "ทดสอบ";
Blockly.Msg["LOGIC_TERNARY_HELPURL"] = "https://en.wikipedia.org/wiki/%3F:";  // untranslated
Blockly.Msg["LOGIC_TERNARY_IF_FALSE"] = "ถ้า เป็นเท็จ";
Blockly.Msg["LOGIC_TERNARY_IF_TRUE"] = "ถ้า เป็นจริง";
Blockly.Msg["LOGIC_TERNARY_TOOLTIP"] = "ตรวจสอบเงื่อนไขใน \"ทดสอบ\" ถ้าเงื่อนไขเป็นจริง จะคืนค่า \"ถ้า เป็นจริง\" ถ้าเงื่อนไขเป็นเท็จ จะคืนค่า \"ถ้า เป็นเท็จ\"";
Blockly.Msg["MATH_ADDITION_SYMBOL"] = "+";  // untranslated
Blockly.Msg["MATH_ARITHMETIC_HELPURL"] = "https://th.wikipedia.org/wiki/เลขคณิต";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_ADD"] = "คืนค่าผลรวมของตัวเลขทั้งสองจำนวน";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_DIVIDE"] = "คืนค่าผลหารของตัวเลขทั้งสองจำนวน";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_MINUS"] = "คืนค่าผลต่างของตัวเลขทั้งสองจำนวน";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_MULTIPLY"] = "คืนค่าผลคูณของตัวเลขทั้งสองจำนวน";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_POWER"] = "คืนค่าผลการยกกำลัง โดยตัวเลขแรกเป็นฐาน และตัวเลขที่สองเป็นเลขชี้กำลัง";
Blockly.Msg["MATH_ATAN2_HELPURL"] = "https://en.wikipedia.org/wiki/Atan2";
Blockly.Msg["MATH_ATAN2_TITLE"] = "atan2 ของ X:%1 Y:%2";
Blockly.Msg["MATH_ATAN2_TOOLTIP"] = "เปลี่ยนอาร์กแทนเจนต์ของชุด (X, Y) จากองศา 180 เป็น -180.";
Blockly.Msg["MATH_CHANGE_HELPURL"] = "https://en.wikipedia.org/wiki/Programming_idiom#Incrementing_a_counter";
Blockly.Msg["MATH_CHANGE_TITLE"] = "เปลี่ยนค่า %1 เป็น %2";
Blockly.Msg["MATH_CHANGE_TOOLTIP"] = "เพิ่มค่าของตัวแปร \"%1\"";
Blockly.Msg["MATH_CONSTANT_HELPURL"] = "https://th.wikipedia.org/wiki/ค่าคงตัวทางคณิตศาสตร์";
Blockly.Msg["MATH_CONSTANT_TOOLTIP"] = "คืนค่าคงตัวทางคณิตศาสตร์ที่พบบ่อยๆ เช่น π (3.141…), e (2.718…), φ (1.618…), รากที่สอง (1.414…), รากที่ ½ (0.707…), ∞ (อนันต์)";
Blockly.Msg["MATH_CONSTRAIN_HELPURL"] = "https://en.wikipedia.org/wiki/Clamping_(graphics)";  // untranslated
Blockly.Msg["MATH_CONSTRAIN_TITLE"] = "จำกัดค่า %1 ต่ำสุด %2 สูงสุด %3";
Blockly.Msg["MATH_CONSTRAIN_TOOLTIP"] = "จำกัดค่าของตัวเลขให้อยู่ในช่วงที่กำหนด";
Blockly.Msg["MATH_DIVISION_SYMBOL"] = "÷";  // untranslated
Blockly.Msg["MATH_IS_DIVISIBLE_BY"] = "หารลงตัว";
Blockly.Msg["MATH_IS_EVEN"] = "เป็นจำนวนคู่";
Blockly.Msg["MATH_IS_NEGATIVE"] = "เป็นเลขติดลบ";
Blockly.Msg["MATH_IS_ODD"] = "เป็นจำนวนคี่";
Blockly.Msg["MATH_IS_POSITIVE"] = "เป็นเลขบวก";
Blockly.Msg["MATH_IS_PRIME"] = "เป็นจำนวนเฉพาะ";
Blockly.Msg["MATH_IS_TOOLTIP"] = "ตรวจว่าตัวเลขเป็นจำนวนคู่ จำนวนคี่ จำนวนเฉพาะ จำนวนเต็ม เลขบวก เลขติดลบ หรือหารด้วยเลขที่กำหนดลงตัวหรือไม่ คืนค่าเป็นจริงหรือเท็จ";
Blockly.Msg["MATH_IS_WHOLE"] = "เป็นเลขจำนวนเต็ม";
Blockly.Msg["MATH_MODULO_HELPURL"] = "https://en.wikipedia.org/wiki/Modulo_operation";
Blockly.Msg["MATH_MODULO_TITLE"] = "เศษของ %1 ÷ %2";
Blockly.Msg["MATH_MODULO_TOOLTIP"] = "คืนค่าเศษที่ได้จากการหารของตัวเลขทั้งสองจำนวน";
Blockly.Msg["MATH_MULTIPLICATION_SYMBOL"] = "×";  // untranslated
Blockly.Msg["MATH_NUMBER_HELPURL"] = "https://th.wikipedia.org/wiki/จำนวน";
Blockly.Msg["MATH_NUMBER_TOOLTIP"] = "จำนวน";
Blockly.Msg["MATH_ONLIST_HELPURL"] = "";  // untranslated
Blockly.Msg["MATH_ONLIST_OPERATOR_AVERAGE"] = "ค่าเฉลี่ยของรายการ";
Blockly.Msg["MATH_ONLIST_OPERATOR_MAX"] = "มากที่สุดในรายการ";
Blockly.Msg["MATH_ONLIST_OPERATOR_MEDIAN"] = "ค่ามัธยฐานของรายการ";
Blockly.Msg["MATH_ONLIST_OPERATOR_MIN"] = "น้อยที่สุดในรายการ";
Blockly.Msg["MATH_ONLIST_OPERATOR_MODE"] = "ฐานนิยมของรายการ";
Blockly.Msg["MATH_ONLIST_OPERATOR_RANDOM"] = "สุ่มรายการ";
Blockly.Msg["MATH_ONLIST_OPERATOR_STD_DEV"] = "ส่วนเบี่ยงเบนมาตรฐานของรายการ";
Blockly.Msg["MATH_ONLIST_OPERATOR_SUM"] = "ผลรวมของรายการ";
Blockly.Msg["MATH_ONLIST_TOOLTIP_AVERAGE"] = "คืนค่าเฉลี่ยของรายการ";
Blockly.Msg["MATH_ONLIST_TOOLTIP_MAX"] = "คืนค่าตัวเลขที่มากที่สุดในรายการ";
Blockly.Msg["MATH_ONLIST_TOOLTIP_MEDIAN"] = "คืนค่ามัธยฐานของรายการ";
Blockly.Msg["MATH_ONLIST_TOOLTIP_MIN"] = "คืนค่าตัวเลขที่น้อยที่สุดในรายการ";
Blockly.Msg["MATH_ONLIST_TOOLTIP_MODE"] = "คืนค่าฐานนิยมของรายการ";
Blockly.Msg["MATH_ONLIST_TOOLTIP_RANDOM"] = "สุ่มคืนค่าสิ่งที่อยู่ในรายการ";
Blockly.Msg["MATH_ONLIST_TOOLTIP_STD_DEV"] = "คืนค่าส่วนเบี่ยงเบนมาตรฐานของรายการ";
Blockly.Msg["MATH_ONLIST_TOOLTIP_SUM"] = "คืนค่าผลรวมของตัวเลขทั้งหมดในรายการ";
Blockly.Msg["MATH_POWER_SYMBOL"] = "^";  // untranslated
Blockly.Msg["MATH_RANDOM_FLOAT_HELPURL"] = "https://en.wikipedia.org/wiki/Random_number_generation";
Blockly.Msg["MATH_RANDOM_FLOAT_TITLE_RANDOM"] = "สุ่มเลขเศษส่วน";
Blockly.Msg["MATH_RANDOM_FLOAT_TOOLTIP"] = "สุ่มเลขเศษส่วน ตั้งแต่ 0.0 แต่ไม่เกิน 1.0";
Blockly.Msg["MATH_RANDOM_INT_HELPURL"] = "https://en.wikipedia.org/wiki/Random_number_generation";
Blockly.Msg["MATH_RANDOM_INT_TITLE"] = "สุ่มเลขจำนวนเต็มตั้งแต่ %1 จนถึง %2";
Blockly.Msg["MATH_RANDOM_INT_TOOLTIP"] = "สุ่มเลขจำนวนเต็มจากช่วงที่กำหนด";
Blockly.Msg["MATH_ROUND_HELPURL"] = "https://th.wikipedia.org/wiki/การปัดเศษ";
Blockly.Msg["MATH_ROUND_OPERATOR_ROUND"] = "ปัดเศษ";
Blockly.Msg["MATH_ROUND_OPERATOR_ROUNDDOWN"] = "ปัดเศษลง";
Blockly.Msg["MATH_ROUND_OPERATOR_ROUNDUP"] = "ปัดเศษขึ้น";
Blockly.Msg["MATH_ROUND_TOOLTIP"] = "ปัดเศษของตัวเลขขึ้นหรือลง";
Blockly.Msg["MATH_SINGLE_HELPURL"] = "https://en.wikipedia.org/wiki/Square_root";
Blockly.Msg["MATH_SINGLE_OP_ABSOLUTE"] = "ค่าสัมบูรณ์";
Blockly.Msg["MATH_SINGLE_OP_ROOT"] = "รากที่สอง";
Blockly.Msg["MATH_SINGLE_TOOLTIP_ABS"] = "คืนค่าค่าสัมบูรณ์ของตัวเลข";
Blockly.Msg["MATH_SINGLE_TOOLTIP_EXP"] = "คืนค่า e ยกกำลังด้วยตัวเลข";
Blockly.Msg["MATH_SINGLE_TOOLTIP_LN"] = "คืนค่าลอการิทึมธรรมชาติของตัวเลข";
Blockly.Msg["MATH_SINGLE_TOOLTIP_LOG10"] = "คืนค่าลอการิทึมฐานสิบของตัวเลข";
Blockly.Msg["MATH_SINGLE_TOOLTIP_NEG"] = "คืนค่าติดลบของตัวเลข";
Blockly.Msg["MATH_SINGLE_TOOLTIP_POW10"] = "คืนค่า 10 ยกกำลังด้วยตัวเลข";
Blockly.Msg["MATH_SINGLE_TOOLTIP_ROOT"] = "คืนค่ารากที่สองของตัวเลข";
Blockly.Msg["MATH_SUBTRACTION_SYMBOL"] = "-";  // untranslated
Blockly.Msg["MATH_TRIG_ACOS"] = "acos";  // untranslated
Blockly.Msg["MATH_TRIG_ASIN"] = "asin";  // untranslated
Blockly.Msg["MATH_TRIG_ATAN"] = "atan";  // untranslated
Blockly.Msg["MATH_TRIG_COS"] = "cos";  // untranslated
Blockly.Msg["MATH_TRIG_HELPURL"] = "https://th.wikipedia.org/wiki/ฟังก์ชันตรีโกณมิติ";
Blockly.Msg["MATH_TRIG_SIN"] = "sin";  // untranslated
Blockly.Msg["MATH_TRIG_TAN"] = "tan";  // untranslated
Blockly.Msg["MATH_TRIG_TOOLTIP_ACOS"] = "คืนค่า arccosine ของตัวเลข";
Blockly.Msg["MATH_TRIG_TOOLTIP_ASIN"] = "คืนค่า arcsine ของตัวเลข";
Blockly.Msg["MATH_TRIG_TOOLTIP_ATAN"] = "คืนค่า arctangent ของตัวเลข";
Blockly.Msg["MATH_TRIG_TOOLTIP_COS"] = "คืนค่า cosine ขององศา (ไม่ใช่เรเดียน)";
Blockly.Msg["MATH_TRIG_TOOLTIP_SIN"] = "คืนค่า sine ขององศา (ไม่ใช่เรเดียน)";
Blockly.Msg["MATH_TRIG_TOOLTIP_TAN"] = "คืนค่า tangent ขององศา (ไม่ใช่เรเดียน)";
Blockly.Msg["NEW_COLOUR_VARIABLE"] = "สร้างตัวแปรสี";
Blockly.Msg["NEW_NUMBER_VARIABLE"] = "สร้างตัวแปรจำนวน";
Blockly.Msg["NEW_STRING_VARIABLE"] = "สร้างตัวแปร string";
Blockly.Msg["NEW_VARIABLE"] = "สร้างตัวแปร...";
Blockly.Msg["NEW_VARIABLE_TITLE"] = "ชื่อตัวแปรใหม่:";
Blockly.Msg["NEW_VARIABLE_TYPE_TITLE"] = "ชนิดตัวแปรใหม่";
Blockly.Msg["ORDINAL_NUMBER_SUFFIX"] = "";  // untranslated
Blockly.Msg["PROCEDURES_ALLOW_STATEMENTS"] = "ข้อความที่ใช้ได้";
Blockly.Msg["PROCEDURES_BEFORE_PARAMS"] = "ด้วย:";
Blockly.Msg["PROCEDURES_CALLNORETURN_HELPURL"] = "https://th.wikipedia.org/wiki/ซับรู้ทีน";
Blockly.Msg["PROCEDURES_CALLNORETURN_TOOLTIP"] = "เรียกใช้ฟังก์ชันที่สร้างโดยผู้ใช้ \"%1\"";
Blockly.Msg["PROCEDURES_CALLRETURN_HELPURL"] = "https://th.wikipedia.org/wiki/ซับรูทีน";
Blockly.Msg["PROCEDURES_CALLRETURN_TOOLTIP"] = "เรียกใช้ฟังก์ชันที่สร้างโดยผู้ใช้ \"%1\" และใช้ผลลัพธ์ของมัน";
Blockly.Msg["PROCEDURES_CALL_BEFORE_PARAMS"] = "ด้วย:";
Blockly.Msg["PROCEDURES_CREATE_DO"] = "สร้าง \"%1\"";
Blockly.Msg["PROCEDURES_DEFNORETURN_COMMENT"] = "อธิบายฟังก์ชันนี้";
Blockly.Msg["PROCEDURES_DEFNORETURN_DO"] = "";  // untranslated
Blockly.Msg["PROCEDURES_DEFNORETURN_HELPURL"] = "https://en.wikipedia.org/wiki/Subroutine";  // untranslated
Blockly.Msg["PROCEDURES_DEFNORETURN_PROCEDURE"] = "ทำอะไรบางอย่าง";
Blockly.Msg["PROCEDURES_DEFNORETURN_TITLE"] = "ถึง";
Blockly.Msg["PROCEDURES_DEFNORETURN_TOOLTIP"] = "สร้างฟังก์ชันที่ไม่มีผลลัพธ์";
Blockly.Msg["PROCEDURES_DEFRETURN_HELPURL"] = "https://en.wikipedia.org/wiki/Subroutine";  // untranslated
Blockly.Msg["PROCEDURES_DEFRETURN_RETURN"] = "คืนค่า";
Blockly.Msg["PROCEDURES_DEFRETURN_TOOLTIP"] = "สร้างฟังก์ชันที่มีผลลัพธ์";
Blockly.Msg["PROCEDURES_DEF_DUPLICATE_WARNING"] = "ระวัง: ฟังก์ชันนี้มีพารามิเตอร์ที่มีชื่อซ้ำกัน";
Blockly.Msg["PROCEDURES_HIGHLIGHT_DEF"] = "เน้นฟังก์ชันนิยาม";
Blockly.Msg["PROCEDURES_IFRETURN_HELPURL"] = "http://c2.com/cgi/wiki?GuardClause";  // untranslated
Blockly.Msg["PROCEDURES_IFRETURN_TOOLTIP"] = "ถ้ามีค่าเป็นจริง ให้คืนค่าที่สอง";
Blockly.Msg["PROCEDURES_IFRETURN_WARNING"] = "ระวัง: บล็อกนี้ใช้เฉพาะในการสร้างฟังก์ชันเท่านั้น";
Blockly.Msg["PROCEDURES_MUTATORARG_TITLE"] = "ชื่อนำเข้า:";
Blockly.Msg["PROCEDURES_MUTATORARG_TOOLTIP"] = "เพิ่มค่าป้อนเข้าฟังก์ชัน";
Blockly.Msg["PROCEDURES_MUTATORCONTAINER_TITLE"] = "นำเข้า";
Blockly.Msg["PROCEDURES_MUTATORCONTAINER_TOOLTIP"] = "เพิ่ม, ลบ, หรือจัดเรียง ข้อมูลที่ป้อนเข้าฟังก์ชันนี้";
Blockly.Msg["REDO"] = "ทำซ้ำ";
Blockly.Msg["REMOVE_COMMENT"] = "เอาคำอธิบายออก";
Blockly.Msg["RENAME_VARIABLE"] = "เปลี่ยนชื่อตัวแปร...";
Blockly.Msg["RENAME_VARIABLE_TITLE"] = "เปลี่ยนชื่อตัวแปร '%1' ทั้งหมดเป็น:";
Blockly.Msg["TEXT_APPEND_HELPURL"] = "https://github.com/google/blockly/wiki/Text#text-modification";  // untranslated
Blockly.Msg["TEXT_APPEND_TITLE"] = "นำเอา %1 ต่อด้วยข้อความ %2";
Blockly.Msg["TEXT_APPEND_TOOLTIP"] = "ต่อข้อความให้ตัวแปร \"%1\"";
Blockly.Msg["TEXT_CHANGECASE_HELPURL"] = "https://github.com/google/blockly/wiki/Text#adjusting-text-case";  // untranslated
Blockly.Msg["TEXT_CHANGECASE_OPERATOR_LOWERCASE"] = "เปลี่ยนเป็น ตัวพิมพ์เล็ก";
Blockly.Msg["TEXT_CHANGECASE_OPERATOR_TITLECASE"] = "เปลี่ยนเป็น ตัวอักษรแรกเป็นตัวพิมพ์ใหญ่";
Blockly.Msg["TEXT_CHANGECASE_OPERATOR_UPPERCASE"] = "เปลี่ยนเป็น ตัวพิมพ์ใหญ่";
Blockly.Msg["TEXT_CHANGECASE_TOOLTIP"] = "คืนค่าสำเนาของข้อความในกรณีต่างๆ";
Blockly.Msg["TEXT_CHARAT_FIRST"] = "ดึง ตัวอักษรตัวแรก";
Blockly.Msg["TEXT_CHARAT_FROM_END"] = "ดึง ตัวอักษรตัวที่ # จากท้าย";
Blockly.Msg["TEXT_CHARAT_FROM_START"] = "ดึง ตัวอักษรตัวที่";
Blockly.Msg["TEXT_CHARAT_HELPURL"] = "https://github.com/google/blockly/wiki/Text#extracting-text";  // untranslated
Blockly.Msg["TEXT_CHARAT_LAST"] = "ดึง ตัวอักษรตัวสุดท้าย";
Blockly.Msg["TEXT_CHARAT_RANDOM"] = "ถึงตัวอักษรแบบสุ่ม";
Blockly.Msg["TEXT_CHARAT_TAIL"] = "";  // untranslated
Blockly.Msg["TEXT_CHARAT_TITLE"] = "ในข้อความ %1 %2";
Blockly.Msg["TEXT_CHARAT_TOOLTIP"] = "คืนค่าตัวอักษรจากตำแหน่งที่ระบุ";
Blockly.Msg["TEXT_COUNT_HELPURL"] = "https://github.com/google/blockly/wiki/Text#counting-substrings";  // untranslated
Blockly.Msg["TEXT_COUNT_MESSAGE0"] = "นับ %1 ภายใน %2";
Blockly.Msg["TEXT_COUNT_TOOLTIP"] = "นับจำนวนข้อความแรกที่พบอยู่ในข้อความที่สอง";
Blockly.Msg["TEXT_CREATE_JOIN_ITEM_TOOLTIP"] = "เพิ่มรายการเข้าไปในข้อความ";
Blockly.Msg["TEXT_CREATE_JOIN_TITLE_JOIN"] = "รวม";
Blockly.Msg["TEXT_CREATE_JOIN_TOOLTIP"] = "เพิ่ม ลบ หรือจัดเรียงบล็อกข้อความนี้ใหม่";
Blockly.Msg["TEXT_GET_SUBSTRING_END_FROM_END"] = "จนถึง ตัวอักษรที่ # จากท้าย";
Blockly.Msg["TEXT_GET_SUBSTRING_END_FROM_START"] = "จนถึง ตัวอักษรที่";
Blockly.Msg["TEXT_GET_SUBSTRING_END_LAST"] = "จนถึง ตัวอักษรสุดท้าย";
Blockly.Msg["TEXT_GET_SUBSTRING_HELPURL"] = "https://github.com/google/blockly/wiki/Text#extracting-a-region-of-text";  // untranslated
Blockly.Msg["TEXT_GET_SUBSTRING_INPUT_IN_TEXT"] = "ในข้อความ";
Blockly.Msg["TEXT_GET_SUBSTRING_START_FIRST"] = "แยกข้อความย่อยตั้งแต่ ตัวอักษรแรก";
Blockly.Msg["TEXT_GET_SUBSTRING_START_FROM_END"] = "แยกข้อความย่อยตั้งแต่ ตัวอักษรที่ # จากท้าย";
Blockly.Msg["TEXT_GET_SUBSTRING_START_FROM_START"] = "แยกข้อความย่อยตั้งแต่ ตัวอักษรที่";
Blockly.Msg["TEXT_GET_SUBSTRING_TAIL"] = "";  // untranslated
Blockly.Msg["TEXT_GET_SUBSTRING_TOOLTIP"] = "คืนค่าบางส่วนของข้อความ";
Blockly.Msg["TEXT_INDEXOF_HELPURL"] = "https://github.com/google/blockly/wiki/Text#finding-text";  // untranslated
Blockly.Msg["TEXT_INDEXOF_OPERATOR_FIRST"] = "หาข้อความแรกที่พบ";
Blockly.Msg["TEXT_INDEXOF_OPERATOR_LAST"] = "หาข้อความสุดท้ายที่พบ";
Blockly.Msg["TEXT_INDEXOF_TITLE"] = "ในข้อความ %1 %2 %3";
Blockly.Msg["TEXT_INDEXOF_TOOLTIP"] = "คืนค่าตำแหน่งที่พบข้อความแรกอยู่ในข้อความที่สอง คืนค่า %1 ถ้าหาไม่พบ";
Blockly.Msg["TEXT_ISEMPTY_HELPURL"] = "https://github.com/google/blockly/wiki/Text#checking-for-empty-text";  // untranslated
Blockly.Msg["TEXT_ISEMPTY_TITLE"] = "%1 ว่าง";
Blockly.Msg["TEXT_ISEMPTY_TOOLTIP"] = "คืนค่าจริง ถ้าข้อความยังว่างเปล่า";
Blockly.Msg["TEXT_JOIN_HELPURL"] = "https://github.com/google/blockly/wiki/Text#text-creation";  // untranslated
Blockly.Msg["TEXT_JOIN_TITLE_CREATEWITH"] = "สร้างข้อความด้วย";
Blockly.Msg["TEXT_JOIN_TOOLTIP"] = "สร้างข้อความด้วยการรวมจำนวนของรายการเข้าด้วยกัน";
Blockly.Msg["TEXT_LENGTH_HELPURL"] = "https://github.com/google/blockly/wiki/Text#text-modification";  // untranslated
Blockly.Msg["TEXT_LENGTH_TITLE"] = "ความยาวของ %1";
Blockly.Msg["TEXT_LENGTH_TOOLTIP"] = "คืนค่าความยาวของข้อความ (รวมช่องว่าง)";
Blockly.Msg["TEXT_PRINT_HELPURL"] = "https://github.com/google/blockly/wiki/Text#printing-text";  // untranslated
Blockly.Msg["TEXT_PRINT_TITLE"] = "พิมพ์ %1";
Blockly.Msg["TEXT_PRINT_TOOLTIP"] = "พิมพ์ข้อความ ตัวเลข หรือค่าอื่นๆ";
Blockly.Msg["TEXT_PROMPT_HELPURL"] = "https://github.com/google/blockly/wiki/Text#getting-input-from-the-user";  // untranslated
Blockly.Msg["TEXT_PROMPT_TOOLTIP_NUMBER"] = "แสดงหน้าต่างให้ผู้ใช้ใส่ตัวเลข";
Blockly.Msg["TEXT_PROMPT_TOOLTIP_TEXT"] = "แสดงหน้าต่างให้ผู้ใช้ใส่ข้อความ";
Blockly.Msg["TEXT_PROMPT_TYPE_NUMBER"] = "แสดงหน้าต่างตัวเลข";
Blockly.Msg["TEXT_PROMPT_TYPE_TEXT"] = "แสดงหน้าต่างข้อความ";
Blockly.Msg["TEXT_REPLACE_HELPURL"] = "https://github.com/google/blockly/wiki/Text#replacing-substrings";  // untranslated
Blockly.Msg["TEXT_REPLACE_MESSAGE0"] = "แทนที่ %1 ด้วย %2 ใน %3";
Blockly.Msg["TEXT_REPLACE_TOOLTIP"] = "แทนที่ข้อความแรกทั้งหมดที่พบในข้อความที่สอง";
Blockly.Msg["TEXT_REVERSE_HELPURL"] = "https://github.com/google/blockly/wiki/Text#reversing-text";  // untranslated
Blockly.Msg["TEXT_REVERSE_MESSAGE0"] = "เรียง %1 แบบย้อนกลับ";
Blockly.Msg["TEXT_REVERSE_TOOLTIP"] = "เรียงตัวอักษรทั้งหมดของข้อความแบบย้อนกลับ";
Blockly.Msg["TEXT_TEXT_HELPURL"] = "https://th.wikipedia.org/wiki/สายอักขระ";
Blockly.Msg["TEXT_TEXT_TOOLTIP"] = "ตัวหนังสือ คำ หรือข้อความ";
Blockly.Msg["TEXT_TRIM_HELPURL"] = "https://github.com/google/blockly/wiki/Text#trimming-removing-spaces";  // untranslated
Blockly.Msg["TEXT_TRIM_OPERATOR_BOTH"] = "ลบช่องว่างทั้งสองข้างของ";
Blockly.Msg["TEXT_TRIM_OPERATOR_LEFT"] = "ลบช่องว่างด้านหน้าของ";
Blockly.Msg["TEXT_TRIM_OPERATOR_RIGHT"] = "ลบช่องว่างข้างท้ายของ";
Blockly.Msg["TEXT_TRIM_TOOLTIP"] = "คืนค่าสำเนาของข้อความที่ลบเอาช่องว่างหน้าและหลังข้อความออกแล้ว";
Blockly.Msg["TODAY"] = "วันนี้";
Blockly.Msg["UNDO"] = "ย้อนกลับ";
Blockly.Msg["UNNAMED_KEY"] = "ไม่มีชื่อ";
Blockly.Msg["VARIABLES_DEFAULT_NAME"] = "รายการ";
Blockly.Msg["VARIABLES_GET_CREATE_SET"] = "สร้าง \"กำหนด %1\"";
Blockly.Msg["VARIABLES_GET_HELPURL"] = "https://github.com/google/blockly/wiki/Variables#get";  // untranslated
Blockly.Msg["VARIABLES_GET_TOOLTIP"] = "คืนค่าของตัวแปรนี้";
Blockly.Msg["VARIABLES_SET"] = "กำหนด %1 จนถึง %2";
Blockly.Msg["VARIABLES_SET_CREATE_GET"] = "สร้าง \"get %1\"";
Blockly.Msg["VARIABLES_SET_HELPURL"] = "https://github.com/google/blockly/wiki/Variables#set";  // untranslated
Blockly.Msg["VARIABLES_SET_TOOLTIP"] = "กำหนดให้ตัวแปรนี้เท่ากับการป้อนข้อมูล";
Blockly.Msg["VARIABLE_ALREADY_EXISTS"] = "มีตัวแปรชื่อ '%1' อยู่แล้ว";
Blockly.Msg["VARIABLE_ALREADY_EXISTS_FOR_ANOTHER_TYPE"] = "ตัวแปรชื่อ '%1' มีอยู่แล้วสำหรับตัวแปรอื่นของชนิด: '%2'";
Blockly.Msg["WORKSPACE_ARIA_LABEL"] = "พื้นที่ทำงาน Blockly";
Blockly.Msg["WORKSPACE_COMMENT_DEFAULT_TEXT"] = "เขียนอะไรสักอย่าง";
Blockly.Msg["CONTROLS_FOREACH_INPUT_DO"] = Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"];
Blockly.Msg["CONTROLS_FOR_INPUT_DO"] = Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"];
Blockly.Msg["CONTROLS_IF_ELSEIF_TITLE_ELSEIF"] = Blockly.Msg["CONTROLS_IF_MSG_ELSEIF"];
Blockly.Msg["CONTROLS_IF_ELSE_TITLE_ELSE"] = Blockly.Msg["CONTROLS_IF_MSG_ELSE"];
Blockly.Msg["CONTROLS_IF_IF_TITLE_IF"] = Blockly.Msg["CONTROLS_IF_MSG_IF"];
Blockly.Msg["CONTROLS_IF_MSG_THEN"] = Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"];
Blockly.Msg["CONTROLS_WHILEUNTIL_INPUT_DO"] = Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"];
Blockly.Msg["LISTS_CREATE_WITH_ITEM_TITLE"] = Blockly.Msg["VARIABLES_DEFAULT_NAME"];
Blockly.Msg["LISTS_GET_INDEX_HELPURL"] = Blockly.Msg["LISTS_INDEX_OF_HELPURL"];
Blockly.Msg["LISTS_GET_INDEX_INPUT_IN_LIST"] = Blockly.Msg["LISTS_INLIST"];
Blockly.Msg["LISTS_GET_SUBLIST_INPUT_IN_LIST"] = Blockly.Msg["LISTS_INLIST"];
Blockly.Msg["LISTS_INDEX_OF_INPUT_IN_LIST"] = Blockly.Msg["LISTS_INLIST"];
Blockly.Msg["LISTS_SET_INDEX_INPUT_IN_LIST"] = Blockly.Msg["LISTS_INLIST"];
Blockly.Msg["MATH_CHANGE_TITLE_ITEM"] = Blockly.Msg["VARIABLES_DEFAULT_NAME"];
Blockly.Msg["PROCEDURES_DEFRETURN_COMMENT"] = Blockly.Msg["PROCEDURES_DEFNORETURN_COMMENT"];
Blockly.Msg["PROCEDURES_DEFRETURN_DO"] = Blockly.Msg["PROCEDURES_DEFNORETURN_DO"];
Blockly.Msg["PROCEDURES_DEFRETURN_PROCEDURE"] = Blockly.Msg["PROCEDURES_DEFNORETURN_PROCEDURE"];
Blockly.Msg["PROCEDURES_DEFRETURN_TITLE"] = Blockly.Msg["PROCEDURES_DEFNORETURN_TITLE"];
Blockly.Msg["TEXT_APPEND_VARIABLE"] = Blockly.Msg["VARIABLES_DEFAULT_NAME"];
Blockly.Msg["TEXT_CREATE_JOIN_ITEM_TITLE_ITEM"] = Blockly.Msg["VARIABLES_DEFAULT_NAME"];

Blockly.Msg["MATH_HUE"] = "230";
Blockly.Msg["LOOPS_HUE"] = "120";
Blockly.Msg["LISTS_HUE"] = "260";
Blockly.Msg["LOGIC_HUE"] = "210";
Blockly.Msg["VARIABLES_HUE"] = "330";
Blockly.Msg["TEXTS_HUE"] = "160";
Blockly.Msg["PROCEDURES_HUE"] = "290";
Blockly.Msg["COLOUR_HUE"] = "20";
Blockly.Msg["VARIABLES_DYNAMIC_HUE"] = "310";