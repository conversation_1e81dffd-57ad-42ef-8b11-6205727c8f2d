// This file was automatically generated.  Do not modify.

'use strict';

Blockly.Msg["ADD_COMMENT"] = "Yorum Ekle";
Blockly.Msg["CANNOT_DELETE_VARIABLE_PROCEDURE"] = "'%1' de<PERSON><PERSON><PERSON><PERSON><PERSON>, '%2' fonksi<PERSON><PERSON>un tanımının bir parçası olduğu için silinemez";
Blockly.Msg["CHANGE_VALUE_TITLE"] = "Değeri değiştir:";
Blockly.Msg["CLEAN_UP"] = "Blokları Temizle";
Blockly.Msg["COLLAPSED_WARNINGS_WARNING"] = "Daraltılmış bloklar uyarı içerir.";
Blockly.Msg["COLLAPSE_ALL"] = "Blokları Daralt";
Blockly.Msg["COLLAPSE_BLOCK"] = "Bloğu Daralt";
Blockly.Msg["COLOUR_BLEND_COLOUR1"] = "1. renk";
Blockly.Msg["COLOUR_BLEND_COLOUR2"] = "2. renk";
Blockly.Msg["COLOUR_BLEND_HELPURL"] = "https://meyerweb.com/eric/tools/color-blend/#:::rgbp";
Blockly.Msg["COLOUR_BLEND_RATIO"] = "oran";
Blockly.Msg["COLOUR_BLEND_TITLE"] = "karıştır";
Blockly.Msg["COLOUR_BLEND_TOOLTIP"] = "Verilen bir orana (0.0 - 1.0) bağlı olarak iki rengi karıştırır.";
Blockly.Msg["COLOUR_PICKER_HELPURL"] = "https://tr.wikipedia.org/wiki/Renk";
Blockly.Msg["COLOUR_PICKER_TOOLTIP"] = "Paletten bir renk seç.";
Blockly.Msg["COLOUR_RANDOM_HELPURL"] = "http://randomcolour.com";  // untranslated
Blockly.Msg["COLOUR_RANDOM_TITLE"] = "rastgele renk";
Blockly.Msg["COLOUR_RANDOM_TOOLTIP"] = "Rastgele bir renk seç.";
Blockly.Msg["COLOUR_RGB_BLUE"] = "mavi";
Blockly.Msg["COLOUR_RGB_GREEN"] = "yeşil";
Blockly.Msg["COLOUR_RGB_HELPURL"] = "https://www.december.com/html/spec/colorpercompact.html";
Blockly.Msg["COLOUR_RGB_RED"] = "kırmızı";
Blockly.Msg["COLOUR_RGB_TITLE"] = "renk değerleri";
Blockly.Msg["COLOUR_RGB_TOOLTIP"] = "Kırmızı, yeşil ve mavinin belirli miktarıyla bir renk oluştur.  Tüm değerler 0 ile 100 arasında olmalıdır.";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_HELPURL"] = "https://github.com/google/blockly/wiki/Loops#loop-termination-blocks";  // untranslated
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_OPERATOR_BREAK"] = "döngüden çık";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_OPERATOR_CONTINUE"] = "döngünün sonraki adımından devam et";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_TOOLTIP_BREAK"] = "İçeren döngüden çık.";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_TOOLTIP_CONTINUE"] = "Bu döngünün geri kalanını atlayın ve sonraki adım ile devam edin.";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_WARNING"] = "Uyarı: Bu blok yalnızca bir döngü içinde kullanılabilir.";
Blockly.Msg["CONTROLS_FOREACH_HELPURL"] = "https://github.com/google/blockly/wiki/Loops#for-each";  // untranslated
Blockly.Msg["CONTROLS_FOREACH_TITLE"] = "her öğe için %1 listede %2";
Blockly.Msg["CONTROLS_FOREACH_TOOLTIP"] = "Bir listedeki her öğe için  '%1' değişkenini maddeye atayın  ve bundan sonra bazı açıklamalar yapın.";
Blockly.Msg["CONTROLS_FOR_HELPURL"] = "https://github.com/google/blockly/wiki/Loops#count-with";  // untranslated
Blockly.Msg["CONTROLS_FOR_TITLE"] = "ile sayılır %1 %2 den %3 ye, her adımda %4 değişim";
Blockly.Msg["CONTROLS_FOR_TOOLTIP"] = "Başlangıç sayısından bitiş sayısına kadar belirtilen aralık ve belirtilen engeller ile devam eden değerler alan '%1' değişkeni oluştur.";
Blockly.Msg["CONTROLS_IF_ELSEIF_TOOLTIP"] = "If bloğuna bir koşul ekleyin.";
Blockly.Msg["CONTROLS_IF_ELSE_TOOLTIP"] = "If bloğuna kalan durumları \"yakalayan\" bir son ekle.";
Blockly.Msg["CONTROLS_IF_HELPURL"] = "https://github.com/google/blockly/wiki/IfElse";  // untranslated
Blockly.Msg["CONTROLS_IF_IF_TOOLTIP"] = "If bloğuna ekle, kaldır veya yeniden düzenleme yap.";
Blockly.Msg["CONTROLS_IF_MSG_ELSE"] = "değilse";
Blockly.Msg["CONTROLS_IF_MSG_ELSEIF"] = "değilse eğer";
Blockly.Msg["CONTROLS_IF_MSG_IF"] = "eğer";
Blockly.Msg["CONTROLS_IF_TOOLTIP_1"] = "Eğer değişken true, yani gerçekleşmiş ise ardından gelen işlemi yerine getir.";
Blockly.Msg["CONTROLS_IF_TOOLTIP_2"] = "Eğer değişken true, yani gerçekleşiyor ise ilk bloktaki işlemleri yerine getir. Aksi halde ikinci bloktaki işlemleri yerine getir.";
Blockly.Msg["CONTROLS_IF_TOOLTIP_3"] = "Eğer ilk değişken true, yani koşul gerçekleşmiş ise ilk blok içerisindeki işlemi gerçekleştir. Eğer ikinci değişken true ise, ikinci bloktaki işlemi gerçekleştir.";
Blockly.Msg["CONTROLS_IF_TOOLTIP_4"] = "Eğer ilk değer true, yani olumlu ise, ilk bloktaki işlemi gerçekleştir. İlk değer true değil ama ikinci değer true ise, ikinci bloktaki işlemi gerçekleştir. Eğer değerlerin hiçbiri true değil ise son bloktaki işlemi gerçekleştir.";
Blockly.Msg["CONTROLS_REPEAT_HELPURL"] = "https://tr.wikipedia.org/wiki/For_döngüsü";
Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"] = "yap";
Blockly.Msg["CONTROLS_REPEAT_TITLE"] = "%1 kez tekrarla";
Blockly.Msg["CONTROLS_REPEAT_TOOLTIP"] = "Bazı işlemleri birkaç kez yap.";
Blockly.Msg["CONTROLS_WHILEUNTIL_HELPURL"] = "https://github.com/google/blockly/wiki/Loops#repeat";  // untranslated
Blockly.Msg["CONTROLS_WHILEUNTIL_OPERATOR_UNTIL"] = "kadar tekrarla";
Blockly.Msg["CONTROLS_WHILEUNTIL_OPERATOR_WHILE"] = "tekrar ederken";
Blockly.Msg["CONTROLS_WHILEUNTIL_TOOLTIP_UNTIL"] = "Bir değer yanlış olduğunda bazı beyanlarda bulun.";
Blockly.Msg["CONTROLS_WHILEUNTIL_TOOLTIP_WHILE"] = "Bir değer doğru olduğunda bazı beyanlarda bulun.";
Blockly.Msg["DELETE_ALL_BLOCKS"] = "Tüm %1 blokları silinsin mi?";
Blockly.Msg["DELETE_BLOCK"] = "Bloğu Sil";
Blockly.Msg["DELETE_VARIABLE"] = "'%1' değişkenini silmek istiyor musunuz?";
Blockly.Msg["DELETE_VARIABLE_CONFIRMATION"] = "'%2' değişkeninin %1 kullanımını silmek istiyor musunuz?";
Blockly.Msg["DELETE_X_BLOCKS"] = "%1 Bloğunu Sil";
Blockly.Msg["DIALOG_CANCEL"] = "İptal";
Blockly.Msg["DIALOG_OK"] = "Tamam";
Blockly.Msg["DISABLE_BLOCK"] = "Bloğu Devre Dışı Bırak";
Blockly.Msg["DUPLICATE_BLOCK"] = "Yinelenen";
Blockly.Msg["DUPLICATE_COMMENT"] = "Yinelenen Yorum";
Blockly.Msg["ENABLE_BLOCK"] = "Bloğu Etkinleştir";
Blockly.Msg["EXPAND_ALL"] = "Blokları Genişlet";
Blockly.Msg["EXPAND_BLOCK"] = "Bloğu Genişlet";
Blockly.Msg["EXTERNAL_INPUTS"] = "Harici Girişler";
Blockly.Msg["HELP"] = "Yardım";
Blockly.Msg["INLINE_INPUTS"] = "Satır içi Girişler";
Blockly.Msg["LISTS_CREATE_EMPTY_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#create-empty-list";
Blockly.Msg["LISTS_CREATE_EMPTY_TITLE"] = "boş liste oluştur";
Blockly.Msg["LISTS_CREATE_EMPTY_TOOLTIP"] = "Veri kaydı içermeyen 0 uzunluğunda bir liste döndürür";
Blockly.Msg["LISTS_CREATE_WITH_CONTAINER_TITLE_ADD"] = "liste";
Blockly.Msg["LISTS_CREATE_WITH_CONTAINER_TOOLTIP"] = "Bu liste bloğunu yeniden yapılandırmak için bölüm ekleyin, kaldırın veya yeniden sıralayın.";
Blockly.Msg["LISTS_CREATE_WITH_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#create-list-with";  // untranslated
Blockly.Msg["LISTS_CREATE_WITH_INPUT_WITH"] = "ile liste oluştur";
Blockly.Msg["LISTS_CREATE_WITH_ITEM_TOOLTIP"] = "Listeye bir öğe ekleyin.";
Blockly.Msg["LISTS_CREATE_WITH_TOOLTIP"] = "İstediğiniz sayıda öğe içeren bir liste oluşturun.";
Blockly.Msg["LISTS_GET_INDEX_FIRST"] = "ilk";
Blockly.Msg["LISTS_GET_INDEX_FROM_END"] = "# sonundan";
Blockly.Msg["LISTS_GET_INDEX_FROM_START"] = "# Kare";
Blockly.Msg["LISTS_GET_INDEX_GET"] = "al";
Blockly.Msg["LISTS_GET_INDEX_GET_REMOVE"] = "al ve kaldır";
Blockly.Msg["LISTS_GET_INDEX_LAST"] = "son";
Blockly.Msg["LISTS_GET_INDEX_RANDOM"] = "rastgele";
Blockly.Msg["LISTS_GET_INDEX_REMOVE"] = "kaldır";
Blockly.Msg["LISTS_GET_INDEX_TAIL"] = "";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_FIRST"] = "Listedeki ilk öğeyi döndürür.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_FROM"] = "Listede belirtilen konumda bulunan öğeyi döndürür.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_LAST"] = "Listedeki son öğeyi döndürür.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_RANDOM"] = "Listedeki rastgele bir öğeyi döndürür.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_FIRST"] = "Listedeki ilk öğeyi kaldırır ve döndürür.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_FROM"] = "Öğeyi bir listede belirtilen konumda kaldırır ve döndürür.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_LAST"] = "Listedeki son öğeyi kaldırır ve döndürür.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_RANDOM"] = "Listedeki rastgele bir öğeyi kaldırır ve döndürür.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_REMOVE_FIRST"] = "Listedeki ilk öğeyi kaldırır.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_REMOVE_FROM"] = "Listede belirtilen konumda bulunan öğeyi kaldırır.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_REMOVE_LAST"] = "Listedeki son öğeyi kaldırır.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_REMOVE_RANDOM"] = "Listedeki rastgele bir öğeyi kaldırır.";
Blockly.Msg["LISTS_GET_SUBLIST_END_FROM_END"] = "sonuna kadar #";
Blockly.Msg["LISTS_GET_SUBLIST_END_FROM_START"] = "#";
Blockly.Msg["LISTS_GET_SUBLIST_END_LAST"] = "sona";
Blockly.Msg["LISTS_GET_SUBLIST_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#getting-a-sublist";  // untranslated
Blockly.Msg["LISTS_GET_SUBLIST_START_FIRST"] = "ilk önce alt listeyi al";
Blockly.Msg["LISTS_GET_SUBLIST_START_FROM_END"] = "# listesinden alt listeyi al";
Blockly.Msg["LISTS_GET_SUBLIST_START_FROM_START"] = "# listesinden alt liste al";
Blockly.Msg["LISTS_GET_SUBLIST_TAIL"] = "";
Blockly.Msg["LISTS_GET_SUBLIST_TOOLTIP"] = "Listenin belirtilen bölümünün bir kopyasını oluşturur.";
Blockly.Msg["LISTS_INDEX_FROM_END_TOOLTIP"] = "%1 son öğedir.";
Blockly.Msg["LISTS_INDEX_FROM_START_TOOLTIP"] = "%1 ilk öğedir.";
Blockly.Msg["LISTS_INDEX_OF_FIRST"] = "öğenin ilk oluşumunu bul";
Blockly.Msg["LISTS_INDEX_OF_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#getting-items-from-a-list";  // untranslated
Blockly.Msg["LISTS_INDEX_OF_LAST"] = "öğenin son tekrarlamasını bul";
Blockly.Msg["LISTS_INDEX_OF_TOOLTIP"] = "Listedeki öğenin ilk/son oluşumunun dizinini döndürür. Öğe bulunmazsa %1 değerini döndürür.";
Blockly.Msg["LISTS_INLIST"] = "listede";
Blockly.Msg["LISTS_ISEMPTY_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#is-empty";  // untranslated
Blockly.Msg["LISTS_ISEMPTY_TITLE"] = "%1 boş";
Blockly.Msg["LISTS_ISEMPTY_TOOLTIP"] = "Liste boşsa true değerini döndürür.";
Blockly.Msg["LISTS_LENGTH_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#length-of";  // untranslated
Blockly.Msg["LISTS_LENGTH_TITLE"] = "%1 uzunluğu";
Blockly.Msg["LISTS_LENGTH_TOOLTIP"] = "Bir listenin uzunluğunu döndürür.";
Blockly.Msg["LISTS_REPEAT_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#create-list-with";  // untranslated
Blockly.Msg["LISTS_REPEAT_TITLE"] = "%1 tekrarlanan %2 öğeyle liste oluştur";
Blockly.Msg["LISTS_REPEAT_TOOLTIP"] = "Belirtilen sayıda tekrarlanan belirli bir değerden oluşan bir liste oluşturur.";
Blockly.Msg["LISTS_REVERSE_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#reversing-a-list";  // untranslated
Blockly.Msg["LISTS_REVERSE_MESSAGE0"] = "%1 ters çevirin";
Blockly.Msg["LISTS_REVERSE_TOOLTIP"] = "Listenin bir kopyasını ters çevirin.";
Blockly.Msg["LISTS_SET_INDEX_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#in-list--set";  // untranslated
Blockly.Msg["LISTS_SET_INDEX_INPUT_TO"] = "olarak";
Blockly.Msg["LISTS_SET_INDEX_INSERT"] = "yerleştir";
Blockly.Msg["LISTS_SET_INDEX_SET"] = "ayarla";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_INSERT_FIRST"] = "Öğeyi listenin başına ekler.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_INSERT_FROM"] = "Öğeyi bir listede belirtilen konuma ekler.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_INSERT_LAST"] = "Öğeyi listenin sonuna ekleyin.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_INSERT_RANDOM"] = "Öğeyi bir listeye rastgele ekler.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_SET_FIRST"] = "Listedeki ilk öğeyi ayarlar.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_SET_FROM"] = "Öğeyi bir listede belirtilen konuma ayarlar.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_SET_LAST"] = "Listedeki son öğeyi ayarlar.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_SET_RANDOM"] = "Listede rastgele bir öğe ayarlar.";
Blockly.Msg["LISTS_SORT_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#sorting-a-list";
Blockly.Msg["LISTS_SORT_ORDER_ASCENDING"] = "artan";
Blockly.Msg["LISTS_SORT_ORDER_DESCENDING"] = "azalan";
Blockly.Msg["LISTS_SORT_TITLE"] = "sıra %1 %2 %3";
Blockly.Msg["LISTS_SORT_TOOLTIP"] = "Listenin bir kopyasını sıralayın.";
Blockly.Msg["LISTS_SORT_TYPE_IGNORECASE"] = "alfabetik, görmezden gelin";
Blockly.Msg["LISTS_SORT_TYPE_NUMERIC"] = "sayısal";
Blockly.Msg["LISTS_SORT_TYPE_TEXT"] = "alfabetik";
Blockly.Msg["LISTS_SPLIT_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#splitting-strings-and-joining-lists";  // untranslated
Blockly.Msg["LISTS_SPLIT_LIST_FROM_TEXT"] = "metinden liste yap";
Blockly.Msg["LISTS_SPLIT_TEXT_FROM_LIST"] = "listeden metin yap";
Blockly.Msg["LISTS_SPLIT_TOOLTIP_JOIN"] = "Metin listesini bir sınırlayıcı ile ayrılmış tek bir metinde birleştirin.";
Blockly.Msg["LISTS_SPLIT_TOOLTIP_SPLIT"] = "Metni, her bir sınırlayıcıyı kırarak bir metin listesine bölün.";
Blockly.Msg["LISTS_SPLIT_WITH_DELIMITER"] = "sınırlayıcı ile";
Blockly.Msg["LOGIC_BOOLEAN_FALSE"] = "false";
Blockly.Msg["LOGIC_BOOLEAN_HELPURL"] = "https://github.com/google/blockly/wiki/Logic#values";  // untranslated
Blockly.Msg["LOGIC_BOOLEAN_TOOLTIP"] = "True veya false değerini döndürür.";
Blockly.Msg["LOGIC_BOOLEAN_TRUE"] = "true";
Blockly.Msg["LOGIC_COMPARE_HELPURL"] = "https://tr.wikipedia.org/wiki/Eşitsizlikler";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_EQ"] = "Her iki giriş de birbirine eşitse true değerini döndürün.";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_GT"] = "İlk giriş ikinci girişten büyükse true değerini döndürün.";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_GTE"] = "İlk giriş ikinci girişten büyük veya ona eşitse true değerini döndürün.";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_LT"] = "İlk giriş ikinci girişten küçükse true değerini döndürün.";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_LTE"] = "İlk giriş ikinci girişten küçük veya ona eşitse true değerini döndürün.";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_NEQ"] = "Her iki giriş de birbirine eşit değilse true değerini döndürün.";
Blockly.Msg["LOGIC_NEGATE_HELPURL"] = "https://github.com/google/blockly/wiki/Logic#not";  // untranslated
Blockly.Msg["LOGIC_NEGATE_TITLE"] = "%1 değil";
Blockly.Msg["LOGIC_NEGATE_TOOLTIP"] = "Giriş yanlışsa true değerini döndürür. Giriş doğruysa false değerini döndürür.";
Blockly.Msg["LOGIC_NULL"] = "boş";
Blockly.Msg["LOGIC_NULL_HELPURL"] = "https://tr.wikipedia.org/wiki/Sıfırlanabilir_tip";
Blockly.Msg["LOGIC_NULL_TOOLTIP"] = "Boş değerini döndürür.";
Blockly.Msg["LOGIC_OPERATION_AND"] = "ve";
Blockly.Msg["LOGIC_OPERATION_HELPURL"] = "https://github.com/google/blockly/wiki/Logic#logical-operations";  // untranslated
Blockly.Msg["LOGIC_OPERATION_OR"] = "veya";
Blockly.Msg["LOGIC_OPERATION_TOOLTIP_AND"] = "Her iki giriş de doğruysa true döndür.";
Blockly.Msg["LOGIC_OPERATION_TOOLTIP_OR"] = "Girişlerden en az biri doğru olduğunda true değerini döndürün.";
Blockly.Msg["LOGIC_TERNARY_CONDITION"] = "deneme";
Blockly.Msg["LOGIC_TERNARY_HELPURL"] = "https://tr.wikipedia.org/wiki/%3F:";
Blockly.Msg["LOGIC_TERNARY_IF_FALSE"] = "if false";
Blockly.Msg["LOGIC_TERNARY_IF_TRUE"] = "if true";
Blockly.Msg["LOGIC_TERNARY_TOOLTIP"] = "'test' durumunu kontrol edin. Koşul true olursa, 'if true' değerini döndürür; aksi takdirde 'if false' değerini döndürür.";
Blockly.Msg["MATH_ADDITION_SYMBOL"] = "+";
Blockly.Msg["MATH_ARITHMETIC_HELPURL"] = "https://tr.wikipedia.org/wiki/Aritmetik";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_ADD"] = "İki sayının toplamını döndürün.";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_DIVIDE"] = "İki sayının bölümünü döndürün.";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_MINUS"] = "İki sayının farkını döndürün.";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_MULTIPLY"] = "İki sayının çarpımını döndürün.";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_POWER"] = "Yükseltilen ilk sayıyı ikinci sayının gücüne döndürün.";
Blockly.Msg["MATH_ATAN2_HELPURL"] = "https://tr.wikipedia.org/wiki/Atan2";
Blockly.Msg["MATH_ATAN2_TITLE"] = "atan2, X:%1 Y:%2";
Blockly.Msg["MATH_ATAN2_TOOLTIP"] = "-180'den 180'e derece cinsinden nokta (X, Y) arktanjantını döndürün.";
Blockly.Msg["MATH_CHANGE_HELPURL"] = "https://tr.wikipedia.org/wiki/Programlama_deyimi";
Blockly.Msg["MATH_CHANGE_TITLE"] = "%1 %2 göre değiştir";
Blockly.Msg["MATH_CHANGE_TOOLTIP"] = "'%1' değişkenine bir sayı ekle.";
Blockly.Msg["MATH_CONSTANT_HELPURL"] = "https://tr.wikipedia.org/wiki/Matematiksel_sabit";
Blockly.Msg["MATH_CONSTANT_TOOLTIP"] = "Ortak sabitlerden birini döndür: π (3.141…), e (2.718…), φ (1.618…), sqrt(2) (1.414…), sqrt(½) (0.707…), or ∞ (sonsuzluk).";
Blockly.Msg["MATH_CONSTRAIN_HELPURL"] = "https://en.wikipedia.org/wiki/Clamping_(graphics)";  // untranslated
Blockly.Msg["MATH_CONSTRAIN_TITLE"] = "%1 en düşük %2 en yüksek %3 ile sınırla";
Blockly.Msg["MATH_CONSTRAIN_TOOLTIP"] = "Bir sayıyı belirtilen sınırlar arasında (dahil) ile sınırlandırın.";
Blockly.Msg["MATH_DIVISION_SYMBOL"] = "÷";
Blockly.Msg["MATH_IS_DIVISIBLE_BY"] = "bölünebilir";
Blockly.Msg["MATH_IS_EVEN"] = "çift";
Blockly.Msg["MATH_IS_NEGATIVE"] = "negatif";
Blockly.Msg["MATH_IS_ODD"] = "tek";
Blockly.Msg["MATH_IS_POSITIVE"] = "pozitif";
Blockly.Msg["MATH_IS_PRIME"] = "asal";
Blockly.Msg["MATH_IS_TOOLTIP"] = "Bir sayının çift, tek, asal, bütün, pozitif, negatif veya belirli bir sayıya bölünebilir olup olmadığını kontrol edin. True veya false değerini döndürür.";
Blockly.Msg["MATH_IS_WHOLE"] = "bütün";
Blockly.Msg["MATH_MODULO_HELPURL"] = "https://tr.wikipedia.org/wiki/Modulo_işlemi";
Blockly.Msg["MATH_MODULO_TITLE"] = "%1 ÷ %2 geri kalan kısım";
Blockly.Msg["MATH_MODULO_TOOLTIP"] = "Kalanı iki sayıyı bölmekten döndürün.";
Blockly.Msg["MATH_MULTIPLICATION_SYMBOL"] = "x";
Blockly.Msg["MATH_NUMBER_HELPURL"] = "https://tr.wikipedia.org/wiki/Sayı";
Blockly.Msg["MATH_NUMBER_TOOLTIP"] = "Sayı.";
Blockly.Msg["MATH_ONLIST_HELPURL"] = "";
Blockly.Msg["MATH_ONLIST_OPERATOR_AVERAGE"] = "liste ortalaması";
Blockly.Msg["MATH_ONLIST_OPERATOR_MAX"] = "maksimum liste";
Blockly.Msg["MATH_ONLIST_OPERATOR_MEDIAN"] = "listenin medyanı";
Blockly.Msg["MATH_ONLIST_OPERATOR_MIN"] = "listenin en küçüğü";
Blockly.Msg["MATH_ONLIST_OPERATOR_MODE"] = "liste modları";
Blockly.Msg["MATH_ONLIST_OPERATOR_RANDOM"] = "listenin rastgele öğesi";
Blockly.Msg["MATH_ONLIST_OPERATOR_STD_DEV"] = "listenin standart sapması";
Blockly.Msg["MATH_ONLIST_OPERATOR_SUM"] = "listenin toplamı";
Blockly.Msg["MATH_ONLIST_TOOLTIP_AVERAGE"] = "Listedeki sayısal değerlerin ortalamasını (aritmetik ortalama) döndürün.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_MAX"] = "Listedeki en büyük sayıyı döndürün.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_MEDIAN"] = "Listeden ortanca numarayı döndürün.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_MIN"] = "Listedeki en küçük sayıyı döndür.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_MODE"] = "Listedeki en yaygın öğenin bir listesini döndürür.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_RANDOM"] = "Listeden rastgele bir öğe döndürün.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_STD_DEV"] = "Listenin standart sapmasını döndürün.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_SUM"] = "Listedeki tüm sayıların toplamını döndürün.";
Blockly.Msg["MATH_POWER_SYMBOL"] = "üst alma";
Blockly.Msg["MATH_RANDOM_FLOAT_HELPURL"] = "https://tr.wikipedia.org/wiki/Rastgele_sayı_üretimi";
Blockly.Msg["MATH_RANDOM_FLOAT_TITLE_RANDOM"] = "rastgele kesir";
Blockly.Msg["MATH_RANDOM_FLOAT_TOOLTIP"] = "0.0 (dahil) ve 1.0 (hariç) arasında rastgele bir kesir döndürün.";
Blockly.Msg["MATH_RANDOM_INT_HELPURL"] = "https://tr.wikipedia.org/wiki/Rastgele_sayı_üretimi";
Blockly.Msg["MATH_RANDOM_INT_TITLE"] = "%1 ile %2 rastgele tam sayı üretin";
Blockly.Msg["MATH_RANDOM_INT_TOOLTIP"] = "Belirtilen iki sınır arasında rastgele bir tamsayı döndürün.";
Blockly.Msg["MATH_ROUND_HELPURL"] = "https://tr.wikipedia.org/wiki/Yuvarlatma";
Blockly.Msg["MATH_ROUND_OPERATOR_ROUND"] = "yuvarla";
Blockly.Msg["MATH_ROUND_OPERATOR_ROUNDDOWN"] = "aşağı yuvarla";
Blockly.Msg["MATH_ROUND_OPERATOR_ROUNDUP"] = "yukarı yuvarla";
Blockly.Msg["MATH_ROUND_TOOLTIP"] = "Bir sayıyı yukarı veya aşağı yuvarlayın.";
Blockly.Msg["MATH_SINGLE_HELPURL"] = "https://tr.wikipedia.org/wiki/Karekök";
Blockly.Msg["MATH_SINGLE_OP_ABSOLUTE"] = "kesin";
Blockly.Msg["MATH_SINGLE_OP_ROOT"] = "kare kök";
Blockly.Msg["MATH_SINGLE_TOOLTIP_ABS"] = "Bir sayının mutlak değerini döndürür.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_EXP"] = "E'yi bir sayının gücüne döndür.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_LN"] = "Bir sayının doğal logaritmasını döndür.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_LOG10"] = "Bir sayının 10 logaritmasını geri döndür.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_NEG"] = "Bir sayının reddini döndür.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_POW10"] = "10'u sayının gücüne döndür.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_ROOT"] = "Bir sayının karekökünü döndürür.";
Blockly.Msg["MATH_SUBTRACTION_SYMBOL"] = "tire";
Blockly.Msg["MATH_TRIG_ACOS"] = "akosünüs";
Blockly.Msg["MATH_TRIG_ASIN"] = "asinüs";
Blockly.Msg["MATH_TRIG_ATAN"] = "atanjant";
Blockly.Msg["MATH_TRIG_COS"] = "kosünüs";
Blockly.Msg["MATH_TRIG_HELPURL"] = "https://tr.wikipedia.org/wiki/Trigonometrik_fonksiyonlar";
Blockly.Msg["MATH_TRIG_SIN"] = "Sinüs";
Blockly.Msg["MATH_TRIG_TAN"] = "tanjant";
Blockly.Msg["MATH_TRIG_TOOLTIP_ACOS"] = "Bir sayının arkosinini döndürün.";
Blockly.Msg["MATH_TRIG_TOOLTIP_ASIN"] = "Bir sayının ark sinüsünü döndürün.";
Blockly.Msg["MATH_TRIG_TOOLTIP_ATAN"] = "Bir sayının arktanjantını döndürün.";
Blockly.Msg["MATH_TRIG_TOOLTIP_COS"] = "Bir derecenin kosinüsünü döndürün (radyan değil).";
Blockly.Msg["MATH_TRIG_TOOLTIP_SIN"] = "Sinüsü bir derece döndürün (radyan değil).";
Blockly.Msg["MATH_TRIG_TOOLTIP_TAN"] = "Bir derecenin tanjantını döndürün (radyan değil).";
Blockly.Msg["NEW_COLOUR_VARIABLE"] = "Renk değişkeni oluştur...";
Blockly.Msg["NEW_NUMBER_VARIABLE"] = "Sayı değişkeni oluştur...";
Blockly.Msg["NEW_STRING_VARIABLE"] = "Dizi değişkeni oluştur...";
Blockly.Msg["NEW_VARIABLE"] = "Değişken oluştur...";
Blockly.Msg["NEW_VARIABLE_TITLE"] = "Yeni değişken ismi:";
Blockly.Msg["NEW_VARIABLE_TYPE_TITLE"] = "Yeni değişken tipi:";
Blockly.Msg["ORDINAL_NUMBER_SUFFIX"] = "";
Blockly.Msg["PROCEDURES_ALLOW_STATEMENTS"] = "ifadelere izin ver";
Blockly.Msg["PROCEDURES_BEFORE_PARAMS"] = "ile:";
Blockly.Msg["PROCEDURES_CALLNORETURN_HELPURL"] = "https://tr.wikipedia.org/wiki/Alt_program";
Blockly.Msg["PROCEDURES_CALLNORETURN_TOOLTIP"] = "Kullanıcı tanımlı '%1' işlevini çalıştırın.";
Blockly.Msg["PROCEDURES_CALLRETURN_HELPURL"] = "https://tr.wikipedia.org/wiki/Alt_program";
Blockly.Msg["PROCEDURES_CALLRETURN_TOOLTIP"] = "Kullanıcı tanımlı '%1' işlevini çalıştırın ve çıkışını kullanın.";
Blockly.Msg["PROCEDURES_CALL_BEFORE_PARAMS"] = "ile:";
Blockly.Msg["PROCEDURES_CREATE_DO"] = "'%1' oluştur";
Blockly.Msg["PROCEDURES_DEFNORETURN_COMMENT"] = "Bu işlevi açıklayın...";
Blockly.Msg["PROCEDURES_DEFNORETURN_DO"] = "";
Blockly.Msg["PROCEDURES_DEFNORETURN_HELPURL"] = "https://tr.wikipedia.org/wiki/Altyordam";
Blockly.Msg["PROCEDURES_DEFNORETURN_PROCEDURE"] = "bir şey yap";
Blockly.Msg["PROCEDURES_DEFNORETURN_TITLE"] = "-";
Blockly.Msg["PROCEDURES_DEFNORETURN_TOOLTIP"] = "Çıkışı olmayan bir işlev oluşturur.";
Blockly.Msg["PROCEDURES_DEFRETURN_HELPURL"] = "https://tr.wikipedia.org/wiki/Altyordam";
Blockly.Msg["PROCEDURES_DEFRETURN_RETURN"] = "dönüş";
Blockly.Msg["PROCEDURES_DEFRETURN_TOOLTIP"] = "Çıkışa sahip bir işlev oluşturur.";
Blockly.Msg["PROCEDURES_DEF_DUPLICATE_WARNING"] = "Uyarı: Bu işlev yinelenen parametrelere sahiptir.";
Blockly.Msg["PROCEDURES_HIGHLIGHT_DEF"] = "Vurgulama işlevi tanımı";
Blockly.Msg["PROCEDURES_IFRETURN_HELPURL"] = "http://c2.com/cgi/wiki?GuardClause";
Blockly.Msg["PROCEDURES_IFRETURN_TOOLTIP"] = "Bir değer true ise, ikinci bir değer döndürün.";
Blockly.Msg["PROCEDURES_IFRETURN_WARNING"] = "Uyarı: Bu blok yalnızca bir işlev tanımı içinde kullanılabilir.";
Blockly.Msg["PROCEDURES_MUTATORARG_TITLE"] = "giriş adı:";
Blockly.Msg["PROCEDURES_MUTATORARG_TOOLTIP"] = "İşleve bir giriş ekleyin.";
Blockly.Msg["PROCEDURES_MUTATORCONTAINER_TITLE"] = "girişler";
Blockly.Msg["PROCEDURES_MUTATORCONTAINER_TOOLTIP"] = "Bu işleve giriş ekleyin, kaldırın veya yeniden sıralayın.";
Blockly.Msg["REDO"] = "Yinele";
Blockly.Msg["REMOVE_COMMENT"] = "Yorumu Sil";
Blockly.Msg["RENAME_VARIABLE"] = "Değişkeni yeniden adlandır...";
Blockly.Msg["RENAME_VARIABLE_TITLE"] = "Tüm '%1' değişkenini yeniden adlandır:";
Blockly.Msg["TEXT_APPEND_HELPURL"] = "https://github.com/google/blockly/wiki/Text#text-modification";  // untranslated
Blockly.Msg["TEXT_APPEND_TITLE"] = "%1 için %2 metnini ekle.";
Blockly.Msg["TEXT_APPEND_TOOLTIP"] = "'%1' değişkenine bir metin ekleyin.";
Blockly.Msg["TEXT_CHANGECASE_HELPURL"] = "https://github.com/google/blockly/wiki/Text#adjusting-text-case";  // untranslated
Blockly.Msg["TEXT_CHANGECASE_OPERATOR_LOWERCASE"] = "küçük harfe";
Blockly.Msg["TEXT_CHANGECASE_OPERATOR_TITLECASE"] = "Başlık Vakasına";
Blockly.Msg["TEXT_CHANGECASE_OPERATOR_UPPERCASE"] = "ÜST DURUMA";
Blockly.Msg["TEXT_CHANGECASE_TOOLTIP"] = "Metnin bir kopyasını farklı bir durumda döndürün.";
Blockly.Msg["TEXT_CHARAT_FIRST"] = "ilk harfini al";
Blockly.Msg["TEXT_CHARAT_FROM_END"] = "# sona harfleri al";
Blockly.Msg["TEXT_CHARAT_FROM_START"] = "# harfini al";
Blockly.Msg["TEXT_CHARAT_HELPURL"] = "https://github.com/google/blockly/wiki/Text#extracting-text";  // untranslated
Blockly.Msg["TEXT_CHARAT_LAST"] = "son harfi al";
Blockly.Msg["TEXT_CHARAT_RANDOM"] = "rastgele harf al";
Blockly.Msg["TEXT_CHARAT_TAIL"] = "";
Blockly.Msg["TEXT_CHARAT_TITLE"] = "%1 içinde %2";
Blockly.Msg["TEXT_CHARAT_TOOLTIP"] = "Belirtilen konumdaki harfi döndürür.";
Blockly.Msg["TEXT_COUNT_HELPURL"] = "https://github.com/google/blockly/wiki/Text#counting-substrings";
Blockly.Msg["TEXT_COUNT_MESSAGE0"] = "%1 içinde %2 say.";
Blockly.Msg["TEXT_COUNT_TOOLTIP"] = "Bazı metnin başka bir metnin içinde kaç kez oluştuğunu sayın.";
Blockly.Msg["TEXT_CREATE_JOIN_ITEM_TOOLTIP"] = "Metne bir öğe ekleyin.";
Blockly.Msg["TEXT_CREATE_JOIN_TITLE_JOIN"] = "katıl";
Blockly.Msg["TEXT_CREATE_JOIN_TOOLTIP"] = "Bu metin bloğunu yeniden yapılandırmak için bölüm ekleyin, kaldırın veya yeniden sıralayın.";
Blockly.Msg["TEXT_GET_SUBSTRING_END_FROM_END"] = "en başından # harfi";
Blockly.Msg["TEXT_GET_SUBSTRING_END_FROM_START"] = "# harfe";
Blockly.Msg["TEXT_GET_SUBSTRING_END_LAST"] = "son harfe";
Blockly.Msg["TEXT_GET_SUBSTRING_HELPURL"] = "https://github.com/google/blockly/wiki/Text#extracting-a-region-of-text";  // untranslated
Blockly.Msg["TEXT_GET_SUBSTRING_INPUT_IN_TEXT"] = "metinde";
Blockly.Msg["TEXT_GET_SUBSTRING_START_FIRST"] = "ilk harfinden alt dize al";
Blockly.Msg["TEXT_GET_SUBSTRING_START_FROM_END"] = "# harfinden alt dize al";
Blockly.Msg["TEXT_GET_SUBSTRING_START_FROM_START"] = "# harfinden alt dize al";
Blockly.Msg["TEXT_GET_SUBSTRING_TAIL"] = "";
Blockly.Msg["TEXT_GET_SUBSTRING_TOOLTIP"] = "Metnin belirli bir bölümünü döndürür.";
Blockly.Msg["TEXT_INDEXOF_HELPURL"] = "https://github.com/google/blockly/wiki/Text#finding-text";  // untranslated
Blockly.Msg["TEXT_INDEXOF_OPERATOR_FIRST"] = "metnin ilk oluşumunu bul";
Blockly.Msg["TEXT_INDEXOF_OPERATOR_LAST"] = "metnin son tekrarlamasını bul";
Blockly.Msg["TEXT_INDEXOF_TITLE"] = "%1 metni içinde %2 %3";
Blockly.Msg["TEXT_INDEXOF_TOOLTIP"] = "İkinci metindeki ilk metnin ilk/son oluşumunun dizinini döndürür. Metin bulunmazsa %1 değerini döndürür.";
Blockly.Msg["TEXT_ISEMPTY_HELPURL"] = "https://github.com/google/blockly/wiki/Text#checking-for-empty-text";  // untranslated
Blockly.Msg["TEXT_ISEMPTY_TITLE"] = "%1 boş";
Blockly.Msg["TEXT_ISEMPTY_TOOLTIP"] = "Sağlanan metin boşsa true değerini döndürür.";
Blockly.Msg["TEXT_JOIN_HELPURL"] = "https://github.com/google/blockly/wiki/Text#text-creation";  // untranslated
Blockly.Msg["TEXT_JOIN_TITLE_CREATEWITH"] = "ile metin oluştur";
Blockly.Msg["TEXT_JOIN_TOOLTIP"] = "İstediğiniz sayıda öğeyi birleştirerek bir metin parçası oluşturun.";
Blockly.Msg["TEXT_LENGTH_HELPURL"] = "https://github.com/google/blockly/wiki/Text#text-modification";  // untranslated
Blockly.Msg["TEXT_LENGTH_TITLE"] = "%1 uzunluğu";
Blockly.Msg["TEXT_LENGTH_TOOLTIP"] = "Sağlanan metindeki harflerin (boşluklar dahil) sayısını döndürür.";
Blockly.Msg["TEXT_PRINT_HELPURL"] = "https://github.com/google/blockly/wiki/Text#printing-text";  // untranslated
Blockly.Msg["TEXT_PRINT_TITLE"] = "%1 yaz";
Blockly.Msg["TEXT_PRINT_TOOLTIP"] = "Belirtilen metni, sayıyı veya başka bir değeri yazdırın.";
Blockly.Msg["TEXT_PROMPT_HELPURL"] = "https://github.com/google/blockly/wiki/Text#getting-input-from-the-user";  // untranslated
Blockly.Msg["TEXT_PROMPT_TOOLTIP_NUMBER"] = "Bir numara için kullanıcı sor.";
Blockly.Msg["TEXT_PROMPT_TOOLTIP_TEXT"] = "Bazı metinler için kullanıcı sor.";
Blockly.Msg["TEXT_PROMPT_TYPE_NUMBER"] = "mesaj içeren numara istemi";
Blockly.Msg["TEXT_PROMPT_TYPE_TEXT"] = "mesaj içeren metin istemi";
Blockly.Msg["TEXT_REPLACE_HELPURL"] = "https://github.com/google/blockly/wiki/Text#replacing-substrings";  // untranslated
Blockly.Msg["TEXT_REPLACE_MESSAGE0"] = "%1 yerine %3 içindeki %2 ile değiştir";
Blockly.Msg["TEXT_REPLACE_TOOLTIP"] = "Bazı metnin tüm tekrarlarını başka bir metnin içinde değiştirin.";
Blockly.Msg["TEXT_REVERSE_HELPURL"] = "https://github.com/google/blockly/wiki/Text#reversing-text";  // untranslated
Blockly.Msg["TEXT_REVERSE_MESSAGE0"] = "%1 ters çevirin";
Blockly.Msg["TEXT_REVERSE_TOOLTIP"] = "Metindeki karakterlerin sırasını tersine çevirir.";
Blockly.Msg["TEXT_TEXT_HELPURL"] = "https://tr.wikipedia.org/wiki/Dize_(bilgisayar_bilimi)";
Blockly.Msg["TEXT_TEXT_TOOLTIP"] = "Bir harf, kelime veya metin satırı.";
Blockly.Msg["TEXT_TRIM_HELPURL"] = "https://github.com/google/blockly/wiki/Text#trimming-removing-spaces";  // untranslated
Blockly.Msg["TEXT_TRIM_OPERATOR_BOTH"] = "her iki tarafından da kırpın";
Blockly.Msg["TEXT_TRIM_OPERATOR_LEFT"] = "sol tarafındaki boşlukları kırpın";
Blockly.Msg["TEXT_TRIM_OPERATOR_RIGHT"] = "sağ tarafındaki boşlukları kırp";
Blockly.Msg["TEXT_TRIM_TOOLTIP"] = "Bir veya her iki uçtan boşluklar kaldırılmış olarak metnin bir kopyasını döndürün.";
Blockly.Msg["TODAY"] = "Bugün";
Blockly.Msg["UNDO"] = "Geri al";
Blockly.Msg["UNNAMED_KEY"] = "isimsiz";
Blockly.Msg["VARIABLES_DEFAULT_NAME"] = "öge";
Blockly.Msg["VARIABLES_GET_CREATE_SET"] = "'set %1' oluştur";
Blockly.Msg["VARIABLES_GET_HELPURL"] = "https://github.com/google/blockly/wiki/Variables#get";  // untranslated
Blockly.Msg["VARIABLES_GET_TOOLTIP"] = "Bu değişkenin değerini döndürür.";
Blockly.Msg["VARIABLES_SET"] = "%1 %2 ayarla";
Blockly.Msg["VARIABLES_SET_CREATE_GET"] = "'get %1' oluştur";
Blockly.Msg["VARIABLES_SET_HELPURL"] = "https://github.com/google/blockly/wiki/Variables#set";  // untranslated
Blockly.Msg["VARIABLES_SET_TOOLTIP"] = "Bu değişkeni girişe eşit olacak şekilde ayarlar.";
Blockly.Msg["VARIABLE_ALREADY_EXISTS"] = "'%1' isimli değişken adı zaten var.";
Blockly.Msg["VARIABLE_ALREADY_EXISTS_FOR_ANOTHER_TYPE"] = "'%1' isimli değişken '%2' tipli başka bir değişkende tanımlı.";
Blockly.Msg["WORKSPACE_ARIA_LABEL"] = "Blockly Çalışma Alanı";
Blockly.Msg["WORKSPACE_COMMENT_DEFAULT_TEXT"] = "Bir şeyler söyle...";
Blockly.Msg["CONTROLS_FOREACH_INPUT_DO"] = Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"];
Blockly.Msg["CONTROLS_FOR_INPUT_DO"] = Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"];
Blockly.Msg["CONTROLS_IF_ELSEIF_TITLE_ELSEIF"] = Blockly.Msg["CONTROLS_IF_MSG_ELSEIF"];
Blockly.Msg["CONTROLS_IF_ELSE_TITLE_ELSE"] = Blockly.Msg["CONTROLS_IF_MSG_ELSE"];
Blockly.Msg["CONTROLS_IF_IF_TITLE_IF"] = Blockly.Msg["CONTROLS_IF_MSG_IF"];
Blockly.Msg["CONTROLS_IF_MSG_THEN"] = Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"];
Blockly.Msg["CONTROLS_WHILEUNTIL_INPUT_DO"] = Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"];
Blockly.Msg["LISTS_CREATE_WITH_ITEM_TITLE"] = Blockly.Msg["VARIABLES_DEFAULT_NAME"];
Blockly.Msg["LISTS_GET_INDEX_HELPURL"] = Blockly.Msg["LISTS_INDEX_OF_HELPURL"];
Blockly.Msg["LISTS_GET_INDEX_INPUT_IN_LIST"] = Blockly.Msg["LISTS_INLIST"];
Blockly.Msg["LISTS_GET_SUBLIST_INPUT_IN_LIST"] = Blockly.Msg["LISTS_INLIST"];
Blockly.Msg["LISTS_INDEX_OF_INPUT_IN_LIST"] = Blockly.Msg["LISTS_INLIST"];
Blockly.Msg["LISTS_SET_INDEX_INPUT_IN_LIST"] = Blockly.Msg["LISTS_INLIST"];
Blockly.Msg["MATH_CHANGE_TITLE_ITEM"] = Blockly.Msg["VARIABLES_DEFAULT_NAME"];
Blockly.Msg["PROCEDURES_DEFRETURN_COMMENT"] = Blockly.Msg["PROCEDURES_DEFNORETURN_COMMENT"];
Blockly.Msg["PROCEDURES_DEFRETURN_DO"] = Blockly.Msg["PROCEDURES_DEFNORETURN_DO"];
Blockly.Msg["PROCEDURES_DEFRETURN_PROCEDURE"] = Blockly.Msg["PROCEDURES_DEFNORETURN_PROCEDURE"];
Blockly.Msg["PROCEDURES_DEFRETURN_TITLE"] = Blockly.Msg["PROCEDURES_DEFNORETURN_TITLE"];
Blockly.Msg["TEXT_APPEND_VARIABLE"] = Blockly.Msg["VARIABLES_DEFAULT_NAME"];
Blockly.Msg["TEXT_CREATE_JOIN_ITEM_TITLE_ITEM"] = Blockly.Msg["VARIABLES_DEFAULT_NAME"];

Blockly.Msg["MATH_HUE"] = "230";
Blockly.Msg["LOOPS_HUE"] = "120";
Blockly.Msg["LISTS_HUE"] = "260";
Blockly.Msg["LOGIC_HUE"] = "210";
Blockly.Msg["VARIABLES_HUE"] = "330";
Blockly.Msg["TEXTS_HUE"] = "160";
Blockly.Msg["PROCEDURES_HUE"] = "290";
Blockly.Msg["COLOUR_HUE"] = "20";
Blockly.Msg["VARIABLES_DYNAMIC_HUE"] = "310";