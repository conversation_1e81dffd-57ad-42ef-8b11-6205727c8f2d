// This file was automatically generated.  Do not modify.

'use strict';

Blockly.Msg["ADD_COMMENT"] = "Додати коментар";
Blockly.Msg["CANNOT_DELETE_VARIABLE_PROCEDURE"] = "Неможливо видалити змінну '%1', тому що це частина визначення функції '%2'";
Blockly.Msg["CHANGE_VALUE_TITLE"] = "Змінити значення:";
Blockly.Msg["CLEAN_UP"] = "Очистити блоки";
Blockly.Msg["COLLAPSED_WARNINGS_WARNING"] = "Звернуті блоки містять попередження.";
Blockly.Msg["COLLAPSE_ALL"] = "Згорнути блоки";
Blockly.Msg["COLLAPSE_BLOCK"] = "Згорнути блок";
Blockly.Msg["COLOUR_BLEND_COLOUR1"] = "колір 1";
Blockly.Msg["COLOUR_BLEND_COLOUR2"] = "колір 2";
Blockly.Msg["COLOUR_BLEND_HELPURL"] = "https://meyerweb.com/eric/tools/color-blend/#:::rgbp";
Blockly.Msg["COLOUR_BLEND_RATIO"] = "співвідношення";
Blockly.Msg["COLOUR_BLEND_TITLE"] = "змішати";
Blockly.Msg["COLOUR_BLEND_TOOLTIP"] = "Змішує два кольори разом у вказаному співвідношені (0.0 - 1.0).";
Blockly.Msg["COLOUR_PICKER_HELPURL"] = "https://uk.wikipedia.org/wiki/Колір";
Blockly.Msg["COLOUR_PICKER_TOOLTIP"] = "Вибрати колір з палітри.";
Blockly.Msg["COLOUR_RANDOM_HELPURL"] = "http://randomcolour.com";  // untranslated
Blockly.Msg["COLOUR_RANDOM_TITLE"] = "випадковий колір";
Blockly.Msg["COLOUR_RANDOM_TOOLTIP"] = "Вибрати колір навмання.";
Blockly.Msg["COLOUR_RGB_BLUE"] = "синій";
Blockly.Msg["COLOUR_RGB_GREEN"] = "зелений";
Blockly.Msg["COLOUR_RGB_HELPURL"] = "https://www.december.com/html/spec/colorpercompact.html";
Blockly.Msg["COLOUR_RGB_RED"] = "червоний";
Blockly.Msg["COLOUR_RGB_TITLE"] = "колір з";
Blockly.Msg["COLOUR_RGB_TOOLTIP"] = "Створити колір зі вказаними рівнями червоного, зеленого та синього. Усі значення мають бути від 0 до 100.";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_HELPURL"] = "https://github.com/google/blockly/wiki/Loops#loop-termination-blocks";  // untranslated
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_OPERATOR_BREAK"] = "перервати цикл";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_OPERATOR_CONTINUE"] = "продовжити з наступної ітерації циклу";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_TOOLTIP_BREAK"] = "Перервати виконання циклу.";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_TOOLTIP_CONTINUE"] = "Пропустити залишок цього циклу і перейти до виконання наступної ітерації.";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_WARNING"] = "Попередження: цей блок може бути використаний тільки в межах циклу.";
Blockly.Msg["CONTROLS_FOREACH_HELPURL"] = "https://github.com/google/blockly/wiki/Loops#for-each";  // untranslated
Blockly.Msg["CONTROLS_FOREACH_TITLE"] = "для кожного елемента %1 у списку %2";
Blockly.Msg["CONTROLS_FOREACH_TOOLTIP"] = "Для кожного елемента в списку змінна '%1' отримує значення елемента, а потім виконуються певні дії.";
Blockly.Msg["CONTROLS_FOR_HELPURL"] = "https://github.com/google/blockly/wiki/Loops#count-with";  // untranslated
Blockly.Msg["CONTROLS_FOR_TITLE"] = "рахувати з %1 від %2 до %3 через %4";
Blockly.Msg["CONTROLS_FOR_TOOLTIP"] = "Наявна змінна \"%1\" набуває значень від початкового до кінцевого, враховуючи заданий інтервал, і виконуються вказані блоки.";
Blockly.Msg["CONTROLS_IF_ELSEIF_TOOLTIP"] = "Додайте умову до блока 'якщо'.";
Blockly.Msg["CONTROLS_IF_ELSE_TOOLTIP"] = "Додати остаточну, всеосяжну умову до блоку 'якщо'.";
Blockly.Msg["CONTROLS_IF_HELPURL"] = "https://github.com/google/blockly/wiki/IfElse";  // untranslated
Blockly.Msg["CONTROLS_IF_IF_TOOLTIP"] = "Додайте, вилучіть або змініть порядок секцій, щоб переналаштувати цей блок 'якщо'.";
Blockly.Msg["CONTROLS_IF_MSG_ELSE"] = "інакше";
Blockly.Msg["CONTROLS_IF_MSG_ELSEIF"] = "інакше якщо";
Blockly.Msg["CONTROLS_IF_MSG_IF"] = "якщо";
Blockly.Msg["CONTROLS_IF_TOOLTIP_1"] = "Якщо значення істинне, то виконати певні дії.";
Blockly.Msg["CONTROLS_IF_TOOLTIP_2"] = "Якщо значення істинне, то виконується перший блок операторів. В іншому випадку виконується другий блок операторів.";
Blockly.Msg["CONTROLS_IF_TOOLTIP_3"] = "Якщо перше значення істинне, то виконується перший блок операторів. В іншому випадку, якщо друге значення істина, то виконується другий блок операторів.";
Blockly.Msg["CONTROLS_IF_TOOLTIP_4"] = "Якщо перше значення істинне, то виконується перший блок операторів. В іншому випадку, якщо друге значення істинне, то виконується другий блок операторів. Якщо жодне із значень не є істинним, то виконується останній блок операторів.";
Blockly.Msg["CONTROLS_REPEAT_HELPURL"] = "https://uk.wikipedia.org/wiki/Цикл_(програмування)#.D0.A6.D0.B8.D0.BA.D0.BB_.D0.B7_.D0.BB.D1.96.D1.87.D0.B8.D0.BB.D1.8C.D0.BD.D0.B8.D0.BA.D0.BE.D0.BC";
Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"] = "виконати";
Blockly.Msg["CONTROLS_REPEAT_TITLE"] = "повторити %1 разів";
Blockly.Msg["CONTROLS_REPEAT_TOOLTIP"] = "Виконати певні дії декілька разів.";
Blockly.Msg["CONTROLS_WHILEUNTIL_HELPURL"] = "https://github.com/google/blockly/wiki/Loops#repeat";  // untranslated
Blockly.Msg["CONTROLS_WHILEUNTIL_OPERATOR_UNTIL"] = "повторювати, доки не";
Blockly.Msg["CONTROLS_WHILEUNTIL_OPERATOR_WHILE"] = "повторювати поки";
Blockly.Msg["CONTROLS_WHILEUNTIL_TOOLTIP_UNTIL"] = "Поки значення хибне, виконувати певні дії.";
Blockly.Msg["CONTROLS_WHILEUNTIL_TOOLTIP_WHILE"] = "Поки значення істинне, виконувати певні дії.";
Blockly.Msg["DELETE_ALL_BLOCKS"] = "Вилучити всі блоки %1?";
Blockly.Msg["DELETE_BLOCK"] = "Видалити блок";
Blockly.Msg["DELETE_VARIABLE"] = "Вилучити змінну '%1'";
Blockly.Msg["DELETE_VARIABLE_CONFIRMATION"] = "Вилучити %1 використання змінної '%2'?";
Blockly.Msg["DELETE_X_BLOCKS"] = "Видалити %1 блоків";
Blockly.Msg["DIALOG_CANCEL"] = "Скасувати";
Blockly.Msg["DIALOG_OK"] = "Гаразд";
Blockly.Msg["DISABLE_BLOCK"] = "Вимкнути блок";
Blockly.Msg["DUPLICATE_BLOCK"] = "Дублювати";
Blockly.Msg["DUPLICATE_COMMENT"] = "Дублювати примітку";
Blockly.Msg["ENABLE_BLOCK"] = "Увімкнути блок";
Blockly.Msg["EXPAND_ALL"] = "Розгорнути блоки";
Blockly.Msg["EXPAND_BLOCK"] = "Розгорнути блок";
Blockly.Msg["EXTERNAL_INPUTS"] = "Зовнішні входи";
Blockly.Msg["HELP"] = "Довідка";
Blockly.Msg["INLINE_INPUTS"] = "Вбудовані входи";
Blockly.Msg["LISTS_CREATE_EMPTY_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#create-empty-list";
Blockly.Msg["LISTS_CREATE_EMPTY_TITLE"] = "створити порожній список";
Blockly.Msg["LISTS_CREATE_EMPTY_TOOLTIP"] = "Повертає список, довжиною 0, що не містить записів даних";
Blockly.Msg["LISTS_CREATE_WITH_CONTAINER_TITLE_ADD"] = "список";
Blockly.Msg["LISTS_CREATE_WITH_CONTAINER_TOOLTIP"] = "Додайте, вилучіть або змініть порядок секцій для переналаштування блока списку.";
Blockly.Msg["LISTS_CREATE_WITH_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#create-list-with";  // untranslated
Blockly.Msg["LISTS_CREATE_WITH_INPUT_WITH"] = "створити список з";
Blockly.Msg["LISTS_CREATE_WITH_ITEM_TOOLTIP"] = "Додати елемент до списку.";
Blockly.Msg["LISTS_CREATE_WITH_TOOLTIP"] = "Створює список з будь-якою кількістю елементів.";
Blockly.Msg["LISTS_GET_INDEX_FIRST"] = "перший";
Blockly.Msg["LISTS_GET_INDEX_FROM_END"] = "# з кінця";
Blockly.Msg["LISTS_GET_INDEX_FROM_START"] = "#";
Blockly.Msg["LISTS_GET_INDEX_GET"] = "отримати";
Blockly.Msg["LISTS_GET_INDEX_GET_REMOVE"] = "отримати і вилучити";
Blockly.Msg["LISTS_GET_INDEX_LAST"] = "останній";
Blockly.Msg["LISTS_GET_INDEX_RANDOM"] = "випадковий";
Blockly.Msg["LISTS_GET_INDEX_REMOVE"] = "вилучити";
Blockly.Msg["LISTS_GET_INDEX_TAIL"] = "-ий.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_FIRST"] = "Повертає перший елемент списку.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_FROM"] = "Повертає елемент у заданій позиції у списку.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_LAST"] = "Повертає останній елемент списку.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_RANDOM"] = "Повертає випадковий елемент списку.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_FIRST"] = "Видаляє і повертає перший елемент списку.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_FROM"] = "Видаляє і повертає елемент у заданій позиції у списку.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_LAST"] = "Видаляє і повертає останній елемент списку.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_RANDOM"] = "Видаляє і повертає випадковий елемент списоку.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_REMOVE_FIRST"] = "Вилучає перший елемент списку.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_REMOVE_FROM"] = "Вилучає зі списку елемент у вказаній позиції.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_REMOVE_LAST"] = "Вилучає останній елемент списку.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_REMOVE_RANDOM"] = "Вилучає випадковий елемент списку.";
Blockly.Msg["LISTS_GET_SUBLIST_END_FROM_END"] = "до # з кінця";
Blockly.Msg["LISTS_GET_SUBLIST_END_FROM_START"] = "до #";
Blockly.Msg["LISTS_GET_SUBLIST_END_LAST"] = "до останнього";
Blockly.Msg["LISTS_GET_SUBLIST_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#getting-a-sublist";  // untranslated
Blockly.Msg["LISTS_GET_SUBLIST_START_FIRST"] = "отримати вкладений список з першого";
Blockly.Msg["LISTS_GET_SUBLIST_START_FROM_END"] = "отримати вкладений список від # з кінця";
Blockly.Msg["LISTS_GET_SUBLIST_START_FROM_START"] = "отримати вкладений список з #";
Blockly.Msg["LISTS_GET_SUBLIST_TAIL"] = "символу.";
Blockly.Msg["LISTS_GET_SUBLIST_TOOLTIP"] = "Створює копію вказаної частини списку.";
Blockly.Msg["LISTS_INDEX_FROM_END_TOOLTIP"] = "%1 - це останній елемент.";
Blockly.Msg["LISTS_INDEX_FROM_START_TOOLTIP"] = "%1 - це перший елемент.";
Blockly.Msg["LISTS_INDEX_OF_FIRST"] = "знайти перше входження елемента";
Blockly.Msg["LISTS_INDEX_OF_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#getting-items-from-a-list";  // untranslated
Blockly.Msg["LISTS_INDEX_OF_LAST"] = "знайти останнє входження елемента";
Blockly.Msg["LISTS_INDEX_OF_TOOLTIP"] = "Повертає індекс першого/останнього входження елемента у списку. Повертає %1, якщо елемент не знайдено.";
Blockly.Msg["LISTS_INLIST"] = "у списку";
Blockly.Msg["LISTS_ISEMPTY_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#is-empty";  // untranslated
Blockly.Msg["LISTS_ISEMPTY_TITLE"] = "%1 є порожнім";
Blockly.Msg["LISTS_ISEMPTY_TOOLTIP"] = "Повертає істину, якщо список порожній.";
Blockly.Msg["LISTS_LENGTH_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#length-of";  // untranslated
Blockly.Msg["LISTS_LENGTH_TITLE"] = "довжина %1";
Blockly.Msg["LISTS_LENGTH_TOOLTIP"] = "Повертає довжину списку.";
Blockly.Msg["LISTS_REPEAT_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#create-list-with";  // untranslated
Blockly.Msg["LISTS_REPEAT_TITLE"] = "створити список з елемента %1 повтореного %2 разів";
Blockly.Msg["LISTS_REPEAT_TOOLTIP"] = "Створює список, що складається з заданого значення повтореного задану кількість разів.";
Blockly.Msg["LISTS_REVERSE_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#reversing-a-list";  // untranslated
Blockly.Msg["LISTS_REVERSE_MESSAGE0"] = "розвернути %1";
Blockly.Msg["LISTS_REVERSE_TOOLTIP"] = "Змінити порядок копії списку на зворотний.";
Blockly.Msg["LISTS_SET_INDEX_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#in-list--set";  // untranslated
Blockly.Msg["LISTS_SET_INDEX_INPUT_TO"] = "як";
Blockly.Msg["LISTS_SET_INDEX_INSERT"] = "вставити в";
Blockly.Msg["LISTS_SET_INDEX_SET"] = "встановити";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_INSERT_FIRST"] = "Вставляє елемент на початок списку.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_INSERT_FROM"] = "Вставка елемента у вказану позицію списку.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_INSERT_LAST"] = "Додає елемент у кінці списку.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_INSERT_RANDOM"] = "Випадковим чином вставляє елемент у список.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_SET_FIRST"] = "Задає перший елемент списку.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_SET_FROM"] = "Задає елемент списку у вказаній позиції.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_SET_LAST"] = "Задає останній елемент списку.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_SET_RANDOM"] = "Задає випадковий елемент у списку.";
Blockly.Msg["LISTS_SORT_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#sorting-a-list";
Blockly.Msg["LISTS_SORT_ORDER_ASCENDING"] = "за зростанням";
Blockly.Msg["LISTS_SORT_ORDER_DESCENDING"] = "за спаданням";
Blockly.Msg["LISTS_SORT_TITLE"] = "сортувати %3 %1 %2";
Blockly.Msg["LISTS_SORT_TOOLTIP"] = "Сортувати копію списку.";
Blockly.Msg["LISTS_SORT_TYPE_IGNORECASE"] = "за абеткою, ігноруючи регістр";
Blockly.Msg["LISTS_SORT_TYPE_NUMERIC"] = "як числа";
Blockly.Msg["LISTS_SORT_TYPE_TEXT"] = "за абеткою";
Blockly.Msg["LISTS_SPLIT_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#splitting-strings-and-joining-lists";  // untranslated
Blockly.Msg["LISTS_SPLIT_LIST_FROM_TEXT"] = "зробити з тексту список";
Blockly.Msg["LISTS_SPLIT_TEXT_FROM_LIST"] = "зробити зі списку текст";
Blockly.Msg["LISTS_SPLIT_TOOLTIP_JOIN"] = "Злити список текстів у єдиний текст, відокремивши розділювачами.";
Blockly.Msg["LISTS_SPLIT_TOOLTIP_SPLIT"] = "Поділити текст на список текстів, розриваючи на кожному розділювачі.";
Blockly.Msg["LISTS_SPLIT_WITH_DELIMITER"] = "з розділювачем";
Blockly.Msg["LOGIC_BOOLEAN_FALSE"] = "хибність";
Blockly.Msg["LOGIC_BOOLEAN_HELPURL"] = "https://github.com/google/blockly/wiki/Logic#values";  // untranslated
Blockly.Msg["LOGIC_BOOLEAN_TOOLTIP"] = "Повертає значення істина або хибність.";
Blockly.Msg["LOGIC_BOOLEAN_TRUE"] = "істина";
Blockly.Msg["LOGIC_COMPARE_HELPURL"] = "https://uk.wikipedia.org/wiki/Нерівність";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_EQ"] = "Повертає істину, якщо обидва входи рівні один одному.";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_GT"] = "Повертає істину, якщо перше вхідне значення більше, ніж друге.";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_GTE"] = "Повертає істину, якщо перше вхідне значення більше або дорівнює другому.";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_LT"] = "Повертає істину, якщо перше вхідне значення менше, ніж друге.";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_LTE"] = "Повертає істину, якщо перше вхідне значення менше або дорівнює другому.";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_NEQ"] = "Повертає істину, якщо обидва входи не дорівнюють один одному.";
Blockly.Msg["LOGIC_NEGATE_HELPURL"] = "https://github.com/google/blockly/wiki/Logic#not";  // untranslated
Blockly.Msg["LOGIC_NEGATE_TITLE"] = "не %1";
Blockly.Msg["LOGIC_NEGATE_TOOLTIP"] = "Повертає істину, якщо вхідне значення хибне. Повертає хибність, якщо вхідне значення істинне.";
Blockly.Msg["LOGIC_NULL"] = "ніщо";
Blockly.Msg["LOGIC_NULL_HELPURL"] = "https://en.wikipedia.org/wiki/Nullable_type";
Blockly.Msg["LOGIC_NULL_TOOLTIP"] = "Повертає ніщо.";
Blockly.Msg["LOGIC_OPERATION_AND"] = "та";
Blockly.Msg["LOGIC_OPERATION_HELPURL"] = "https://github.com/google/blockly/wiki/Logic#logical-operations";  // untranslated
Blockly.Msg["LOGIC_OPERATION_OR"] = "або";
Blockly.Msg["LOGIC_OPERATION_TOOLTIP_AND"] = "Повертає істину, якщо обидва входи істинні.";
Blockly.Msg["LOGIC_OPERATION_TOOLTIP_OR"] = "Повертає істину, якщо принаймні один з входів істинний.";
Blockly.Msg["LOGIC_TERNARY_CONDITION"] = "тест";
Blockly.Msg["LOGIC_TERNARY_HELPURL"] = "https://en.wikipedia.org/wiki/%3F:";
Blockly.Msg["LOGIC_TERNARY_IF_FALSE"] = "якщо хибність";
Blockly.Msg["LOGIC_TERNARY_IF_TRUE"] = "якщо істина";
Blockly.Msg["LOGIC_TERNARY_TOOLTIP"] = "Перевіряє умову в 'тест'. Якщо умова істинна, то повертає  значення 'якщо істина'; в іншому випадку повертає значення 'якщо хибність'.";
Blockly.Msg["MATH_ADDITION_SYMBOL"] = "+";  // untranslated
Blockly.Msg["MATH_ARITHMETIC_HELPURL"] = "https://uk.wikipedia.org/wiki/Арифметика";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_ADD"] = "Повертає суму двох чисел.";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_DIVIDE"] = "Повертає частку двох чисел.";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_MINUS"] = "Повертає різницю двох чисел.";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_MULTIPLY"] = "Повертає добуток двох чисел.";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_POWER"] = "Повертає перше число, піднесене до степеня, вираженого другим числом.";
Blockly.Msg["MATH_ATAN2_HELPURL"] = "https://en.wikipedia.org/wiki/Atan2";
Blockly.Msg["MATH_ATAN2_TITLE"] = "atan2 по X:%1 Y:%2";
Blockly.Msg["MATH_ATAN2_TOOLTIP"] = "Повертає арктангенс точки (X, Y) у градусах від -180 до 180.";
Blockly.Msg["MATH_CHANGE_HELPURL"] = "https://en.wikipedia.org/wiki/Programming_idiom#Incrementing_a_counter";
Blockly.Msg["MATH_CHANGE_TITLE"] = "змінити %1 на %2";
Blockly.Msg["MATH_CHANGE_TOOLTIP"] = "Додати число до змінної '%1'.";
Blockly.Msg["MATH_CONSTANT_HELPURL"] = "https://uk.wikipedia.org/wiki/Математична_константа";
Blockly.Msg["MATH_CONSTANT_TOOLTIP"] = "Повертає одну з поширених констант: π (3.141...), e (2.718...), φ (1,618...), sqrt(2) (1.414...), sqrt(½) (0.707...) або ∞ (нескінченність).";
Blockly.Msg["MATH_CONSTRAIN_HELPURL"] = "https://en.wikipedia.org/wiki/Clamping_(graphics)";  // untranslated
Blockly.Msg["MATH_CONSTRAIN_TITLE"] = "обмежити %1 від %2 до %3";
Blockly.Msg["MATH_CONSTRAIN_TOOLTIP"] = "Обмежує число вказаними межами (включно).";
Blockly.Msg["MATH_DIVISION_SYMBOL"] = "÷";  // untranslated
Blockly.Msg["MATH_IS_DIVISIBLE_BY"] = "ділиться на";
Blockly.Msg["MATH_IS_EVEN"] = "парне";
Blockly.Msg["MATH_IS_NEGATIVE"] = "від'ємне";
Blockly.Msg["MATH_IS_ODD"] = "непарне";
Blockly.Msg["MATH_IS_POSITIVE"] = "додатне";
Blockly.Msg["MATH_IS_PRIME"] = "просте";
Blockly.Msg["MATH_IS_TOOLTIP"] = "Перевіряє, чи число парне, непарне, просте, ціле, додатне, від'ємне або чи воно ділиться на певне число без остачі. Повертає істину або хибність.";
Blockly.Msg["MATH_IS_WHOLE"] = "ціле";
Blockly.Msg["MATH_MODULO_HELPURL"] = "https://uk.wikipedia.org/wiki/Ділення_з_остачею";
Blockly.Msg["MATH_MODULO_TITLE"] = "остача від %1 ÷ %2";
Blockly.Msg["MATH_MODULO_TOOLTIP"] = "Повертає остачу від ділення двох чисел.";
Blockly.Msg["MATH_MULTIPLICATION_SYMBOL"] = "×";  // untranslated
Blockly.Msg["MATH_NUMBER_HELPURL"] = "https://uk.wikipedia.org/wiki/Число";
Blockly.Msg["MATH_NUMBER_TOOLTIP"] = "Число.";
Blockly.Msg["MATH_ONLIST_HELPURL"] = "http://www.mapleprimes.com/questions/100441-Applying-Function-To-List-Of-Numbers";
Blockly.Msg["MATH_ONLIST_OPERATOR_AVERAGE"] = "середнє списку";
Blockly.Msg["MATH_ONLIST_OPERATOR_MAX"] = "максимум списку";
Blockly.Msg["MATH_ONLIST_OPERATOR_MEDIAN"] = "медіана списку";
Blockly.Msg["MATH_ONLIST_OPERATOR_MIN"] = "мінімум списку";
Blockly.Msg["MATH_ONLIST_OPERATOR_MODE"] = "моди списку";
Blockly.Msg["MATH_ONLIST_OPERATOR_RANDOM"] = "випадковий елемент списку";
Blockly.Msg["MATH_ONLIST_OPERATOR_STD_DEV"] = "стандартне відхилення списку";
Blockly.Msg["MATH_ONLIST_OPERATOR_SUM"] = "сума списку";
Blockly.Msg["MATH_ONLIST_TOOLTIP_AVERAGE"] = "Повертає середнє (арифметичне) числових значень у списку.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_MAX"] = "Повертає найбільше число у списку.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_MEDIAN"] = "Повертає медіану списку.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_MIN"] = "Повертає найменше число у списку.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_MODE"] = "Повертає перелік найпоширеніших елементів у списку.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_RANDOM"] = "Повертає випадковий елемент зі списку.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_STD_DEV"] = "Повертає стандартне відхилення списку.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_SUM"] = "Повертає суму всіх чисел у списку.";
Blockly.Msg["MATH_POWER_SYMBOL"] = "^";  // untranslated
Blockly.Msg["MATH_RANDOM_FLOAT_HELPURL"] = "https://uk.wikipedia.org/wiki/Генерація_випадкових_чисел";
Blockly.Msg["MATH_RANDOM_FLOAT_TITLE_RANDOM"] = "випадковий дріб";
Blockly.Msg["MATH_RANDOM_FLOAT_TOOLTIP"] = "Повертає випадковий дріб від 0,0 (включно) та 1.0 (не включно).";
Blockly.Msg["MATH_RANDOM_INT_HELPURL"] = "https://uk.wikipedia.org/wiki/Генерація_випадкових_чисел";
Blockly.Msg["MATH_RANDOM_INT_TITLE"] = "випадкове ціле число від %1 до %2";
Blockly.Msg["MATH_RANDOM_INT_TOOLTIP"] = "Повертає випадкове ціле число між двома заданими межами включно.";
Blockly.Msg["MATH_ROUND_HELPURL"] = "https://uk.wikipedia.org/wiki/Округлення";
Blockly.Msg["MATH_ROUND_OPERATOR_ROUND"] = "округлити";
Blockly.Msg["MATH_ROUND_OPERATOR_ROUNDDOWN"] = "округлити до меншого";
Blockly.Msg["MATH_ROUND_OPERATOR_ROUNDUP"] = "округлити до більшого";
Blockly.Msg["MATH_ROUND_TOOLTIP"] = "Округлення числа до більшого або до меншого.";
Blockly.Msg["MATH_SINGLE_HELPURL"] = "https://uk.wikipedia.org/wiki/Квадратний_корінь";
Blockly.Msg["MATH_SINGLE_OP_ABSOLUTE"] = "модуль";
Blockly.Msg["MATH_SINGLE_OP_ROOT"] = "квадратний корінь";
Blockly.Msg["MATH_SINGLE_TOOLTIP_ABS"] = "Повертає модуль числа.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_EXP"] = "Повертає e у степені.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_LN"] = "Повертає натуральний логарифм числа.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_LOG10"] = "Повертає десятковий логарифм числа.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_NEG"] = "Повертає протилежне число.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_POW10"] = "Повертає 10 у степені.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_ROOT"] = "Повертає квадратний корінь з числа.";
Blockly.Msg["MATH_SUBTRACTION_SYMBOL"] = "-";  // untranslated
Blockly.Msg["MATH_TRIG_ACOS"] = "acos";  // untranslated
Blockly.Msg["MATH_TRIG_ASIN"] = "asin";  // untranslated
Blockly.Msg["MATH_TRIG_ATAN"] = "atan";  // untranslated
Blockly.Msg["MATH_TRIG_COS"] = "cos";  // untranslated
Blockly.Msg["MATH_TRIG_HELPURL"] = "https://uk.wikipedia.org/wiki/Тригонометричні_функції";
Blockly.Msg["MATH_TRIG_SIN"] = "sin";  // untranslated
Blockly.Msg["MATH_TRIG_TAN"] = "tan";  // untranslated
Blockly.Msg["MATH_TRIG_TOOLTIP_ACOS"] = "Повертає арккосинус числа.";
Blockly.Msg["MATH_TRIG_TOOLTIP_ASIN"] = "Повертає арксинус числа.";
Blockly.Msg["MATH_TRIG_TOOLTIP_ATAN"] = "Повертає арктангенс числа.";
Blockly.Msg["MATH_TRIG_TOOLTIP_COS"] = "Повертає косинус кута в градусах (не в радіанах).";
Blockly.Msg["MATH_TRIG_TOOLTIP_SIN"] = "Повертає синус кута в градусах (не в радіанах).";
Blockly.Msg["MATH_TRIG_TOOLTIP_TAN"] = "Повертає тангенс кута в градусах (не в радіанах).";
Blockly.Msg["NEW_COLOUR_VARIABLE"] = "Створити колірну змінну...";
Blockly.Msg["NEW_NUMBER_VARIABLE"] = "Створити числову змінну...";
Blockly.Msg["NEW_STRING_VARIABLE"] = "Створити рядкову змінну...";
Blockly.Msg["NEW_VARIABLE"] = "Створити змінну...";
Blockly.Msg["NEW_VARIABLE_TITLE"] = "Нова назва змінної:";
Blockly.Msg["NEW_VARIABLE_TYPE_TITLE"] = "Тип нової змінної:";
Blockly.Msg["ORDINAL_NUMBER_SUFFIX"] = "-ий.";
Blockly.Msg["PROCEDURES_ALLOW_STATEMENTS"] = "дозволити дії";
Blockly.Msg["PROCEDURES_BEFORE_PARAMS"] = "з:";
Blockly.Msg["PROCEDURES_CALLNORETURN_HELPURL"] = "https://uk.wikipedia.org/wiki/Підпрограма";
Blockly.Msg["PROCEDURES_CALLNORETURN_TOOLTIP"] = "Запустити користувацьку функцію \"%1\".";
Blockly.Msg["PROCEDURES_CALLRETURN_HELPURL"] = "https://uk.wikipedia.org/wiki/Підпрограма";
Blockly.Msg["PROCEDURES_CALLRETURN_TOOLTIP"] = "Запустити користувацьку функцію \"%1\" і використати її вивід.";
Blockly.Msg["PROCEDURES_CALL_BEFORE_PARAMS"] = "з:";
Blockly.Msg["PROCEDURES_CREATE_DO"] = "Створити \"%1\"";
Blockly.Msg["PROCEDURES_DEFNORETURN_COMMENT"] = "Опишіть цю функцію...";
Blockly.Msg["PROCEDURES_DEFNORETURN_DO"] = "блок тексту";
Blockly.Msg["PROCEDURES_DEFNORETURN_HELPURL"] = "https://uk.wikipedia.org/wiki/Підпрограма";
Blockly.Msg["PROCEDURES_DEFNORETURN_PROCEDURE"] = "щось зробити";
Blockly.Msg["PROCEDURES_DEFNORETURN_TITLE"] = "до";
Blockly.Msg["PROCEDURES_DEFNORETURN_TOOLTIP"] = "Створює функцію без виводу.";
Blockly.Msg["PROCEDURES_DEFRETURN_HELPURL"] = "https://uk.wikipedia.org/wiki/Підпрограма";
Blockly.Msg["PROCEDURES_DEFRETURN_RETURN"] = "повернути";
Blockly.Msg["PROCEDURES_DEFRETURN_TOOLTIP"] = "Створює функцію з виводом.";
Blockly.Msg["PROCEDURES_DEF_DUPLICATE_WARNING"] = "Увага: ця функція має дубльовані параметри.";
Blockly.Msg["PROCEDURES_HIGHLIGHT_DEF"] = "Підсвітити визначення функції";
Blockly.Msg["PROCEDURES_IFRETURN_HELPURL"] = "http://c2.com/cgi/wiki?GuardClause";
Blockly.Msg["PROCEDURES_IFRETURN_TOOLTIP"] = "Якщо значення істинне, то повернути друге значення.";
Blockly.Msg["PROCEDURES_IFRETURN_WARNING"] = "Попередження: цей блок може використовуватися лише в межах визначення функції.";
Blockly.Msg["PROCEDURES_MUTATORARG_TITLE"] = "назва входу:";
Blockly.Msg["PROCEDURES_MUTATORARG_TOOLTIP"] = "Додати до функції вхідні параметри.";
Blockly.Msg["PROCEDURES_MUTATORCONTAINER_TITLE"] = "входи";
Blockly.Msg["PROCEDURES_MUTATORCONTAINER_TOOLTIP"] = "Додайте, вилучіть або змініть порядок вхідних параметрів для цієї функції.";
Blockly.Msg["REDO"] = "Повторити";
Blockly.Msg["REMOVE_COMMENT"] = "Видалити коментар";
Blockly.Msg["RENAME_VARIABLE"] = "Перейменувати змінну...";
Blockly.Msg["RENAME_VARIABLE_TITLE"] = "Перейменувати усі змінні \"%1\" до:";
Blockly.Msg["TEXT_APPEND_HELPURL"] = "https://github.com/google/blockly/wiki/Text#text-modification";  // untranslated
Blockly.Msg["TEXT_APPEND_TITLE"] = "до %1 додати текст %2";
Blockly.Msg["TEXT_APPEND_TOOLTIP"] = "Додати деякий текст до змінної '%1'.";
Blockly.Msg["TEXT_CHANGECASE_HELPURL"] = "https://github.com/google/blockly/wiki/Text#adjusting-text-case";  // untranslated
Blockly.Msg["TEXT_CHANGECASE_OPERATOR_LOWERCASE"] = "до нижнього регістру";
Blockly.Msg["TEXT_CHANGECASE_OPERATOR_TITLECASE"] = "Великі Перші Букви";
Blockly.Msg["TEXT_CHANGECASE_OPERATOR_UPPERCASE"] = "до ВЕРХНЬОГО регістру";
Blockly.Msg["TEXT_CHANGECASE_TOOLTIP"] = "В іншому випадку повертає копію тексту.";
Blockly.Msg["TEXT_CHARAT_FIRST"] = "отримати перший символ";
Blockly.Msg["TEXT_CHARAT_FROM_END"] = "отримати символ # з кінця";
Blockly.Msg["TEXT_CHARAT_FROM_START"] = "отримати символ #";
Blockly.Msg["TEXT_CHARAT_HELPURL"] = "https://github.com/google/blockly/wiki/Text#extracting-text";
Blockly.Msg["TEXT_CHARAT_LAST"] = "отримати останній символ";
Blockly.Msg["TEXT_CHARAT_RANDOM"] = "отримати випадковий символ";
Blockly.Msg["TEXT_CHARAT_TAIL"] = "-ий.";
Blockly.Msg["TEXT_CHARAT_TITLE"] = "з тексту %1 %2";
Blockly.Msg["TEXT_CHARAT_TOOLTIP"] = "Повертає символ у зазначеній позиції.";
Blockly.Msg["TEXT_COUNT_HELPURL"] = "https://github.com/google/blockly/wiki/Text#counting-substrings";  // untranslated
Blockly.Msg["TEXT_COUNT_MESSAGE0"] = "кількість %1 в %2";
Blockly.Msg["TEXT_COUNT_TOOLTIP"] = "Підраховує, скільки разів деякий текст з'являється в іншому тексті.";
Blockly.Msg["TEXT_CREATE_JOIN_ITEM_TOOLTIP"] = "Додати елемент до тексту.";
Blockly.Msg["TEXT_CREATE_JOIN_TITLE_JOIN"] = "приєднати";
Blockly.Msg["TEXT_CREATE_JOIN_TOOLTIP"] = "Додайте, вилучіть або змініть порядок секцій для переналаштування текстового блоку.";
Blockly.Msg["TEXT_GET_SUBSTRING_END_FROM_END"] = "до символу # з кінця";
Blockly.Msg["TEXT_GET_SUBSTRING_END_FROM_START"] = "до символу #";
Blockly.Msg["TEXT_GET_SUBSTRING_END_LAST"] = "до останнього символу";
Blockly.Msg["TEXT_GET_SUBSTRING_HELPURL"] = "https://github.com/google/blockly/wiki/Text#extracting-a-region-of-text";  // untranslated
Blockly.Msg["TEXT_GET_SUBSTRING_INPUT_IN_TEXT"] = "у тексті";
Blockly.Msg["TEXT_GET_SUBSTRING_START_FIRST"] = "отримати підрядок від першого символу";
Blockly.Msg["TEXT_GET_SUBSTRING_START_FROM_END"] = "отримати підрядок від символу # з кінця";
Blockly.Msg["TEXT_GET_SUBSTRING_START_FROM_START"] = "отримати підрядок від символу #";
Blockly.Msg["TEXT_GET_SUBSTRING_TAIL"] = "-ого.";
Blockly.Msg["TEXT_GET_SUBSTRING_TOOLTIP"] = "Повертає заданий фрагмент тексту.";
Blockly.Msg["TEXT_INDEXOF_HELPURL"] = "https://github.com/google/blockly/wiki/Text#finding-text";  // untranslated
Blockly.Msg["TEXT_INDEXOF_OPERATOR_FIRST"] = "знайти перше входження тексту";
Blockly.Msg["TEXT_INDEXOF_OPERATOR_LAST"] = "знайти останнє входження тексту";
Blockly.Msg["TEXT_INDEXOF_TITLE"] = "у тексті %1 %2 %3.";
Blockly.Msg["TEXT_INDEXOF_TOOLTIP"] = "Повертає індекс першого/останнього входження першого тексту в другий. Повертає %1, якщо текст не знайдено.";
Blockly.Msg["TEXT_ISEMPTY_HELPURL"] = "https://github.com/google/blockly/wiki/Text#checking-for-empty-text";  // untranslated
Blockly.Msg["TEXT_ISEMPTY_TITLE"] = "%1 є порожнім";
Blockly.Msg["TEXT_ISEMPTY_TOOLTIP"] = "Повертає істину, якщо вказаний текст порожній.";
Blockly.Msg["TEXT_JOIN_HELPURL"] = "https://github.com/google/blockly/wiki/Text#text-creation";
Blockly.Msg["TEXT_JOIN_TITLE_CREATEWITH"] = "створити текст з";
Blockly.Msg["TEXT_JOIN_TOOLTIP"] = "Створити фрагмент тексту шляхом з'єднування будь-якого числа елементів.";
Blockly.Msg["TEXT_LENGTH_HELPURL"] = "https://github.com/google/blockly/wiki/Text#text-modification";  // untranslated
Blockly.Msg["TEXT_LENGTH_TITLE"] = "довжина %1";
Blockly.Msg["TEXT_LENGTH_TOOLTIP"] = "Повертає число символів (включно з пропусками) у даному тексті.";
Blockly.Msg["TEXT_PRINT_HELPURL"] = "https://github.com/google/blockly/wiki/Text#printing-text";  // untranslated
Blockly.Msg["TEXT_PRINT_TITLE"] = "друк %1";
Blockly.Msg["TEXT_PRINT_TOOLTIP"] = "Надрукувати заданий текст, числа або інші значення.";
Blockly.Msg["TEXT_PROMPT_HELPURL"] = "https://github.com/google/blockly/wiki/Text#getting-input-from-the-user";  // untranslated
Blockly.Msg["TEXT_PROMPT_TOOLTIP_NUMBER"] = "Запитати у користувача число.";
Blockly.Msg["TEXT_PROMPT_TOOLTIP_TEXT"] = "Запитати у користувача деякий текст.";
Blockly.Msg["TEXT_PROMPT_TYPE_NUMBER"] = "запит числа з повідомленням";
Blockly.Msg["TEXT_PROMPT_TYPE_TEXT"] = "запит тексту з повідомленням";
Blockly.Msg["TEXT_REPLACE_HELPURL"] = "https://github.com/google/blockly/wiki/Text#replacing-substrings";  // untranslated
Blockly.Msg["TEXT_REPLACE_MESSAGE0"] = "замінити %1 на %2 в %3";
Blockly.Msg["TEXT_REPLACE_TOOLTIP"] = "Замінює всі входження деякого тексту іншим текстом.";
Blockly.Msg["TEXT_REVERSE_HELPURL"] = "https://github.com/google/blockly/wiki/Text#reversing-text";  // untranslated
Blockly.Msg["TEXT_REVERSE_MESSAGE0"] = "розвернути %1";
Blockly.Msg["TEXT_REVERSE_TOOLTIP"] = "Змінює на протилежний порядок символів у тексті.";
Blockly.Msg["TEXT_TEXT_HELPURL"] = "https://uk.wikipedia.org/wiki/Рядок_(програмування)";
Blockly.Msg["TEXT_TEXT_TOOLTIP"] = "Символ, слово або рядок тексту.";
Blockly.Msg["TEXT_TRIM_HELPURL"] = "https://github.com/google/blockly/wiki/Text#trimming-removing-spaces";  // untranslated
Blockly.Msg["TEXT_TRIM_OPERATOR_BOTH"] = "вилучити крайні пропуски з обох кінців";
Blockly.Msg["TEXT_TRIM_OPERATOR_LEFT"] = "вилучити пропуски з лівого боку";
Blockly.Msg["TEXT_TRIM_OPERATOR_RIGHT"] = "вилучити пропуски з правого боку";
Blockly.Msg["TEXT_TRIM_TOOLTIP"] = "Повертає копію тексту з вилученими пропусками з одного або обох кінців.";
Blockly.Msg["TODAY"] = "Сьогодні";
Blockly.Msg["UNDO"] = "Скасувати";
Blockly.Msg["UNNAMED_KEY"] = "без назви";
Blockly.Msg["VARIABLES_DEFAULT_NAME"] = "елемент";
Blockly.Msg["VARIABLES_GET_CREATE_SET"] = "Створити 'встановити %1'";
Blockly.Msg["VARIABLES_GET_HELPURL"] = "https://github.com/google/blockly/wiki/Variables#get";  // untranslated
Blockly.Msg["VARIABLES_GET_TOOLTIP"] = "Повертає значення цієї змінної.";
Blockly.Msg["VARIABLES_SET"] = "встановити %1 до %2";
Blockly.Msg["VARIABLES_SET_CREATE_GET"] = "Створити 'отримати %1'";
Blockly.Msg["VARIABLES_SET_HELPURL"] = "https://github.com/google/blockly/wiki/Variables#set";  // untranslated
Blockly.Msg["VARIABLES_SET_TOOLTIP"] = "Задає цю змінну рівною входу.";
Blockly.Msg["VARIABLE_ALREADY_EXISTS"] = "Змінна з назвою '%1' вже існує.";
Blockly.Msg["VARIABLE_ALREADY_EXISTS_FOR_ANOTHER_TYPE"] = "Змінна з назвою '%1' вже існує в іншому типі: '%2'.";
Blockly.Msg["WORKSPACE_ARIA_LABEL"] = "Робоча область Blockly";
Blockly.Msg["WORKSPACE_COMMENT_DEFAULT_TEXT"] = "Скажіть щось...";
Blockly.Msg["CONTROLS_FOREACH_INPUT_DO"] = Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"];
Blockly.Msg["CONTROLS_FOR_INPUT_DO"] = Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"];
Blockly.Msg["CONTROLS_IF_ELSEIF_TITLE_ELSEIF"] = Blockly.Msg["CONTROLS_IF_MSG_ELSEIF"];
Blockly.Msg["CONTROLS_IF_ELSE_TITLE_ELSE"] = Blockly.Msg["CONTROLS_IF_MSG_ELSE"];
Blockly.Msg["CONTROLS_IF_IF_TITLE_IF"] = Blockly.Msg["CONTROLS_IF_MSG_IF"];
Blockly.Msg["CONTROLS_IF_MSG_THEN"] = Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"];
Blockly.Msg["CONTROLS_WHILEUNTIL_INPUT_DO"] = Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"];
Blockly.Msg["LISTS_CREATE_WITH_ITEM_TITLE"] = Blockly.Msg["VARIABLES_DEFAULT_NAME"];
Blockly.Msg["LISTS_GET_INDEX_HELPURL"] = Blockly.Msg["LISTS_INDEX_OF_HELPURL"];
Blockly.Msg["LISTS_GET_INDEX_INPUT_IN_LIST"] = Blockly.Msg["LISTS_INLIST"];
Blockly.Msg["LISTS_GET_SUBLIST_INPUT_IN_LIST"] = Blockly.Msg["LISTS_INLIST"];
Blockly.Msg["LISTS_INDEX_OF_INPUT_IN_LIST"] = Blockly.Msg["LISTS_INLIST"];
Blockly.Msg["LISTS_SET_INDEX_INPUT_IN_LIST"] = Blockly.Msg["LISTS_INLIST"];
Blockly.Msg["MATH_CHANGE_TITLE_ITEM"] = Blockly.Msg["VARIABLES_DEFAULT_NAME"];
Blockly.Msg["PROCEDURES_DEFRETURN_COMMENT"] = Blockly.Msg["PROCEDURES_DEFNORETURN_COMMENT"];
Blockly.Msg["PROCEDURES_DEFRETURN_DO"] = Blockly.Msg["PROCEDURES_DEFNORETURN_DO"];
Blockly.Msg["PROCEDURES_DEFRETURN_PROCEDURE"] = Blockly.Msg["PROCEDURES_DEFNORETURN_PROCEDURE"];
Blockly.Msg["PROCEDURES_DEFRETURN_TITLE"] = Blockly.Msg["PROCEDURES_DEFNORETURN_TITLE"];
Blockly.Msg["TEXT_APPEND_VARIABLE"] = Blockly.Msg["VARIABLES_DEFAULT_NAME"];
Blockly.Msg["TEXT_CREATE_JOIN_ITEM_TITLE_ITEM"] = Blockly.Msg["VARIABLES_DEFAULT_NAME"];

Blockly.Msg["MATH_HUE"] = "230";
Blockly.Msg["LOOPS_HUE"] = "120";
Blockly.Msg["LISTS_HUE"] = "260";
Blockly.Msg["LOGIC_HUE"] = "210";
Blockly.Msg["VARIABLES_HUE"] = "330";
Blockly.Msg["TEXTS_HUE"] = "160";
Blockly.Msg["PROCEDURES_HUE"] = "290";
Blockly.Msg["COLOUR_HUE"] = "20";
Blockly.Msg["VARIABLES_DYNAMIC_HUE"] = "310";