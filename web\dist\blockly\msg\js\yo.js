// This file was automatically generated.  Do not modify.

'use strict';

Blockly.Msg["ADD_COMMENT"] = "Ṣe afikun ọrọ iwoye";
Blockly.Msg["CANNOT_DELETE_VARIABLE_PROCEDURE"] = "E ko lee paa Oniruuru rẹ ' %1' nitori wipe o je ara itumọ isise eto yi '%2'";
Blockly.Msg["CHANGE_VALUE_TITLE"] = "Ṣe ayipada iye:";
Blockly.Msg["CLEAN_UP"] = "Nu Bulọọku kuro";
Blockly.Msg["COLLAPSED_WARNINGS_WARNING"] = "Collapsed blocks contain warnings.";  // untranslated
Blockly.Msg["COLLAPSE_ALL"] = "Bi awọn Bulọọku ṣubu";
Blockly.Msg["COLLAPSE_BLOCK"] = "Bi Bulọọku ṣubu";
Blockly.Msg["COLOUR_BLEND_COLOUR1"] = "awọ 1";
Blockly.Msg["COLOUR_BLEND_COLOUR2"] = "awọ 2";
Blockly.Msg["COLOUR_BLEND_HELPURL"] = "https://meyerweb.com/eric/tools/color-blend/#:::rgbp";  // untranslated
Blockly.Msg["COLOUR_BLEND_RATIO"] = "ipin";
Blockly.Msg["COLOUR_BLEND_TITLE"] = "apapọ";
Blockly.Msg["COLOUR_BLEND_TOOLTIP"] = "Da awo meji papo pelu ipin (0.0 - 1.0).";
Blockly.Msg["COLOUR_PICKER_HELPURL"] = "https://en.wikipedia.org/wiki/Color";
Blockly.Msg["COLOUR_PICKER_TOOLTIP"] = "Yan awọ kan lati inu patako awọ.";
Blockly.Msg["COLOUR_RANDOM_HELPURL"] = "http://randomcolour.com";  // untranslated
Blockly.Msg["COLOUR_RANDOM_TITLE"] = "awọ àrìnàkò";
Blockly.Msg["COLOUR_RANDOM_TOOLTIP"] = "Yan awọ kan ni ọna àrìnàkò.";
Blockly.Msg["COLOUR_RGB_BLUE"] = "alawọ omi aro";
Blockly.Msg["COLOUR_RGB_GREEN"] = "alawọ ewe";
Blockly.Msg["COLOUR_RGB_HELPURL"] = "https://www.december.com/html/spec/colorpercompact.html";  // untranslated
Blockly.Msg["COLOUR_RGB_RED"] = "awọ pupu";
Blockly.Msg["COLOUR_RGB_TITLE"] = "awọ pelu:";
Blockly.Msg["COLOUR_RGB_TOOLTIP"] = "Ṣe idasile awọ kan pelu iye awọ pupu, alawọ ewe, ati alawọ omi aro. Gbogbo iye re gbọdọ je laarin 0 and 100.";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_HELPURL"] = "https://github.com/google/blockly/wiki/Loops#loop-termination-blocks";  // untranslated
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_OPERATOR_BREAK"] = "ya kuro ninu lupu";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_OPERATOR_CONTINUE"] = "beere pelu aṣiṣe lupu";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_TOOLTIP_BREAK"] = "Ya kuro ninu akojọ lupu.";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_TOOLTIP_CONTINUE"] = "Ṣe afoda awon lupu to ku yii, kii o si tesiwaju pelu awon aṣiṣe lupu.";
Blockly.Msg["CONTROLS_FLOW_STATEMENTS_WARNING"] = "Ikilo: Bulọọku yii se lo ninu aṣiṣe lupu yii nikan.";
Blockly.Msg["CONTROLS_FOREACH_HELPURL"] = "https://github.com/google/blockly/wiki/Loops#for-each";  // untranslated
Blockly.Msg["CONTROLS_FOREACH_TITLE"] = "fun nigba kọọkan %1 ni akojọ %2";
Blockly.Msg["CONTROLS_FOREACH_TOOLTIP"] = "Fun nkan kọọkan ninu akojọ kan, ṣe eto oriṢiriṢi '%1' si nkan naa, ki o si tun koodu naa ṣe.";
Blockly.Msg["CONTROLS_FOR_HELPURL"] = "https://github.com/google/blockly/wiki/Loops#count-with";  // untranslated
Blockly.Msg["CONTROLS_FOR_TITLE"] = "iyipada %1 lati %2 si %3 fifi kun %4";
Blockly.Msg["CONTROLS_FOR_TOOLTIP"] = "Ni awon oriṣiriṣi '%1' ṣe imulo lori iye kọọkan lati ori nọmba tio beere titi de eyin to pari nọmba, kaa ni pase aarin kan pato. Tun koodu yi se nigba kọọkan:";
Blockly.Msg["CONTROLS_IF_ELSEIF_TOOLTIP"] = "Ṣe afikun si ipo yii bi bulọọku.";
Blockly.Msg["CONTROLS_IF_ELSE_TOOLTIP"] = "Ṣe afikun ipari, mu-gbogbo ipo si bulọọku.";
Blockly.Msg["CONTROLS_IF_HELPURL"] = "https://github.com/google/blockly/wiki/IfElse";  // untranslated
Blockly.Msg["CONTROLS_IF_IF_TOOLTIP"] = "Ṣe afikun, se ayọkuro, tabi se a tun beere abala yii lati se a tun gbejade bulọọku yii.";
Blockly.Msg["CONTROLS_IF_MSG_ELSE"] = "bibẹẹkọ";
Blockly.Msg["CONTROLS_IF_MSG_ELSEIF"] = "bibẹẹkọ bi";
Blockly.Msg["CONTROLS_IF_MSG_IF"] = "bi";
Blockly.Msg["CONTROLS_IF_TOOLTIP_1"] = "Bi iye yii ba je otito, lẹyinna ṣe awọn alaye.";
Blockly.Msg["CONTROLS_IF_TOOLTIP_2"] = "Bi iye yii ba je otito, lẹyinna ṣe alaye bulọọku akọkọ. Bibẹẹkọ, ṣe alaye akọkọ bulọọku keji.";
Blockly.Msg["CONTROLS_IF_TOOLTIP_3"] = "Bi iye akọkọ yii ba je otito, lẹyinna ṣe alaye bulọọku akọkọ. Bibẹẹkọ, Bi iye keji yii ba je otito, ṣe alaye akọkọ bulọọku keji.";
Blockly.Msg["CONTROLS_IF_TOOLTIP_4"] = "Bi iye akọkọ yii ba je otito, lẹyinna ṣe alaye bulọọku akọkọ. Bi iye keji yii ba je otito, ṣe alaye akọkọ bulọọku keji. Bi eyikeyi iye naa ko ba je otito, ṣe alaye akọkọ bulọọku ti o gbeyin.";
Blockly.Msg["CONTROLS_REPEAT_HELPURL"] = "https://en.wikipedia.org/wiki/For_loop";
Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"] = "ṣe";
Blockly.Msg["CONTROLS_REPEAT_TITLE"] = "Iye igba %1 ti tun ṣe";
Blockly.Msg["CONTROLS_REPEAT_TOOLTIP"] = "Ṣe awon alaye ni igba pupo.";
Blockly.Msg["CONTROLS_WHILEUNTIL_HELPURL"] = "https://github.com/google/blockly/wiki/Loops#repeat";  // untranslated
Blockly.Msg["CONTROLS_WHILEUNTIL_OPERATOR_UNTIL"] = "tun ṣe titi ti";
Blockly.Msg["CONTROLS_WHILEUNTIL_OPERATOR_WHILE"] = "tun ṣe nigbati";
Blockly.Msg["CONTROLS_WHILEUNTIL_TOOLTIP_UNTIL"] = "Nigbati awon iye kan ba iro, tun awon koodu kan ṣe.";
Blockly.Msg["CONTROLS_WHILEUNTIL_TOOLTIP_WHILE"] = "Nigbati iye kan ba je otito, tun awon koodu kan ṣe.";
Blockly.Msg["DELETE_ALL_BLOCKS"] = "Paa gbogbo %1 bulọọku rẹ?";
Blockly.Msg["DELETE_BLOCK"] = "Paa Bulọọku rẹ";
Blockly.Msg["DELETE_VARIABLE"] = "Paa awon '%1' Oniruuru rẹ";
Blockly.Msg["DELETE_VARIABLE_CONFIRMATION"] = "Paa %1 lilo '%2' oniruuru rẹ?";
Blockly.Msg["DELETE_X_BLOCKS"] = "Paa %1 awọn Bulọọku rẹ";
Blockly.Msg["DIALOG_CANCEL"] = "Fagilé";
Blockly.Msg["DIALOG_OK"] = "O DARA";
Blockly.Msg["DISABLE_BLOCK"] = "Sọ Bulọọku di alaiṣiṣẹ";
Blockly.Msg["DUPLICATE_BLOCK"] = "Ṣe ẹẹda";
Blockly.Msg["DUPLICATE_COMMENT"] = "Ṣe ẹẹda afikun ọrọ iwoye";
Blockly.Msg["ENABLE_BLOCK"] = "Muu Bulọọku ṣiṣẹ";
Blockly.Msg["EXPAND_ALL"] = "Fẹ awọn Bulọọku";
Blockly.Msg["EXPAND_BLOCK"] = "Fẹ Bulọọku";
Blockly.Msg["EXTERNAL_INPUTS"] = "Awon afikun okeere";
Blockly.Msg["HELP"] = "Iranwọ";
Blockly.Msg["INLINE_INPUTS"] = "Afiku tẹle n tẹle";
Blockly.Msg["LISTS_CREATE_EMPTY_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#create-empty-list";  // untranslated
Blockly.Msg["LISTS_CREATE_EMPTY_TITLE"] = "Ṣẹda akojọ aṣayan tio ṣofo";
Blockly.Msg["LISTS_CREATE_EMPTY_TOOLTIP"] = "Da akojọ pada, ti gigun 0, ko ni awon akosile alaye";
Blockly.Msg["LISTS_CREATE_WITH_CONTAINER_TITLE_ADD"] = "akojọ";
Blockly.Msg["LISTS_CREATE_WITH_CONTAINER_TOOLTIP"] = "Fikun, yọ, tabi yọ, tunṣe awọn apakan lati akojọ bulooku yii.";
Blockly.Msg["LISTS_CREATE_WITH_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#create-list-with";  // untranslated
Blockly.Msg["LISTS_CREATE_WITH_INPUT_WITH"] = "ṣẹda akojọ";
Blockly.Msg["LISTS_CREATE_WITH_ITEM_TOOLTIP"] = "Fi nkan kun akojọ.";
Blockly.Msg["LISTS_CREATE_WITH_TOOLTIP"] = "Ṣẹda akojọ pẹlu nọmba eyikeyi ti awọn akojo.";
Blockly.Msg["LISTS_GET_INDEX_FIRST"] = "àkọ́kọ́";
Blockly.Msg["LISTS_GET_INDEX_FROM_END"] = "# lati opin";
Blockly.Msg["LISTS_GET_INDEX_FROM_START"] = "#";  // untranslated
Blockly.Msg["LISTS_GET_INDEX_GET"] = "gba";
Blockly.Msg["LISTS_GET_INDEX_GET_REMOVE"] = "gba ati yọ";
Blockly.Msg["LISTS_GET_INDEX_LAST"] = "tógbẹ̀yìn";
Blockly.Msg["LISTS_GET_INDEX_RANDOM"] = "àrìnàkò";
Blockly.Msg["LISTS_GET_INDEX_REMOVE"] = "yọ";
Blockly.Msg["LISTS_GET_INDEX_TAIL"] = "";  // untranslated
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_FIRST"] = "Da akojọ akọkọ pada.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_FROM"] = "Returns the item at the specified position in a list.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_LAST"] = "Da akojọ ti o kẹhin pada.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_RANDOM"] = "Da ohun àrìnàkò kan pada ninu akojọ";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_FIRST"] = "Yọ ki o si da akojọ kuro ni akọkọ pada.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_FROM"] = "Yọ ki o si da akojọ naa pada kuro ni ipo kan pato ti o wa.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_LAST"] = "Yọ ki o si da akojọ ti o kẹhin pada";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_GET_REMOVE_RANDOM"] = "Yọ ki o si da akojọ ti o kẹhin àrìnàkò pada";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_REMOVE_FIRST"] = "Da akojọ akọkọ pada.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_REMOVE_FROM"] = "Yọ ki o si da akojọ naa pada kuro ni ipo kan pato ti o wa.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_REMOVE_LAST"] = "Da akojọ ti o kẹhin pada.";
Blockly.Msg["LISTS_GET_INDEX_TOOLTIP_REMOVE_RANDOM"] = "Da ohun àrìnàkò kan pada ninu akojọ";
Blockly.Msg["LISTS_GET_SUBLIST_END_FROM_END"] = "sii # lati opin";
Blockly.Msg["LISTS_GET_SUBLIST_END_FROM_START"] = "sii #";
Blockly.Msg["LISTS_GET_SUBLIST_END_LAST"] = "sii opin";
Blockly.Msg["LISTS_GET_SUBLIST_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#getting-a-sublist";  // untranslated
Blockly.Msg["LISTS_GET_SUBLIST_START_FIRST"] = "gba ipin -akojọ lati akọkọ";
Blockly.Msg["LISTS_GET_SUBLIST_START_FROM_END"] = "gba ipin -akojọ lati # lati opin";
Blockly.Msg["LISTS_GET_SUBLIST_START_FROM_START"] = "gba ipin -akojọ lati #";
Blockly.Msg["LISTS_GET_SUBLIST_TAIL"] = "";  // untranslated
Blockly.Msg["LISTS_GET_SUBLIST_TOOLTIP"] = "Ṣẹda ẹda ti apa kan ti o wa ninu akojọ.";
Blockly.Msg["LISTS_INDEX_FROM_END_TOOLTIP"] = "%1 jẹ ohun ti o kẹhin.";
Blockly.Msg["LISTS_INDEX_FROM_START_TOOLTIP"] = "%1 jẹ ohun akọkọ.";
Blockly.Msg["LISTS_INDEX_OF_FIRST"] = "wa awon nkan akọkọ ti o sele";
Blockly.Msg["LISTS_INDEX_OF_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#getting-items-from-a-list";  // untranslated
Blockly.Msg["LISTS_INDEX_OF_LAST"] = "wa iṣẹlẹ ti o kẹhin ti akojọ";
Blockly.Msg["LISTS_INDEX_OF_TOOLTIP"] = "Da atọka ti akọkọ / iṣẹlẹ to kẹhin ti akojọ. Da %1 akojọ pada ti o ko ba ri.";
Blockly.Msg["LISTS_INLIST"] = "ni akojọ";
Blockly.Msg["LISTS_ISEMPTY_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#is-empty";  // untranslated
Blockly.Msg["LISTS_ISEMPTY_TITLE"] = "%1 je ofo";
Blockly.Msg["LISTS_ISEMPTY_TOOLTIP"] = "Da otitọ pada nigbati akojọ ba ṣofo.";
Blockly.Msg["LISTS_LENGTH_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#length-of";  // untranslated
Blockly.Msg["LISTS_LENGTH_TITLE"] = "gigun ti %1";
Blockly.Msg["LISTS_LENGTH_TOOLTIP"] = "Da gigun ti akojo pada.";
Blockly.Msg["LISTS_REPEAT_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#create-list-with";  // untranslated
Blockly.Msg["LISTS_REPEAT_TITLE"] = "ṣẹda akojọ pẹlu nkan %1 tun ṣe %2 igba";
Blockly.Msg["LISTS_REPEAT_TOOLTIP"] = "Ṣẹda akojọ kan ti o wa fun iye tun nọmba kan pato ti akoko ti a ti yan.";
Blockly.Msg["LISTS_REVERSE_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#reversing-a-list";  // untranslated
Blockly.Msg["LISTS_REVERSE_MESSAGE0"] = "Ṣe iyipada %1";
Blockly.Msg["LISTS_REVERSE_TOOLTIP"] = "Ṣe iyipada ẹda ti akojọ kan.";
Blockly.Msg["LISTS_SET_INDEX_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#in-list--set";  // untranslated
Blockly.Msg["LISTS_SET_INDEX_INPUT_TO"] = "bii";
Blockly.Msg["LISTS_SET_INDEX_INSERT"] = "fi sii ni";
Blockly.Msg["LISTS_SET_INDEX_SET"] = "ṣeto";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_INSERT_FIRST"] = "Fi ohun kan sii ni ibẹrẹ akojọ.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_INSERT_FROM"] = "Fi ohun kan sii ipo kan pato ti a ti yan ni akojọ.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_INSERT_LAST"] = "Fi ohun kan kun si opin akojọ.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_INSERT_RANDOM"] = "Fi ohun kan kun si àrìnàkò akojọ.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_SET_FIRST"] = "Fi ohun kan sii ni ibẹrẹ akojọ.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_SET_FROM"] = "Ṣeto ohun akọkọ sii ipo kan pato ti a ti yan ni akojọ.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_SET_LAST"] = "Fi ohun kan kun si opin akojọ.";
Blockly.Msg["LISTS_SET_INDEX_TOOLTIP_SET_RANDOM"] = "Fi ohun kan kun si àrìnàkò akojọ.";
Blockly.Msg["LISTS_SORT_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#sorting-a-list";
Blockly.Msg["LISTS_SORT_ORDER_ASCENDING"] = "si oke";
Blockly.Msg["LISTS_SORT_ORDER_DESCENDING"] = "si isalẹ";
Blockly.Msg["LISTS_SORT_TITLE"] = "to %1 %2 %3";
Blockly.Msg["LISTS_SORT_TOOLTIP"] = "To ẹda akojọ lẹsẹẹsẹ.";
Blockly.Msg["LISTS_SORT_TYPE_IGNORECASE"] = "awon alfabeti, fojufo irufe";
Blockly.Msg["LISTS_SORT_TYPE_NUMERIC"] = "awọn nọmba";
Blockly.Msg["LISTS_SORT_TYPE_TEXT"] = "awon alfabeti";
Blockly.Msg["LISTS_SPLIT_HELPURL"] = "https://github.com/google/blockly/wiki/Lists#splitting-strings-and-joining-lists";  // untranslated
Blockly.Msg["LISTS_SPLIT_LIST_FROM_TEXT"] = "ṣe akojọ lati inu ọrọ";
Blockly.Msg["LISTS_SPLIT_TEXT_FROM_LIST"] = "ṣe ọrọ lati akojọ";
Blockly.Msg["LISTS_SPLIT_TOOLTIP_JOIN"] = "Darapọ mọ akojọ awọn ọrọ sinu ọrọ kan, ti a pin nipase delimita.";
Blockly.Msg["LISTS_SPLIT_TOOLTIP_SPLIT"] = "Pin ọrọ sinu akojọ awọn ọrọ kan, fọ ni dẹlimita kọọkan.";
Blockly.Msg["LISTS_SPLIT_WITH_DELIMITER"] = "pẹlu dẹlimita";
Blockly.Msg["LOGIC_BOOLEAN_FALSE"] = "irọ";
Blockly.Msg["LOGIC_BOOLEAN_HELPURL"] = "https://github.com/google/blockly/wiki/Logic#values";  // untranslated
Blockly.Msg["LOGIC_BOOLEAN_TOOLTIP"] = "Da pada bi o je otito tabi iro.";
Blockly.Msg["LOGIC_BOOLEAN_TRUE"] = "otitọ";
Blockly.Msg["LOGIC_COMPARE_HELPURL"] = "https://en.wikipedia.org/wiki/Inequality_(mathematics)";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_EQ"] = "Da otito pada b iafikun mejeji ba dogba bakanna.";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_GT"] = "Da otito pada bi afikun akooko ba tobi ju afiku keji lo.";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_GTE"] = "Da otito pada bi afikun akooko ba tobi ju tabi dogba pelu afiku keji lo.";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_LT"] = "Da otito pada bi afikun akooko ba kere ju afiku keji lo.";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_LTE"] = "Da otito pada bi afikun akooko ba kere ju tabi dogba pelu afiku keji lo.";
Blockly.Msg["LOGIC_COMPARE_TOOLTIP_NEQ"] = "Da otito pada bi afikun mejeji ko ba dogba bakanna.";
Blockly.Msg["LOGIC_NEGATE_HELPURL"] = "https://github.com/google/blockly/wiki/Logic#not";  // untranslated
Blockly.Msg["LOGIC_NEGATE_TITLE"] = "kii ṣe %1";
Blockly.Msg["LOGIC_NEGATE_TOOLTIP"] = "Da otitọ pada bi afikun ba je irọ. Da iro pada bi afikun ba je otito.";
Blockly.Msg["LOGIC_NULL"] = "ofo";
Blockly.Msg["LOGIC_NULL_HELPURL"] = "https://en.wikipedia.org/wiki/Nullable_type";  // untranslated
Blockly.Msg["LOGIC_NULL_TOOLTIP"] = "Da ofo pada.";
Blockly.Msg["LOGIC_OPERATION_AND"] = "ati";
Blockly.Msg["LOGIC_OPERATION_HELPURL"] = "https://github.com/google/blockly/wiki/Logic#logical-operations";  // untranslated
Blockly.Msg["LOGIC_OPERATION_OR"] = "tabi";
Blockly.Msg["LOGIC_OPERATION_TOOLTIP_AND"] = "Da otito pada bi afikun mejeji ba je otito.";
Blockly.Msg["LOGIC_OPERATION_TOOLTIP_OR"] = "Da otitọ pada bi o kere ju afikun kan ba je otito.";
Blockly.Msg["LOGIC_TERNARY_CONDITION"] = "idanwo";
Blockly.Msg["LOGIC_TERNARY_HELPURL"] = "https://en.wikipedia.org/wiki/%3F:";  // untranslated
Blockly.Msg["LOGIC_TERNARY_IF_FALSE"] = "bi irọ";
Blockly.Msg["LOGIC_TERNARY_IF_TRUE"] = "bi otitọ";
Blockly.Msg["LOGIC_TERNARY_TOOLTIP"] = "Ṣe ayewo ipo naa ni 'idanwo'. Bi ipo nab a je otito, Da pada 'bi otito' iye; bibẹẹkọ da pada  'bi iro' iye.";
Blockly.Msg["MATH_ADDITION_SYMBOL"] = "+";  // untranslated
Blockly.Msg["MATH_ARITHMETIC_HELPURL"] = "https://yo.wikipedia.org/wiki/Ìṣírò";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_ADD"] = "Da apapo awọn nọmba meji pada.";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_DIVIDE"] = "Da adarọ iye ti awọn nọmba meji pada.";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_MINUS"] = "Da iyatọ awọn nọmba meji naa pada.";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_MULTIPLY"] = "Da abajade awọn nọmba meji naa pada.";
Blockly.Msg["MATH_ARITHMETIC_TOOLTIP_POWER"] = "Da nọmba akọkọ ti a gbe si agbara ti nọmba keji pada.";
Blockly.Msg["MATH_ATAN2_HELPURL"] = "https://en.wikipedia.org/wiki/Atan2";
Blockly.Msg["MATH_ATAN2_TITLE"] = "atan2 X:%1 Y:%2";
Blockly.Msg["MATH_ATAN2_TOOLTIP"] = "Da ojuami arctangent pada (X, Y) ni awon digiri lati -180 si 180.";
Blockly.Msg["MATH_CHANGE_HELPURL"] = "https://en.wikipedia.org/wiki/Programming_idiom#Incrementing_a_counter";
Blockly.Msg["MATH_CHANGE_TITLE"] = "iyipada %1 nipasẹ %2";
Blockly.Msg["MATH_CHANGE_TOOLTIP"] = "Se afiku si nọmba orisirisi '%1'.";
Blockly.Msg["MATH_CONSTANT_HELPURL"] = "https://en.wikipedia.org/wiki/Mathematical_constant";
Blockly.Msg["MATH_CONSTANT_TOOLTIP"] = "Da ọkan ninu awọn aiyipada ti o wọpọ pada: π (3.141…), e (2.718…), φ (1.618…), sqrt(2) (1.414…), sqrt(½) (0.707…), or ∞ (ailopin).";
Blockly.Msg["MATH_CONSTRAIN_HELPURL"] = "https://en.wikipedia.org/wiki/Clamping_(graphics)";  // untranslated
Blockly.Msg["MATH_CONSTRAIN_TITLE"] = "atokọ %1 kukuru %2 giga %3";
Blockly.Msg["MATH_CONSTRAIN_TOOLTIP"] = "Ṣe atokọ nọmba laarin awọn nọmba kukuru ati giga. (ini afikun).";
Blockly.Msg["MATH_DIVISION_SYMBOL"] = "÷";  // untranslated
Blockly.Msg["MATH_IS_DIVISIBLE_BY"] = "je sisee pin pẹlu";
Blockly.Msg["MATH_IS_EVEN"] = "je se e pin";
Blockly.Msg["MATH_IS_NEGATIVE"] = "je ai dara";
Blockly.Msg["MATH_IS_ODD"] = "je ai se e pin";
Blockly.Msg["MATH_IS_POSITIVE"] = "je di dara";
Blockly.Msg["MATH_IS_PRIME"] = "je nọ́mbà àkọ́kọ́";
Blockly.Msg["MATH_IS_TOOLTIP"] = "Ṣe ayẹwo boya nọmba jẹ eyi to se pin, ai se pin, akori, odidi, ti o dara, ti ko dara, tabi ti o ba se e pin pelu nọmba kan. Pada otitọ tabi irọ.";
Blockly.Msg["MATH_IS_WHOLE"] = "je odidi";
Blockly.Msg["MATH_MODULO_HELPURL"] = "https://en.wikipedia.org/wiki/Modulo_operation";
Blockly.Msg["MATH_MODULO_TITLE"] = "iyokù %1 ÷ %2";
Blockly.Msg["MATH_MODULO_TOOLTIP"] = "Da iyokù lati pinpin awọn nọmba meji pada.";
Blockly.Msg["MATH_MULTIPLICATION_SYMBOL"] = "×";  // untranslated
Blockly.Msg["MATH_NUMBER_HELPURL"] = "https://yo.wikipedia.org/wiki/Nọ́mbà";
Blockly.Msg["MATH_NUMBER_TOOLTIP"] = "Nọ́mbà kan.";
Blockly.Msg["MATH_ONLIST_HELPURL"] = "";  // untranslated
Blockly.Msg["MATH_ONLIST_OPERATOR_AVERAGE"] = "idameji akojọ";
Blockly.Msg["MATH_ONLIST_OPERATOR_MAX"] = "akojọ ti o pọ ju";
Blockly.Msg["MATH_ONLIST_OPERATOR_MEDIAN"] = "agbedemeji akojọ";
Blockly.Msg["MATH_ONLIST_OPERATOR_MIN"] = "akojọ ti o kere ju";
Blockly.Msg["MATH_ONLIST_OPERATOR_MODE"] = "awọn ipo ti akojọ";
Blockly.Msg["MATH_ONLIST_OPERATOR_RANDOM"] = "àrìnàkò nkan ti akojọ";
Blockly.Msg["MATH_ONLIST_OPERATOR_STD_DEV"] = "iṣiro deede ti akojọ";
Blockly.Msg["MATH_ONLIST_OPERATOR_SUM"] = "apao akojọ";
Blockly.Msg["MATH_ONLIST_TOOLTIP_AVERAGE"] = "Da idameji pada (ipin isiro) ti awọn nọmba iye inu akojọ.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_MAX"] = "Da nọmba ti o tobi julọ ninu akojọ pada.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_MEDIAN"] = "Da agbedemeji nọmba inu akojọ pada.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_MIN"] = "Da nọmba ti o kere julọ ninu akojọ pada.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_MODE"] = "Da akojọ ti eyi ti o wọpọ julọ ninu akojọ.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_RANDOM"] = "Da àrìnàkò ida ipilẹ nkan lati inu akojọ.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_STD_DEV"] = "Da iṣiro deede ti akojọ pada.";
Blockly.Msg["MATH_ONLIST_TOOLTIP_SUM"] = "Da apapo gbogbo awọn nọmba inu akojọ pada.";
Blockly.Msg["MATH_POWER_SYMBOL"] = "^";  // untranslated
Blockly.Msg["MATH_RANDOM_FLOAT_HELPURL"] = "https://en.wikipedia.org/wiki/Random_number_generation";
Blockly.Msg["MATH_RANDOM_FLOAT_TITLE_RANDOM"] = "oniruru ipin";
Blockly.Msg["MATH_RANDOM_FLOAT_TOOLTIP"] = "Da àrìnàkò ida pada laarin 0.0 (ini afikun) ati 1.0 (iyasọtọ).";
Blockly.Msg["MATH_RANDOM_INT_HELPURL"] = "https://en.wikipedia.org/wiki/Random_number_generation";
Blockly.Msg["MATH_RANDOM_INT_TITLE"] = "oniruru abala lati %1 si %2";
Blockly.Msg["MATH_RANDOM_INT_TOOLTIP"] = "Da àrìnàkò abala laarin awon opin pato meji pada, ini afikun.";
Blockly.Msg["MATH_ROUND_HELPURL"] = "https://en.wikipedia.org/wiki/Rounding";
Blockly.Msg["MATH_ROUND_OPERATOR_ROUND"] = "pa ju de";
Blockly.Msg["MATH_ROUND_OPERATOR_ROUNDDOWN"] = "pa ju de si isalẹ";
Blockly.Msg["MATH_ROUND_OPERATOR_ROUNDUP"] = "pa ju de soke";
Blockly.Msg["MATH_ROUND_TOOLTIP"] = "Pa oju nọmba de soke tabi si isalẹ.";
Blockly.Msg["MATH_SINGLE_HELPURL"] = "https://yo.wikipedia.org/wiki/Gb%C3%B2ngb%C3%B2_al%C3%A1gb%C3%A1ram%C3%A9j%C3%AC";
Blockly.Msg["MATH_SINGLE_OP_ABSOLUTE"] = "patapata";
Blockly.Msg["MATH_SINGLE_OP_ROOT"] = "Ipilẹ onihamẹrin";
Blockly.Msg["MATH_SINGLE_TOOLTIP_ABS"] = "Da iye patapata ti nọmba kan pada.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_EXP"] = "Da e pada si agbara ti nọmba kan.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_LN"] = "Da lọgaridimu adayeba ti nọmba kan pada.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_LOG10"] = "Da ipilẹ 10 lọgaridimu nọmba kan pada.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_NEG"] = "Da ilodisi ti nọmba kan pada";
Blockly.Msg["MATH_SINGLE_TOOLTIP_POW10"] = "Da 10 pada si agbara nọmba kan.";
Blockly.Msg["MATH_SINGLE_TOOLTIP_ROOT"] = "Da Ipilẹ onihamẹrin nọmba kan pada.";
Blockly.Msg["MATH_SUBTRACTION_SYMBOL"] = "-";  // untranslated
Blockly.Msg["MATH_TRIG_ACOS"] = "acos";  // untranslated
Blockly.Msg["MATH_TRIG_ASIN"] = "asin";  // untranslated
Blockly.Msg["MATH_TRIG_ATAN"] = "atan";  // untranslated
Blockly.Msg["MATH_TRIG_COS"] = "cos";  // untranslated
Blockly.Msg["MATH_TRIG_HELPURL"] = "https://en.wikipedia.org/wiki/Trigonometric_functions";
Blockly.Msg["MATH_TRIG_SIN"] = "sin";  // untranslated
Blockly.Msg["MATH_TRIG_TAN"] = "tan";  // untranslated
Blockly.Msg["MATH_TRIG_TOOLTIP_ACOS"] = "Da arccosine ti digiri pada.";
Blockly.Msg["MATH_TRIG_TOOLTIP_ASIN"] = "Da arcsine ti digiri pada.";
Blockly.Msg["MATH_TRIG_TOOLTIP_ATAN"] = "Da arctangent ti digiri pada.";
Blockly.Msg["MATH_TRIG_TOOLTIP_COS"] = "Da cosine ti digiri pada (kii ṣe Radian).";
Blockly.Msg["MATH_TRIG_TOOLTIP_SIN"] = "Da sine ti digiri pada (kii ṣe Radian).";
Blockly.Msg["MATH_TRIG_TOOLTIP_TAN"] = "Da tangent ti digiri pada (kii ṣe Radian).";
Blockly.Msg["NEW_COLOUR_VARIABLE"] = "Ṣe idasile awọ oniruuru...";
Blockly.Msg["NEW_NUMBER_VARIABLE"] = "Ṣe idasile nọ́mbà oniruru...";
Blockly.Msg["NEW_STRING_VARIABLE"] = "Ṣe idasile asopọ oniruru...";
Blockly.Msg["NEW_VARIABLE"] = "Ṣe idasile oniruuru...";
Blockly.Msg["NEW_VARIABLE_TITLE"] = "Orukọ oniruuru tuntun:";
Blockly.Msg["NEW_VARIABLE_TYPE_TITLE"] = "Iru oniruuru tuntun:";
Blockly.Msg["ORDINAL_NUMBER_SUFFIX"] = "";  // untranslated
Blockly.Msg["PROCEDURES_ALLOW_STATEMENTS"] = "gba alaye laaye";
Blockly.Msg["PROCEDURES_BEFORE_PARAMS"] = "pẹlu:";
Blockly.Msg["PROCEDURES_CALLNORETURN_HELPURL"] = "https://en.wikipedia.org/wiki/Subroutine";
Blockly.Msg["PROCEDURES_CALLNORETURN_TOOLTIP"] = "Ṣe ṣalaye-iṣẹ ti olumulo '%1'.";
Blockly.Msg["PROCEDURES_CALLRETURN_HELPURL"] = "https://en.wikipedia.org/wiki/Subroutine";
Blockly.Msg["PROCEDURES_CALLRETURN_TOOLTIP"] = "Ṣe ṣalaye-iṣẹ ti olumulo '%1' kii o sii lo iṣagbejade rẹ.";
Blockly.Msg["PROCEDURES_CALL_BEFORE_PARAMS"] = "pẹlu:";
Blockly.Msg["PROCEDURES_CREATE_DO"] = "Ṣe idasile '%1'";
Blockly.Msg["PROCEDURES_DEFNORETURN_COMMENT"] = "Ṣe apejuwe iṣẹ yii...";
Blockly.Msg["PROCEDURES_DEFNORETURN_DO"] = "";  // untranslated
Blockly.Msg["PROCEDURES_DEFNORETURN_HELPURL"] = "https://en.wikipedia.org/wiki/Subroutine";  // untranslated
Blockly.Msg["PROCEDURES_DEFNORETURN_PROCEDURE"] = "Ṣe awon alaye ni igba pupo.";
Blockly.Msg["PROCEDURES_DEFNORETURN_TITLE"] = "sii";
Blockly.Msg["PROCEDURES_DEFNORETURN_TOOLTIP"] = "Ṣẹda iṣẹ kan lai si iṣagbejade.";
Blockly.Msg["PROCEDURES_DEFRETURN_HELPURL"] = "https://en.wikipedia.org/wiki/Subroutine";  // untranslated
Blockly.Msg["PROCEDURES_DEFRETURN_RETURN"] = "tun tẹ";
Blockly.Msg["PROCEDURES_DEFRETURN_TOOLTIP"] = "Ṣẹda iṣẹ pẹlu iṣagbejade kan.";
Blockly.Msg["PROCEDURES_DEF_DUPLICATE_WARNING"] = "Ikilo: Isẹ yii ni awọn ẹda odiwọn.";
Blockly.Msg["PROCEDURES_HIGHLIGHT_DEF"] = "Ṣafihan iṣẹ isọtunmọ";
Blockly.Msg["PROCEDURES_IFRETURN_HELPURL"] = "http://c2.com/cgi/wiki?GuardClause";  // untranslated
Blockly.Msg["PROCEDURES_IFRETURN_TOOLTIP"] = "Ti iye ba jẹ otitọ, lẹhinna da iye keji pada.";
Blockly.Msg["PROCEDURES_IFRETURN_WARNING"] = "Ikilo: Bulọọki yii le ṣee lo nikan laarin itumọ iṣẹ kan";
Blockly.Msg["PROCEDURES_MUTATORARG_TITLE"] = "igbewọle orukọ:";
Blockly.Msg["PROCEDURES_MUTATORARG_TOOLTIP"] = "Ṣe afikun kan sii igbewọle si iṣẹ yii.";
Blockly.Msg["PROCEDURES_MUTATORCONTAINER_TITLE"] = "igbewọle";
Blockly.Msg["PROCEDURES_MUTATORCONTAINER_TOOLTIP"] = "Fikun, yọ kuro, tabi tun beere awọn igbewọle si iṣẹ yii.";
Blockly.Msg["REDO"] = "Tun ṣe";
Blockly.Msg["REMOVE_COMMENT"] = "Yọ afikun ọrọ iwoye";
Blockly.Msg["RENAME_VARIABLE"] = "Tun orukọ oniruuru kọ...";
Blockly.Msg["RENAME_VARIABLE_TITLE"] = "Tun orukọ gbogbo '%1' v oniruru kọ si:";
Blockly.Msg["TEXT_APPEND_HELPURL"] = "https://github.com/google/blockly/wiki/Text#text-modification";  // untranslated
Blockly.Msg["TEXT_APPEND_TITLE"] = "si %1 fikun ọrọ %2";
Blockly.Msg["TEXT_APPEND_TOOLTIP"] = "Ṣe afikun awon ọrọ oniruru '%1'.";
Blockly.Msg["TEXT_CHANGECASE_HELPURL"] = "https://github.com/google/blockly/wiki/Text#adjusting-text-case";  // untranslated
Blockly.Msg["TEXT_CHANGECASE_OPERATOR_LOWERCASE"] = "si alfabeti kekere";
Blockly.Msg["TEXT_CHANGECASE_OPERATOR_TITLECASE"] = "si Alfabeti Aarin";
Blockly.Msg["TEXT_CHANGECASE_OPERATOR_UPPERCASE"] = "si ALFABETI NLA";
Blockly.Msg["TEXT_CHANGECASE_TOOLTIP"] = "aa <-> AA";
Blockly.Msg["TEXT_CHARAT_FIRST"] = "gba lẹta akọkọ";
Blockly.Msg["TEXT_CHARAT_FROM_END"] = "gba lẹta # lati opin";
Blockly.Msg["TEXT_CHARAT_FROM_START"] = "gba lẹta #";
Blockly.Msg["TEXT_CHARAT_HELPURL"] = "https://github.com/google/blockly/wiki/Text#extracting-text";  // untranslated
Blockly.Msg["TEXT_CHARAT_LAST"] = "gba lẹta ti o kẹhin";
Blockly.Msg["TEXT_CHARAT_RANDOM"] = "gba lẹta àrìnàkò";
Blockly.Msg["TEXT_CHARAT_TAIL"] = "";  // untranslated
Blockly.Msg["TEXT_CHARAT_TITLE"] = "ninu %1 %2";
Blockly.Msg["TEXT_CHARAT_TOOLTIP"] = "Da lẹta naa pada si ipo ti a ti sọ tẹlẹ.";
Blockly.Msg["TEXT_COUNT_HELPURL"] = "https://github.com/google/blockly/wiki/Text#counting-substrings";  // untranslated
Blockly.Msg["TEXT_COUNT_MESSAGE0"] = "ka %1 ni %2";
Blockly.Msg["TEXT_COUNT_TOOLTIP"] = "Ka iye igba diẹ ninu awọn ọrọ kan waye laarin awọn ọrọ miiran.";
Blockly.Msg["TEXT_CREATE_JOIN_ITEM_TOOLTIP"] = "Ṣe afikun nkan si ọrọ naa.";
Blockly.Msg["TEXT_CREATE_JOIN_TITLE_JOIN"] = "darapọ";
Blockly.Msg["TEXT_CREATE_JOIN_TOOLTIP"] = "Fikun, yọ kuro, tabi ṣe atunṣe awọn apakan lati se atunkọ ọrọ bulooku yii.";
Blockly.Msg["TEXT_GET_SUBSTRING_END_FROM_END"] = "si lẹta # lati opin";
Blockly.Msg["TEXT_GET_SUBSTRING_END_FROM_START"] = "si lẹta #";
Blockly.Msg["TEXT_GET_SUBSTRING_END_LAST"] = "si lẹta kẹhin";
Blockly.Msg["TEXT_GET_SUBSTRING_HELPURL"] = "https://github.com/google/blockly/wiki/Text#extracting-a-region-of-text";  // untranslated
Blockly.Msg["TEXT_GET_SUBSTRING_INPUT_IN_TEXT"] = "ninu";
Blockly.Msg["TEXT_GET_SUBSTRING_START_FIRST"] = "gba substring lati lẹta akọkọ";
Blockly.Msg["TEXT_GET_SUBSTRING_START_FROM_END"] = "gba substring lati lẹta # lati opin";
Blockly.Msg["TEXT_GET_SUBSTRING_START_FROM_START"] = "gba substring lati lẹta #";
Blockly.Msg["TEXT_GET_SUBSTRING_TAIL"] = "";  // untranslated
Blockly.Msg["TEXT_GET_SUBSTRING_TOOLTIP"] = "Da ipin kan pato ti ọrọ naa pada.";
Blockly.Msg["TEXT_INDEXOF_HELPURL"] = "https://github.com/google/blockly/wiki/Text#finding-text";  // untranslated
Blockly.Msg["TEXT_INDEXOF_OPERATOR_FIRST"] = "wa isele akọkọ ti o wa ninu ọrọ";
Blockly.Msg["TEXT_INDEXOF_OPERATOR_LAST"] = "wa isele igbeyin ti o wa ninu ọrọ";
Blockly.Msg["TEXT_INDEXOF_TITLE"] = "ninu %1 %2 %3";
Blockly.Msg["TEXT_INDEXOF_TOOLTIP"] = "Da atọka ti akọkọ / iṣẹlẹ to kẹhin ti akojọ. Da %1 akojọ pada ti o ko ba ri.";
Blockly.Msg["TEXT_ISEMPTY_HELPURL"] = "https://github.com/google/blockly/wiki/Text#checking-for-empty-text";  // untranslated
Blockly.Msg["TEXT_ISEMPTY_TITLE"] = "%1 je isofo";
Blockly.Msg["TEXT_ISEMPTY_TOOLTIP"] = "Da otitọ pada ti ọrọ ti a pese ba ṣofo.";
Blockly.Msg["TEXT_JOIN_HELPURL"] = "https://github.com/google/blockly/wiki/Text#text-creation";  // untranslated
Blockly.Msg["TEXT_JOIN_TITLE_CREATEWITH"] = "ṣẹ ẹda ọrọ pẹlu";
Blockly.Msg["TEXT_JOIN_TOOLTIP"] = "Ṣẹda ọrọ kan nipa ṣiṣepọ gbogbo awọn ohun kan.";
Blockly.Msg["TEXT_LENGTH_HELPURL"] = "https://github.com/google/blockly/wiki/Text#text-modification";  // untranslated
Blockly.Msg["TEXT_LENGTH_TITLE"] = "Gigun ti %1";
Blockly.Msg["TEXT_LENGTH_TOOLTIP"] = "Da nọmba awọn lẹta pada (pẹlu awọn alafo) ninu ọrọ ti a pese.";
Blockly.Msg["TEXT_PRINT_HELPURL"] = "https://github.com/google/blockly/wiki/Text#printing-text";  // untranslated
Blockly.Msg["TEXT_PRINT_TITLE"] = "tẹ ọrọ %1";
Blockly.Msg["TEXT_PRINT_TOOLTIP"] = "Tẹ ọrọ kan pato, nọmba tabi iye awon miiran.";
Blockly.Msg["TEXT_PROMPT_HELPURL"] = "https://github.com/google/blockly/wiki/Text#getting-input-from-the-user";  // untranslated
Blockly.Msg["TEXT_PROMPT_TOOLTIP_NUMBER"] = "Kiakia fun olumulo fun nọmba.";
Blockly.Msg["TEXT_PROMPT_TOOLTIP_TEXT"] = "Kiakia fun olumulo fun awon ifiranṣẹ.";
Blockly.Msg["TEXT_PROMPT_TYPE_NUMBER"] = "kiakia fun nọmba pẹlu ifiranṣẹ";
Blockly.Msg["TEXT_PROMPT_TYPE_TEXT"] = "kiakia fun ọrọ pẹlu ifiranṣẹ";
Blockly.Msg["TEXT_REPLACE_HELPURL"] = "https://github.com/google/blockly/wiki/Text#replacing-substrings";  // untranslated
Blockly.Msg["TEXT_REPLACE_MESSAGE0"] = "Ṣe iropọ %1 pelu %2 in %3";
Blockly.Msg["TEXT_REPLACE_TOOLTIP"] = "Ṣe iropo gbogbo awọn iṣẹlẹ ti o sele ninu awọn ọrọ laarin awọn ọrọ miiran.";
Blockly.Msg["TEXT_REVERSE_HELPURL"] = "https://github.com/google/blockly/wiki/Text#reversing-text";  // untranslated
Blockly.Msg["TEXT_REVERSE_MESSAGE0"] = "Ṣe iyipada %1";
Blockly.Msg["TEXT_REVERSE_TOOLTIP"] = "Ṣe iyipada aṣẹ awọn ohun kikọ inu ọrọ naa.";
Blockly.Msg["TEXT_TEXT_HELPURL"] = "https://en.wikipedia.org/wiki/String_(computer_science)";
Blockly.Msg["TEXT_TEXT_TOOLTIP"] = "Lẹta, ọrọ, tabi ila ọrọ.";
Blockly.Msg["TEXT_TRIM_HELPURL"] = "https://github.com/google/blockly/wiki/Text#trimming-removing-spaces";  // untranslated
Blockly.Msg["TEXT_TRIM_OPERATOR_BOTH"] = "ge awọn alafo lati awọn igun mejeji ti";
Blockly.Msg["TEXT_TRIM_OPERATOR_LEFT"] = "ge awọn alafo lati apa osi ti";
Blockly.Msg["TEXT_TRIM_OPERATOR_RIGHT"] = "ge awọn alafo lati apa otun ti";
Blockly.Msg["TEXT_TRIM_TOOLTIP"] = "Da ẹda ti ọrọ naa pada pẹlu awọn alafo miiran ti o kuro lati ọkan tabi awọn opin mejeeji.";
Blockly.Msg["TODAY"] = "Loni";
Blockly.Msg["UNDO"] = "Maa ṣe";
Blockly.Msg["UNNAMED_KEY"] = "unnamed";  // untranslated
Blockly.Msg["VARIABLES_DEFAULT_NAME"] = "nkan";
Blockly.Msg["VARIABLES_GET_CREATE_SET"] = "Ṣe idasile 'ṣeto %1'";
Blockly.Msg["VARIABLES_GET_HELPURL"] = "https://github.com/google/blockly/wiki/Variables#get";  // untranslated
Blockly.Msg["VARIABLES_GET_TOOLTIP"] = "Da iye orisirisi yii pada.";
Blockly.Msg["VARIABLES_SET"] = "ṣeto %1 sii %2";
Blockly.Msg["VARIABLES_SET_CREATE_GET"] = "Ṣe idasile 'gba %1'";
Blockly.Msg["VARIABLES_SET_HELPURL"] = "https://github.com/google/blockly/wiki/Variables#set";  // untranslated
Blockly.Msg["VARIABLES_SET_TOOLTIP"] = "Ṣeto oniruru yii lati je bakanna sii igbasilẹ.";
Blockly.Msg["VARIABLE_ALREADY_EXISTS"] = "Oniruuru ti a darukọ '%1' ti wa tẹlẹtẹlẹ.";
Blockly.Msg["VARIABLE_ALREADY_EXISTS_FOR_ANOTHER_TYPE"] = "Oniruuru ti a darukọ '%1' ti wa tẹlẹtẹlẹ fun iru miran: '%2'.";
Blockly.Msg["WORKSPACE_ARIA_LABEL"] = "Blockly Workspace";  // untranslated
Blockly.Msg["WORKSPACE_COMMENT_DEFAULT_TEXT"] = "Sọ nkankan...";
Blockly.Msg["CONTROLS_FOREACH_INPUT_DO"] = Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"];
Blockly.Msg["CONTROLS_FOR_INPUT_DO"] = Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"];
Blockly.Msg["CONTROLS_IF_ELSEIF_TITLE_ELSEIF"] = Blockly.Msg["CONTROLS_IF_MSG_ELSEIF"];
Blockly.Msg["CONTROLS_IF_ELSE_TITLE_ELSE"] = Blockly.Msg["CONTROLS_IF_MSG_ELSE"];
Blockly.Msg["CONTROLS_IF_IF_TITLE_IF"] = Blockly.Msg["CONTROLS_IF_MSG_IF"];
Blockly.Msg["CONTROLS_IF_MSG_THEN"] = Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"];
Blockly.Msg["CONTROLS_WHILEUNTIL_INPUT_DO"] = Blockly.Msg["CONTROLS_REPEAT_INPUT_DO"];
Blockly.Msg["LISTS_CREATE_WITH_ITEM_TITLE"] = Blockly.Msg["VARIABLES_DEFAULT_NAME"];
Blockly.Msg["LISTS_GET_INDEX_HELPURL"] = Blockly.Msg["LISTS_INDEX_OF_HELPURL"];
Blockly.Msg["LISTS_GET_INDEX_INPUT_IN_LIST"] = Blockly.Msg["LISTS_INLIST"];
Blockly.Msg["LISTS_GET_SUBLIST_INPUT_IN_LIST"] = Blockly.Msg["LISTS_INLIST"];
Blockly.Msg["LISTS_INDEX_OF_INPUT_IN_LIST"] = Blockly.Msg["LISTS_INLIST"];
Blockly.Msg["LISTS_SET_INDEX_INPUT_IN_LIST"] = Blockly.Msg["LISTS_INLIST"];
Blockly.Msg["MATH_CHANGE_TITLE_ITEM"] = Blockly.Msg["VARIABLES_DEFAULT_NAME"];
Blockly.Msg["PROCEDURES_DEFRETURN_COMMENT"] = Blockly.Msg["PROCEDURES_DEFNORETURN_COMMENT"];
Blockly.Msg["PROCEDURES_DEFRETURN_DO"] = Blockly.Msg["PROCEDURES_DEFNORETURN_DO"];
Blockly.Msg["PROCEDURES_DEFRETURN_PROCEDURE"] = Blockly.Msg["PROCEDURES_DEFNORETURN_PROCEDURE"];
Blockly.Msg["PROCEDURES_DEFRETURN_TITLE"] = Blockly.Msg["PROCEDURES_DEFNORETURN_TITLE"];
Blockly.Msg["TEXT_APPEND_VARIABLE"] = Blockly.Msg["VARIABLES_DEFAULT_NAME"];
Blockly.Msg["TEXT_CREATE_JOIN_ITEM_TITLE_ITEM"] = Blockly.Msg["VARIABLES_DEFAULT_NAME"];

Blockly.Msg["MATH_HUE"] = "230";
Blockly.Msg["LOOPS_HUE"] = "120";
Blockly.Msg["LISTS_HUE"] = "260";
Blockly.Msg["LOGIC_HUE"] = "210";
Blockly.Msg["VARIABLES_HUE"] = "330";
Blockly.Msg["TEXTS_HUE"] = "160";
Blockly.Msg["PROCEDURES_HUE"] = "290";
Blockly.Msg["COLOUR_HUE"] = "20";
Blockly.Msg["VARIABLES_DYNAMIC_HUE"] = "310";