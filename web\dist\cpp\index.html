<!DOCTYPE html>
<html>
<head>
  <meta charset=utf-8>
  <meta name=viewport content="width=device-width,initial-scale=1">
  <title>Teaching C++</title>
  <script src="./static/lib/wasm/jscpp.js"></script>
  <script src="../js/jquery.min.js"></script>
  <script src="../js/jquery.cokie.min.js"></script>
  <script src="../js/jquery.qrcode.min.js"></script>
  <script src="../js/qiniu.min.js"></script>
  <script src="../js/common.js"></script>
  <script type="text/javascript" src="./static/js/test-jscpp.js"></script>
  <script>
    var uploadParam = {}
    var workName = urlParams('workName') || '' // 获取URL中的workName参数
    if(workName) workName = decodeURIComponent(workName)
    var workId = urlParams('workId')
    var unitId = urlParams('unitId')
    var departId = urlParams('departId') //班级ID
    var additionalId = urlParams('additionalId') //附加作业ID
    var scene = urlParams("scene") || 'create' //来源
    var access_token = getUserToken()
    var userInfo = getUserInfo();
    var qn_token = getQiniuToken();
    var workStatus = 1; // 默认状态为1(待批改)，添加全局变量
    var disableSubmitSave = false; // 控制保存和提交按钮的禁用状态
    
    // 设置默认项目名称
    var defaultCppProjectName = workName || "C++作品";
    
    setInterval(function () {
      qn_token = getQiniuToken();
    }, 600 * 1000)
    
    var observer = {
      next(res) { },
      error(err) {
        console.log(1, err)
      },
      complete(res) {
        uploadParam.projectKey = uploadFile(res.key, '学生作业-cpp', res.key, 5) // 注意这里的类型改为5，表示C++
        uploadWork()
      }
    }

    function handleFileUploaded(res){
      console.log(res);
      if(res.success){
        var key = res.message
        uploadParam.projectKey = uploadFile(key, '学生作业-cpp', key, 5) // C++类型
        uploadWork()
      }else{
        alert("上传失败：" + res.message)
      }
    }

    if (workId) {
      getWorkInfo(workId, function (info) {
        var myEvent = new CustomEvent('loadProject', {
          detail: {
            projectName: info.workName,
            url: info.workFileKey_url
          }
        });
        setTimeout(()=>{
          window.document.dispatchEvent(myEvent);
        }, 500)
        
        // 检查作品状态，如果已批改、首页展示或精选作品，则禁用保存和提交按钮
        if (info.workStatus == 2 || info.workStatus == 3 || info.workStatus == 4) {
          disableSubmitSave = true;
          // 使用setTimeout确保DOM加载完成后再修改按钮状态
          setTimeout(function() {
            // 禁用保存和提交按钮
            $("#submitButton").prop("disabled", true).addClass("disabled-button");
            $("#saveButton").prop("disabled", true).addClass("disabled-button");
            // 显示2秒提示信息
            showMessage("作品已批改，无法保存或提交", 2000);
          }, 1000); // 增加延迟到1000毫秒确保DOM已完全加载
        }
      })
    }
    
    // 显示提示信息的函数
    function showMessage(message, duration) {
      // 创建提示框元素
      var messageBox = document.createElement("div");
      messageBox.className = "message-box";
      messageBox.innerText = message;
      document.body.appendChild(messageBox);
      
      // 2秒后自动移除提示框
      setTimeout(function() {
        messageBox.classList.add("message-hide");
        setTimeout(function() {
          document.body.removeChild(messageBox);
        }, 300); // 渐变动画结束后移除
      }, duration);
    }
    
    // 页面加载完成后设置默认项目名
    $(document).ready(function() {
      // 设置默认项目名称
        $("#projectName").attr("placeholder", defaultCppProjectName);
      
      // 如果有workName，直接设置项目名称
      if (workName) {
        $("#projectName").val(workName);
      }
    });
    
    window.submitCode = function (projectName, code) {
      // 如果按钮被禁用，直接返回
      if (disableSubmitSave) {
        showMessage("作品已批改，无法保存或提交", 2000);
        return;
      }
      
      console.log(code);
      // 如果项目名为空，使用默认名称
      if (!projectName || projectName.trim() === "") {
        projectName = defaultCppProjectName;
      }
      uploadParam.projectTitle = projectName
      workStatus = 1; // 明确设置状态为1（待批改状态）
      
      // 检查是否有同名作品
      if (!workId) { // 只有在新建作品时检查重名
        checkWorkNameExists(projectName, 5, function(exists) {
          if (exists) {
            // 存在同名作品，弹出确认对话框
            if (!confirm("已存在同名作品，是否继续保存覆盖？")) {
              return; // 用户取消，不继续上传
            }
          }
          // 继续上传流程
          continueUpload(projectName, code);
        });
      } else {
        // 有workId，是更新已有作品，直接上传
        continueUpload(projectName, code);
      }
    }

    // 保存作品到我的作品
    window.saveCode = function (projectName, code) {
      // 如果按钮被禁用，直接返回
      if (disableSubmitSave) {
        showMessage("作品已批改，无法保存或提交", 2000);
        return;
      }
      
      console.log(code);
      // 如果项目名为空，使用默认名称
      if (!projectName || projectName.trim() === "") {
        projectName = defaultCppProjectName;
      }
      uploadParam.projectTitle = projectName
      workStatus = 0; // 明确设置状态为0（已保存状态）
      
      // 检查是否有同名作品
      if (!workId) { // 只有在新建作品时检查重名
        checkWorkNameExists(projectName, 5, function(exists) {
          if (exists) {
            // 存在同名作品，弹出确认对话框
            if (!confirm("已存在同名作品，是否继续保存覆盖？")) {
              return; // 用户取消，不继续上传
            }
          }
          // 继续上传流程
          continueUpload(projectName, code);
        });
      } else {
        // 有workId，是更新已有作品，直接上传
        continueUpload(projectName, code);
      }
    }
    
    // 继续上传流程
    function continueUpload(projectName, code) {
      qn_token = getQiniuToken();
      var uuid = window.uuid()
      var defaultUploadType = getSysConfig('uploadType')
      if(defaultUploadType == 'qiniu'){
        upload2Qiniu(code, 'cpp/' + uuid + '.cpp', projectName, observer)
      }else{
        update2Local(new Blob([code], {type: 'text/plain'}), projectName + ".cpp", 'cpp', handleFileUploaded)
      }
    }

    //上传作业
    function uploadWork() {
      $.ajax({
        url: '/api/teaching/teachingWork/submit',
        type: 'POST',
        dataType: 'json',
        contentType: 'application/json',
        beforeSend: function (request) {
          request.setRequestHeader('X-Access-Token', access_token)
        },
        data: JSON.stringify({
          courseId: unitId,
          workCover: "",
          workFile: uploadParam.projectKey,
          workName: uploadParam.projectTitle,
          id: workId,
          workType: 5, // 定义C++工作类型为5
          workStatus: workStatus, // 使用全局workStatus变量，不使用默认值
          departId: departId,
          additionalId: additionalId,
          workScene: scene,
        }),
        success: function (res) {
          if (res.code == 200) {
            if (workStatus === 0) {
              alert("保存成功")
            } else {
              alert("提交成功")
            }
            // 更新workId，方便下次保存或提交
            if (res.result && res.result.id) {
              workId = res.result.id
            }
          } else {
            if (workStatus === 0) {
              alert("保存失败：" + res.message)
            } else {
              alert("提交失败：" + res.message)
            }
          }
        },
        error: function () {
          if (workStatus === 0) {
            alert("保存失败，网络错误")
          } else {
            alert("提交失败，网络错误")
          }
        },
        complete: function () {}
      })
    }
  </script>
  <link href=./static/css/app.css rel=stylesheet>
  <style>
    .disabled-button {
      opacity: 0.5;
      cursor: not-allowed;
      background-color: #cccccc !important; /* 确保灰色效果明显 */
    }
    .message-box {
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background-color: rgba(0, 0, 0, 0.7);
      color: white;
      padding: 15px 20px;
      border-radius: 5px;
      z-index: 9999; /* 确保显示在最顶层 */
      animation: fadeIn 0.3s ease;
      font-size: 16px;
    }
    .message-hide {
      animation: fadeOut 0.3s ease;
    }
    @keyframes fadeIn {
      from { opacity: 0; }
      to { opacity: 1; }
    }
    @keyframes fadeOut {
      from { opacity: 1; }
      to { opacity: 0; }
    }
  </style>
</head>
<body>
  <div id="app">
    <div class="header">
      <div class="left-actions">
        <div class="file-button">
          <span class="file-icon"><i class="file-menu-icon"></i>文件</span>
          <div class="file-dropdown">
            <div class="file-option" id="openFileBtn">打开</div>
            <div class="file-option" id="saveFileBtn">保存</div>
          </div>
        </div>
        <input id="projectName" class="project-name" placeholder="C++作品" />
        <button id="submitButton" class="button">提交</button>
        <button id="saveButton" class="button save-button"><i class="save-icon"></i>保存</button>
        <button id="runButton" class="button run-button"><i class="run-icon"></i>运行</button>
        <button id="backButton" class="back-button">返回</button>
        <input type="file" id="fileInput" style="display: none;" accept=".cpp, .h" />
      </div>
      <div class="button-group">
      </div>
    </div>
    <div class="main-content">
      <div id="editor" class="editor-container"></div>
      <div class="io-container">
        <div class="input-container">
          <div class="input-header">输入</div>
          <textarea id="input" class="input-content" placeholder="请在此输入数据，多个数据可用空格分隔"></textarea>
        </div>
        <div class="output-container">
          <div class="output-header">输出</div>
          <div id="output" class="output-content"></div>
        </div>
      </div>
    </div>
  </div>
  
  <!-- CodeMirror编辑器库 -->
  <script src="./static/lib/codemirror/lib/codemirror.js"></script>
  <link rel="stylesheet" href="./static/lib/codemirror/lib/codemirror.css">
  <link rel="stylesheet" href="./static/lib/codemirror/theme/monokai.css">
  <script src="./static/lib/codemirror/mode/clike/clike.js"></script>
  <script src="./static/lib/codemirror/addon/edit/matchbrackets.js"></script>
  <script src="./static/lib/codemirror/addon/edit/closebrackets.js"></script>
  
  <!-- 额外CodeMirror插件 -->
  <script src="./static/lib/codemirror/addon/selection/active-line.js"></script>
  <script src="./static/lib/codemirror/addon/fold/foldcode.js"></script>
  <script src="./static/lib/codemirror/addon/fold/foldgutter.js"></script>
  <script src="./static/lib/codemirror/addon/fold/brace-fold.js"></script>
  <script src="./static/lib/codemirror/addon/fold/comment-fold.js"></script>
  <script src="./static/lib/codemirror/addon/fold/foldgutter.css"></script>
  <script src="./static/lib/codemirror/addon/hint/show-hint.js"></script>
  <script src="./static/lib/codemirror/addon/hint/javascript-hint.js"></script>
  <script src="./static/lib/codemirror/addon/hint/show-hint.css"></script>
  <script src="./static/lib/codemirror/addon/edit/closetag.js"></script>
  <script src="./static/lib/codemirror/addon/edit/matchtags.js"></script>
  <script src="./static/lib/codemirror/addon/search/search.js"></script>
  <script src="./static/lib/codemirror/addon/search/searchcursor.js"></script>
  <script src="./static/lib/codemirror/addon/search/jump-to-line.js"></script>
  <script src="./static/lib/codemirror/addon/dialog/dialog.js"></script>
  <script src="./static/lib/codemirror/addon/dialog/dialog.css"></script>
  
  <script>
    // 编辑器初始化和配置
    var editor = CodeMirror(document.getElementById("editor"), {
      mode: "text/x-c++src",
      theme: "monokai",
      lineNumbers: true,
      lineWrapping: true,
      autoCloseBrackets: true,
      matchBrackets: true,
      styleActiveLine: true,
      indentUnit: 4,
      smartIndent: true,
      tabSize: 4,
      indentWithTabs: false,
      extraKeys: {
        "Ctrl-Space": "autocomplete",
        "Tab": function(cm) {
          var spaces = Array(cm.getOption("indentUnit") + 1).join(" ");
          cm.replaceSelection(spaces);
        }
      }
    });
    
    // 设置默认代码
    editor.setValue(`#include <iostream>
using namespace std;

int main() {
    cout << "Hello, World!" << endl;
    return 0;
}`);
    
    // 处理代码运行
    document.getElementById("runButton").addEventListener("click", function() {
      var code = editor.getValue();
      var input = document.getElementById("input").value;
      var output = document.getElementById("output");
      
      // 显示编译提示
      output.innerHTML = "编译中...<br>";
      
      // 运行代码
      try {
        // 定义变量捕获输出内容
        var stdout = "";
        
        var exitcode = JSCPP.run(code, input, {
          stdio: {
            write: function(s) {
              stdout += s; // 将输出捕获到变量中，而不是直接追加到DOM
            }
          }
        });
        
        // 构建最终输出内容
        var finalOutput = stdout;
        if (exitcode !== undefined) {
          finalOutput += "<br>程序退出，返回值: " + exitcode;
        }
        
        // 用最终结果替换整个输出区域内容，清除"编译中..."提示
        output.innerHTML = finalOutput;
      } catch (e) {
        // 发生错误时也替换整个内容
        output.innerHTML = "<span style='color:red;'>错误: " + e.message + "</span>";
      }
    });
    
    // 处理文件保存
    document.getElementById("saveFileBtn").addEventListener("click", function() {
      var code = editor.getValue();
      var projectName = document.getElementById("projectName").value || defaultCppProjectName;
      var blob = new Blob([code], {type: "text/plain;charset=utf-8"});
      saveAs(blob, projectName + ".cpp");
    });
    
    // 处理文件打开
    document.getElementById("openFileBtn").addEventListener("click", function() {
      document.getElementById("fileInput").click();
    });
    
    document.getElementById("fileInput").addEventListener("change", function(e) {
      var file = e.target.files[0];
      if (file) {
        var reader = new FileReader();
        reader.onload = function(e) {
          editor.setValue(e.target.result);
        };
        reader.readAsText(file);
      }
    });
    
    // 处理提交代码
    document.getElementById("submitButton").addEventListener("click", function() {
      // 如果按钮被禁用，直接返回
      if (disableSubmitSave) {
        showMessage("作品已批改，无法保存或提交", 2000);
        return;
      }
      
      var code = editor.getValue();
      var projectName = document.getElementById("projectName").value || defaultCppProjectName;
      if (window.submitCode) {
        window.submitCode(projectName, code);
      }
    });
    
    // 处理保存代码
    document.getElementById("saveButton").addEventListener("click", function() {
      // 如果按钮被禁用，直接返回
      if (disableSubmitSave) {
        showMessage("作品已批改，无法保存或提交", 2000);
        return;
      }
      
      var code = editor.getValue();
      var projectName = document.getElementById("projectName").value || defaultCppProjectName;
      if (window.saveCode) {
        window.saveCode(projectName, code);
      }
    });
    
    // 处理返回按钮
    document.getElementById("backButton").addEventListener("click", function() {
      window.location.href = "/account/center";
    });
    
    // 处理加载项目事件
    window.document.addEventListener("loadProject", function(e) {
      if (e.detail.projectName) {
        document.getElementById("projectName").value = e.detail.projectName;
      }
      
      if (e.detail.url) {
        fetch(e.detail.url)
          .then(response => response.text())
          .then(data => {
            editor.setValue(data);
          })
          .catch(error => {
            console.error("加载项目失败:", error);
          });
      }
    });
    
    // 保存函数
    function saveAs(blob, filename) {
      var a = document.createElement("a");
      a.href = URL.createObjectURL(blob);
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
    }
  </script>
</body>
</html>