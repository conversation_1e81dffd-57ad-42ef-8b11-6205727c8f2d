<!DOCTYPE html>
<html>
<head>
  <meta charset=utf-8>
  <meta name=viewport content="width=device-width,initial-scale=1">
  <title>C++ 作品查看</title>
  <script src="../js/jquery.min.js"></script>
  <script src="../js/common.js"></script>
  <script src="./static/lib/wasm/jscpp.js"></script>
  <link href=./static/css/app.css rel=stylesheet>
  <style>
    body {
      margin: 0;
      padding: 0;
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
      background-color: #f5f5f5;
    }
    
    #app {
      display: flex;
      flex-direction: column;
      height: 100vh;
    }
    
    .header {
      background-color: #2c3e50;
      color: white;
      padding: 10px 15px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .project-name {
      font-size: 16px;
      font-weight: bold;
    }
    
    .button {
      background-color: #3498db;
      color: white;
      border: none;
      padding: 5px 12px;
      border-radius: 3px;
      cursor: pointer;
    }
    
    .main-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      background-color: #1e2a38;
      color: white;
      padding: 0;
      overflow: auto;
    }
    
    .editor-section {
      border-bottom: 1px solid #34495e;
      padding: 10px;
    }
    
    .section-content {
      background-color: #1e2a38;
      border-radius: 4px;
      overflow: hidden;
    }
    
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 10px;
      margin-bottom: 8px;
    }
    
    .section-title {
      font-size: 14px;
      font-weight: bold;
    }
    
    .btn-clean {
      padding: 4px 8px;
      background-color: #f1f1f1;
      color: #333;
      border: none;
      border-radius: 3px;
      cursor: pointer;
      font-size: 12px;
    }
    
    .input-section {
      padding: 10px;
    }
    
    #inputArea {
      width: 100%;
      min-height: 80px;
      background-color: #0f1924;
      color: white;
      border: none;
      padding: 10px;
      border-radius: 4px;
      resize: none;
      font-family: monospace;
      box-sizing: border-box;
    }
    
    .output-section {
      padding: 10px;
      flex: 1;
    }
    
    .output-header {
      padding: 8px 10px;
      background-color: #1e2a38;
      color: white;
      font-weight: bold;
      margin-bottom: 8px;
    }
    
    #output {
      min-height: 120px;
      background-color: #0f1924;
      color: white;
      padding: 10px;
      border-radius: 4px;
      font-family: monospace;
      white-space: pre-wrap;
      word-break: break-all;
      overflow: auto;
    }
    
    .editor-container {
      min-height: 200px;
      max-height: 500px;
      overflow: auto;
    }
    
    /* CodeMirror 自定义样式 */
    .CodeMirror {
      height: auto !important;
      background-color: #0f1924 !important;
      color: white !important;
      border-radius: 4px;
      font-family: "Monaco", "Menlo", monospace;
      font-size: 14px;
      line-height: 1.5;
    }
    
    .error {
      color: #e74c3c;
    }
    
    .run-button-container {
      padding: 15px;
      text-align: center;
    }
    
    .btn-run {
      padding: 8px 30px;
      background-color: #27ae60;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      font-weight: bold;
    }
  </style>
</head>
<body>
  <div id="app">
    <div class="header">
      <div class="project-info">
        <div id="projectTitle" class="project-name">作品查看</div>
      </div>
      <div class="button-group">
        <button id="runButton" class="button">运行</button>
      </div>
    </div>
    <div class="main-content">
      <!-- 代码编辑器（只读） -->
      <div class="editor-section">
        <div id="viewer" class="editor-container"></div>
      </div>
      
      <!-- 输入窗口 -->
      <div class="input-section">
        <div class="section-header">
          <div class="section-title">输入</div>
          <button id="clearButton" class="btn-clean">清空</button>
        </div>
        <textarea id="inputArea" placeholder="在此输入程序需要的数据..."></textarea>
      </div>
      
      <!-- 输出窗口 -->
      <div class="output-section">
        <div class="section-header">
          <div class="section-title">输出</div>
        </div>
        <div id="output"></div>
      </div>
      
      <!-- 运行按钮 -->
      <div class="run-button-container">
        <button id="runButtonBottom" class="btn-run">运行</button>
      </div>
    </div>
  </div>
  
  <!-- CodeMirror编辑器库 -->
  <script src="./static/lib/codemirror/lib/codemirror.js"></script>
  <link rel="stylesheet" href="./static/lib/codemirror/lib/codemirror.css">
  <link rel="stylesheet" href="./static/lib/codemirror/theme/monokai.css">
  <script src="./static/lib/codemirror/mode/clike/clike.js"></script>
  
  <!-- C++编译运行环境 -->
  <script src="./static/lib/wasm/jscpp.js"></script>
  
  <!-- 应用脚本 -->
  <script type="text/javascript" src="./static/js/manifest.js"></script>
  <script type="text/javascript" src="./static/js/vendor.js"></script>
  <script type="text/javascript" src="./static/js/appPlayer.js"></script>
  
  <script>
    // 修改运行代码函数，使其支持用户输入
    document.addEventListener('DOMContentLoaded', function() {
      // 直接定义运行代码函数
      function executeCode() {
        try {
          const viewer = document.querySelector('.CodeMirror').CodeMirror;
          const code = viewer.getValue();
          const input = document.getElementById('inputArea').value;
          const output = document.getElementById('output');
          
          output.innerHTML = '<div class="compiler-info">正在编译并运行代码...</div>';
          
          // 使用JSCPP运行C++代码
          let stdout = '';
          let inputIndex = 0;
          const inputLines = input.split('\n');
          
          const result = JSCPP.run(code, input, {
            stdio: {
              write: function(s) {
                stdout += s;
              },
              read: function() {
                if (inputIndex < inputLines.length) {
                  const line = inputLines[inputIndex++];
                  return line;
                }
                return '';
              }
            }
          });
          
          let outputHtml = '';
          if (stdout) {
            outputHtml += stdout;
          }
          if (result !== undefined) {
            outputHtml += `\n程序返回值: ${result}`;
          }
          
          output.innerHTML = outputHtml;
        } catch (e) {
          document.getElementById('output').innerHTML = `<div class="error">编译或运行错误:\n${e.message}</div>`;
          console.error('代码执行错误:', e);
        }
      }
      
      // 顶部运行按钮事件
      document.getElementById('runButton').addEventListener('click', executeCode);
      
      // 底部运行按钮事件
      document.getElementById('runButtonBottom').addEventListener('click', executeCode);
      
      // 清空按钮事件
      document.getElementById('clearButton').addEventListener('click', function() {
        document.getElementById('inputArea').value = '';
      });
      
      // 确保与原有的appPlayer.js不冲突
      window.runCode = executeCode;
    });
  </script>
</body>
</html>