/* 添加到文件顶部，确保覆盖所有默认样式 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html, body {
  width: 100%;
  height: 100vh;
  overflow: hidden;
  margin: 0;
  padding: 0;
  background-color: #2c3e50;
}

/* 确保编辑器占满整个视口区域 */
#app {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100vw;
  max-height: 100vh;
  max-width: 100vw;
  overflow: hidden;
  background-color: #2c3e50;
}

/* 调整header高度为更小值，给编辑器更多空间 */
.header {
  background-color: #2c3e50;
  color: white;
  padding: 5px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 35px;
  min-height: 35px;
  max-height: 35px;
  box-sizing: border-box;
  border-bottom: 1px solid #34495e;
}

.project-info {
  display: flex;
  align-items: center;
}

.project-name {
  padding: 4px 8px;
  height: 19px;
  border: 1px solid #34495e;
  border-radius: 3px;
  background: rgba(255,255,255,0.1);
  color: white;
  width: 170px;
  outline: none;
  font-size: 13px;
}

.project-name::placeholder {
  color: rgba(255,255,255,0.5);
}

.button-group {
  display: flex;
}

.button {
  margin-left: 8px;
  padding: 4px 12px;
  background-color: #3498db;
  color: white;
  border: none;
  cursor: pointer;
  border-radius: 3px;
  font-size: 13px;
}

.button:hover {
  background-color: #2980b9;
}

/* 确保主内容区域填充剩余空间 */
.main-content {
  display: flex;
  flex: 1 1 auto;
  height: calc(100vh - 35px);
  min-height: calc(100vh - 35px);
  overflow: hidden;
  background-color: #2c3e50;
}

.editor-container {
  flex: 1;
  overflow: hidden;
  position: relative;
  height: 100%;
}

.io-container {
  width: 40%;
  display: flex;
  flex-direction: column;
  background-color: #2c3e50;
  color: white;
  border-left: 1px solid #34495e;
  height: 100%;
}

.input-container {
  height: 33%;
  display: flex;
  flex-direction: column;
  border-bottom: 1px solid #34495e;
  background-color: #2c3e50;
}

.input-header {
  padding: 10px;
  background-color: #34495e;
  font-weight: bold;
}

.input-content {
  flex: 1;
  background-color: #2c3e50;
  color: white;
  border: none;
  padding: 10px;
  font-family: monospace;
  resize: none;
  outline: none;
  font-size: 14px;
}

.output-container {
  height: 67%;
  display: flex;
  flex-direction: column;
  background-color: #2c3e50;
}

.output-header {
  padding: 10px;
  background-color: #34495e;
  font-weight: bold;
}

.output-content {
  flex: 1;
  padding: 10px;
  font-family: monospace;
  overflow: auto;
  white-space: pre-wrap;
  font-size: 14px;
}

.CodeMirror {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  height: 100% !important;
  width: 100% !important;
  font-size: 14px;
  line-height: 1.5;
  overflow: hidden !important;
  background-color: #2c3e50 !important;
}

.CodeMirror-scroll {
  overflow: auto !important;
  scrollbar-width: thin;
  scrollbar-color: #3e4c5a #17212e;
}

.CodeMirror-vscrollbar, .CodeMirror-hscrollbar {
  display: block !important;
}

.CodeMirror-gutters {
  border-right: 1px solid #34495e !important;
  background-color: #2c3e50 !important;
}

/* 防止任何子元素露出白色背景 */
.CodeMirror *, .editor-container * {
  background-color: inherit;
}

.error {
  color: #e74c3c;
  padding: 5px 0;
}

.compiler-info {
  color: #95a5a6;
  font-style: italic;
  padding: 5px 0;
}

/* 文件菜单样式 */
.left-actions {
  display: flex;
  align-items: center;
}

.file-button {
  position: relative;
  margin-right: 15px;
  cursor: pointer;
}

.file-icon {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 3px;
  background-color: #3498db;
  color: white;
  transition: background-color 0.2s;
  font-size: 13px;
}

.file-icon:hover {
  background-color: #2980b9;
}

.file-menu-icon {
  display: inline-block;
  width: 14px;
  height: 14px;
  margin-right: 4px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8l-6-6zm-1 1v5h5v10H6V3h7z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}

.file-dropdown {
  display: none;
  position: absolute;
  top: 100%;
  left: 0;
  min-width: 120px;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 3px;
  box-shadow: 0 2px 5px rgba(0,0,0,0.2);
  z-index: 10;
}

.file-button:hover .file-dropdown {
  display: block;
}

.file-option {
  padding: 8px 12px;
  color: #333;
  transition: background-color 0.2s;
}

.file-option:hover {
  background-color: #f5f5f5;
}

/* 确保全局背景色 */
html, body, #app, .main-content, .editor-container, 
.io-container, .input-container, .output-container {
  background-color: #1e2a3a !important;
}

/* 确保没有边框导致的白色 */
.CodeMirror, .CodeMirror-scroll, .editor-container {
  border: none !important;
}

/* 确保CodeMirror内容区域完全填充 */
.CodeMirror-sizer, .CodeMirror-gutter, .CodeMirror-lines {
  background-color: #1e2a3a !important;
}

/* 覆盖monokai主题可能的白色背景 */
.cm-s-monokai.CodeMirror {
  background-color: #2c3e50 !important;
}

.cm-s-monokai .CodeMirror-gutters {
  background-color: #2c3e50 !important;
  border-right: 1px solid #34495e !important;
}

/* 现代化编辑器主题 */
.CodeMirror {
  font-family: 'JetBrains Mono', 'Consolas', 'Menlo', monospace;
  font-size: 14px;
  line-height: 1.6;
  background-color: #1e2a3a !important;
  color: #e0e0e0;
}

/* 调整编辑器和CodeMirror主题配色 */
.cm-s-monokai.CodeMirror {
  background-color: #1e2a3a !important;
  color: #e0e0e0;
}

.cm-s-monokai .CodeMirror-gutters {
  background-color: #17212e !important;
  border-right: 1px solid #2e3e50 !important;
}

.cm-s-monokai .CodeMirror-linenumber {
  color: #607080;
}

/* 代码高亮增强 */
.cm-s-monokai .cm-keyword {
  color: #c792ea;
}

.cm-s-monokai .cm-def {
  color: #82aaff;
}

.cm-s-monokai .cm-variable {
  color: #bfc7d5;
}

.cm-s-monokai .cm-variable-2 {
  color: #82aaff;
}

.cm-s-monokai .cm-type {
  color: #7fdbca;
}

.cm-s-monokai .cm-property {
  color: #c3e88d;
}

.cm-s-monokai .cm-operator {
  color: #89ddff;
}

.cm-s-monokai .cm-string {
  color: #f1c40f;
}

.cm-s-monokai .cm-comment {
  color: #546e7a;
  font-style: italic;
}

.cm-s-monokai .cm-number {
  color: #ff9d00;
}

.cm-s-monokai .cm-tag {
  color: #ff6370;
}

/* 光标和选择区域 */
.cm-s-monokai .CodeMirror-cursor {
  border-left: 2px solid #f8f8f2 !important;
}

.cm-s-monokai .CodeMirror-selected {
  background: rgba(255, 255, 255, 0.1) !important;
}

.cm-s-monokai .CodeMirror-line::selection,
.cm-s-monokai .CodeMirror-line > span::selection,
.cm-s-monokai .CodeMirror-line > span > span::selection {
  background: rgba(255, 255, 255, 0.15);
}

/* 匹配括号高亮 */
.cm-s-monokai .CodeMirror-matchingbracket {
  color: #76ff03 !important;
  background-color: rgba(118, 255, 3, 0.1);
  font-weight: bold;
}

/* 活动行高亮 */
.CodeMirror-activeline-background {
  background: rgba(255, 255, 255, 0.05) !important;
}

/* IO区域美化 */
.io-container {
  background-color: #1e2a3a;
  border-left: 1px solid #2e3e50;
}

.input-header, .output-header {
  background-color: #17212e;
  color: #e0e0e0;
  font-weight: 500;
  border-bottom: 1px solid #2e3e50;
}

.input-content {
  background-color: #1e2a3a;
  color: #e0e0e0;
  border: none;
  font-family: 'JetBrains Mono', 'Consolas', 'Menlo', monospace;
}

.output-content {
  font-family: 'JetBrains Mono', 'Consolas', 'Menlo', monospace;
  color: #e0e0e0;
}

/* 统一其他背景色 */
html, body, #app, .main-content, .editor-container {
  background-color: #1e2a3a !important;
}

.header {
  background-color: #17212e;
  border-bottom: 1px solid #2e3e50;
}

/* 添加细微的阴影效果 */
.file-button .file-dropdown {
  background-color: #17212e;
  border: 1px solid #2e3e50;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
}

.file-option {
  color: #e0e0e0;
}

.file-option:hover {
  background-color: #2e3e50;
}

/* 美化按钮 */
.button {
  background-color: #4299e1;
  border-radius: 4px;
  transition: all 0.2s ease;
  border: none;
  font-weight: 500;
}

.button:hover {
  background-color: #3182ce;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  transform: translateY(-1px);
}

/* 文件菜单按钮样式 */
.file-icon {
  background-color: #4299e1;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.file-icon:hover {
  background-color: #3182ce;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  transform: translateY(-1px);
}

/* 输入框样式 */
.project-name {
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid #2e3e50;
  color: #e0e0e0;
  border-radius: 4px;
}

.project-name::placeholder {
  color: rgba(224, 224, 224, 0.4);
}

/* 错误消息样式 */
.error {
  color: #ff6370;
  background-color: rgba(255, 99, 112, 0.1);
  padding: 8px;
  border-radius: 4px;
  border-left: 3px solid #ff6370;
}

/* 运行信息样式 */
.compiler-info {
  color: #a0aec0;
  font-style: italic;
  padding: 8px;
  border-radius: 4px;
  background-color: rgba(160, 174, 192, 0.1);
}

/* 改进滚动行为 */
.CodeMirror-scroll {
  overflow: auto !important;
  scrollbar-width: thin;
  scrollbar-color: #3e4c5a #17212e;
}

/* 更新的滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #17212e;
}

::-webkit-scrollbar-thumb {
  background: #3e4c5a;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #4a5969;
}

/* 添加JetBrains Mono字体 */
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600&display=swap');

/* 轻微的过渡动画 */
.CodeMirror {
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* 提高代码可读性 */
.CodeMirror {
  letter-spacing: 0.3px;
  word-spacing: 1px;
}

/* 改进按钮动画 */
.button, .file-icon {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.button:active, .file-icon:active {
  transform: scale(0.97);
}

/* 添加输入区域聚焦效果 */
.project-name:focus {
  box-shadow: 0 0 0 2px rgba(66, 153, 225, 0.5);
  border-color: #4299e1;
}

.input-content:focus {
  box-shadow: inset 0 0 0 1px rgba(66, 153, 225, 0.3);
  outline: none;
}

/* 添加下拉菜单过渡动画 */
.file-dropdown {
  transition: opacity 0.15s ease, transform 0.15s ease;
  transform-origin: top left;
  opacity: 0;
  transform: scaleY(0.9) translateY(-10px);
  display: block;
  pointer-events: none;
}

.file-button:hover .file-dropdown {
  opacity: 1;
  transform: scaleY(1) translateY(0);
  pointer-events: auto;
}

/* 运行按钮样式 */
.run-button {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 10px;
}

.run-icon {
  display: inline-block;
  width: 16px;
  height: 16px;
  margin-right: 5px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M8 5v14l11-7z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}

/* 调整左侧按钮组布局 */
.left-actions {
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
}

/* 清空原右侧按钮组 */
.button-group {
  display: none;
}

/* 调整按钮统一样式 */
.button, .back-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin-left: 20px;
  padding: 4px 12px;
  background-color: #4299e1;
  color: white;
  border: none;
  cursor: pointer;
  border-radius: 4px;
  font-size: 13px;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  font-weight: 500;
  height: 27px;
}

/* 返回按钮样式修改为白色 */
.back-button {
  background-color: white;
  color: #2d3748;
  border: 1px solid #e2e8f0;
}

.back-button:hover {
  background-color: #f7fafc;
  color: #1a202c;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

/* 设置提交按钮颜色 - 改回与运行按钮一致的蓝色 */
#saveButton {
  background-color: #4299e1;
}

#saveButton:hover {
  background-color: #3182ce;
}

/* 添加返回按钮悬停和激活状态样式 */
.button:hover {
  background-color: #3182ce;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  transform: translateY(-1px);
}

.button:active, .back-button:active {
  transform: scale(0.97);
}

/* 设置保存按钮样式 - 紫色 */
.save-button {
  background-color: hsl(271deg 89% 70%);
}

.save-button:hover {
  background-color: hsl(271deg 89% 60%);
}

/* 添加保存图标 */
.save-icon {
  display: inline-block;
  width: 16px;
  height: 16px;
  margin-right: 5px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M17 3H5a2 2 0 00-2 2v14a2 2 0 002 2h14a2 2 0 002-2V7l-4-4zm2 16H5V5h11.17L19 7.83V19zM12 12c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zM7 7h10v2H7V7z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}