(function() {
  // 默认代码
  let defaultCode = '';
  let editor = null;
  
  // 初始化编辑器
  function initEditor() {
    editor = CodeMirror(document.getElementById('editor'), {
      mode: 'text/x-c++src',
      theme: 'monokai',
      lineNumbers: true,
      matchBrackets: true,
      autoCloseBrackets: true,
      indentUnit: 4,
      tabSize: 4,
      indentWithTabs: true,
      lineWrapping: true,
      styleActiveLine: true,
      foldGutter: true,
      gutters: ["CodeMirror-linenumbers", "CodeMirror-foldgutter"]
    });
    
    // 加载默认代码
    fetch('./static/defaultCpp.cpp')
      .then(response => response.text())
      .then(code => {
        defaultCode = code;
        if (!window.workId) {
          editor.setValue(defaultCode);
        }
      })
      .catch(err => {
        console.error('加载默认代码失败:', err);
        defaultCode = '#include <iostream>\nusing namespace std;\n\nint main() {\n    cout << "Hello, Teaching C++ Editor!" << endl;\n    return 0;\n}';
        if (!window.workId) {
          editor.setValue(defaultCode);
        }
      });
      
    // 自动调整编辑器大小
    window.addEventListener('resize', function() {
      editor.refresh();
    });
  }
  
  // 运行C++代码
  function runCode() {
    const code = editor.getValue();
    const output = document.getElementById('output');
    const inputText = document.getElementById('input').value;
    
    output.innerHTML = '<div class="compiler-info">正在编译并运行代码...</div>';
    
    try {
      // 使用JSCPP运行C++代码，并传入输入数据
      let stdout = '';
      const result = JSCPP.run(code, inputText, {
        stdio: {
          write: function(s) {
            stdout += s;
          }
        }
      });
      
      let outputHtml = '';
      if (stdout) {
        outputHtml += stdout;
      }
      if (result !== undefined) {
        outputHtml += `\n程序返回值: ${result}`;
      }
      
      output.innerHTML = outputHtml;
    } catch (e) {
      output.innerHTML = `<div class="error">编译或运行错误:\n${e.message}</div>`;
    }
  }
  
  // 提交代码（提交作业）
  function submitCode() {
    const projectNameInput = document.getElementById('projectName');
    const projectName = projectNameInput.value.trim() || 'C++项目';
    const code = editor.getValue();
    
    // 调用提交代码函数
    window.submitCode(projectName, code);
  }
  
  // 保存代码（保存到我的作品）
  function saveCode() {
    const projectNameInput = document.getElementById('projectName');
    const projectName = projectNameInput.value.trim() || 'C++项目';
    const code = editor.getValue();
    
    // 调用保存代码函数
    window.saveCode(projectName, code);
  }
  
  // 打开本地文件
  function openLocalFile() {
    const fileInput = document.getElementById('fileInput');
    fileInput.click();
    
    fileInput.onchange = function(e) {
      const file = e.target.files[0];
      if (!file) return;
      
      const reader = new FileReader();
      reader.onload = function(e) {
        const code = e.target.result;
        editor.setValue(code);
        
        // 从文件名更新项目名称（去掉扩展名）
        const fileName = file.name.replace(/\.[^/.]+$/, "");
        document.getElementById('projectName').value = fileName;
      };
      reader.readAsText(file);
      
      // 重置input，允许打开同一个文件
      fileInput.value = '';
    };
  }
  
  // 保存到本地
  function saveToLocal() {
    const code = editor.getValue();
    const projectName = document.getElementById('projectName').value.trim() || 'untitled';
    const fileName = projectName.endsWith('.cpp') ? projectName : `${projectName}.cpp`;
    
    // 创建下载链接
    const blob = new Blob([code], {type: 'text/plain'});
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = fileName;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }
  
  // 加载现有项目
  function loadProject(url, projectName) {
    fetch(url)
      .then(response => response.text())
      .then(code => {
        editor.setValue(code);
        document.getElementById('projectName').value = projectName || '';
      })
      .catch(err => {
        console.error('加载项目失败:', err);
        document.getElementById('output').innerHTML = `<div class="error">加载项目失败: ${err.message}</div>`;
      });
  }
  
  // 初始化事件监听
  function initEvents() {
    // 运行按钮
    document.getElementById('runButton').addEventListener('click', runCode);
    
    // 提交按钮
    document.getElementById('submitButton').addEventListener('click', submitCode);
    
    // 保存到我的作品按钮
    document.getElementById('saveButton').addEventListener('click', saveCode);
    
    // 打开文件按钮
    document.getElementById('openFileBtn').addEventListener('click', openLocalFile);
    
    // 保存到本地按钮
    document.getElementById('saveFileBtn').addEventListener('click', saveToLocal);
    
    // 返回按钮
    document.getElementById('backButton').addEventListener('click', function() {
      window.location.href = '/account/center';
    });
    
    // 加载项目事件
    document.addEventListener('loadProject', function(e) {
      const detail = e.detail;
      loadProject(detail.url, detail.projectName);
    });
    
    // 支持Ctrl+S保存
    document.addEventListener('keydown', function(e) {
      if ((e.ctrlKey || e.metaKey) && e.key === 's') {
        e.preventDefault();
        if (e.shiftKey) {
          // Ctrl+Shift+S 保存到本地
          saveToLocal();
        } else {
          // Ctrl+S 保存到我的作品
          saveCode();
        }
      }
    });
    
    // 支持Ctrl+Enter运行
    editor && editor.setOption('extraKeys', {
      'Ctrl-Enter': runCode
    });
  }
  
  // 页面初始化
  function init() {
    initEditor();
    initEvents();
  }
  
  // 等待页面加载完成后初始化
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', init);
  } else {
    init();
  }
})();