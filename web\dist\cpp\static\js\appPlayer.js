(function() {
  let viewer = null;
  
  // 初始化代码查看器
  function initViewer() {
    viewer = CodeMirror(document.getElementById('viewer'), {
      mode: 'text/x-c++src',
      theme: 'monokai',
      lineNumbers: true,
      readOnly: true
    });
    
    // 获取URL参数
    const urlParams = new URLSearchParams(window.location.search);
    const codeUrl = urlParams.get('url');
    
    if (!codeUrl) {
      document.getElementById('output').innerHTML = '<div class="error">未指定代码文件URL</div>';
      return;
    }
    
    // 加载代码文件
    fetch(codeUrl)
      .then(response => response.text())
      .then(code => {
        viewer.setValue(code);
        
        // 尝试获取文件名并设置标题
        let fileName = 'C++作品';
        try {
          fileName = codeUrl.split('/').pop().split('?')[0];
          if (fileName.endsWith('.cpp')) {
            fileName = fileName.substring(0, fileName.length - 4);
          }
        } catch (e) {}
        
        document.getElementById('projectTitle').textContent = fileName;
      })
      .catch(err => {
        document.getElementById('output').innerHTML = `<div class="error">加载代码失败: ${err.message}</div>`;
      });
  }
  
  // 运行C++代码
  function runCode() {
    const code = viewer.getValue();
    const output = document.getElementById('output');
    
    output.innerHTML = '<div class="compiler-info">正在编译并运行代码...</div>';
    
    try {
      // 使用JSCPP运行C++代码
      let stdout = '';
      const result = JSCPP.run(code, '', {
        stdio: {
          write: function(s) {
            stdout += s;
          }
        }
      });
      
      let outputHtml = '';
      if (stdout) {
        outputHtml += stdout;
      }
      if (result !== undefined) {
        outputHtml += `\n程序返回值: ${result}`;
      }
      
      output.innerHTML = outputHtml;
    } catch (e) {
      output.innerHTML = `<div class="error">编译或运行错误:\n${e.message}</div>`;
    }
  }
  
  // 初始化事件
  function initEvents() {
    // 运行按钮事件
    document.getElementById('runButton').addEventListener('click', runCode);
  }
  
  // 页面初始化
  function init() {
    initViewer();
    initEvents();
  }
  
  // 等待页面加载完成后初始化
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', init);
  } else {
    init();
  }
})();