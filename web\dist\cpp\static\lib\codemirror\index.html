<!doctype html>

<title>CodeMirror 5</title>
<meta charset="utf-8"/>

<link rel=stylesheet href="lib/codemirror.css">
<link rel=stylesheet href="doc/docs.css">
<script src="lib/codemirror.js"></script>
<script src="mode/xml/xml.js"></script>
<script src="mode/javascript/javascript.js"></script>
<script src="mode/css/css.js"></script>
<script src="mode/htmlmixed/htmlmixed.js"></script>
<script src="addon/edit/matchbrackets.js"></script>

<script src="doc/activebookmark.js"></script>

<style>
  .CodeMirror { height: auto; border: 1px solid #ddd; }
  .CodeMirror-scroll { max-height: 200px; }
  .CodeMirror pre { padding-left: 7px; line-height: 1.25; }
  .banner { background: #ffc; padding: 6px; border-bottom: 2px solid silver; text-align: center }
</style>

<div class=banner>
  Note that this is the website for CodeMirror 5. <a href="https://codemirror.net/">Version 6</a> is the current version.
</div>

<div id=nav>
  <a href="https://codemirror.net/5"><h1>CodeMirror</h1><img id=logo src="doc/logo.png"></a>

  <ul>
    <li><a class=active data-default="true" href="#description">Home</a>
    <li><a href="doc/manual.html">Manual</a>
    <li><a href="https://github.com/codemirror/codemirror5">Code</a>
    <li><a href="https://codemirror.net/">Version 6</a>
  </ul>
  <ul>
    <li><a href="#features">Features</a>
    <li><a href="#community">Community</a>
    <li><a href="#browsersupport">Browser support</a>
  </ul>
</div>

<article>

<section id=description class=first>
  <p><strong>CodeMirror</strong> is a versatile text editor
  implemented in JavaScript for the browser. It is specialized for
  editing code, and comes with a number of <a href="mode/index.html">language modes</a> and <a href="doc/manual.html#addons">addons</a>
  that implement more advanced editing functionality.</p>

  <p>A rich <a href="doc/manual.html#api">programming API</a> and a
  CSS <a href="doc/manual.html#styling">theming</a> system are
  available for customizing CodeMirror to fit your application, and
  extending it with new functionality.</p>
</section>

<section id=demo>
  <h2>This is CodeMirror</h2>
  <form style="position: relative; margin-top: .5em;"><textarea id=demotext>
<!-- Create a simple CodeMirror instance -->
<link rel="stylesheet" href="lib/codemirror.css">
<script src="lib/codemirror.js"></script>
<script>
  var editor = CodeMirror.fromTextArea(myTextarea, {
    lineNumbers: true
  });
</script></textarea>
  <select id="demolist" onchange="document.location = this.options[this.selectedIndex].value;">
    <option value="#">Other demos...</option>
    <option value="demo/complete.html">Autocompletion</option>
    <option value="demo/folding.html">Code folding</option>
    <option value="demo/theme.html">Themes</option>
    <option value="mode/htmlmixed/index.html">Mixed language modes</option>
    <option value="demo/bidi.html">Bi-directional text</option>
    <option value="demo/variableheight.html">Variable font sizes</option>
    <option value="demo/search.html">Search interface</option>
    <option value="demo/vim.html">Vim bindings</option>
    <option value="demo/emacs.html">Emacs bindings</option>
    <option value="demo/sublime.html">Sublime Text bindings</option>
    <option value="demo/tern.html">Tern integration</option>
    <option value="demo/merge.html">Merge/diff interface</option>
    <option value="demo/fullscreen.html">Full-screen editor</option>
    <option value="demo/simplescrollbars.html">Custom scrollbars</option>
  </select></form>
  <script>
    var editor = CodeMirror.fromTextArea(document.getElementById("demotext"), {
      lineNumbers: true,
      mode: "text/html",
      matchBrackets: true
    });
  </script>

  <div class=actions>
    Get the current version: <a href="https://codemirror.net/5/codemirror.zip">5.65.19</a>.<br>
    You can see the <a href="https://github.com/codemirror/codemirror5" title="GitHub repository">code</a>,<br>
    read the <a href="doc/releases.html">release notes</a>,<br>
    or study the <a href="doc/manual.html">user manual</a>.
  </div>

</section>

<section id=features>
  <h2>Features</h2>
  <ul>
    <li>Support for <a href="mode/index.html">over 100 languages</a> out of the box
    <li>A powerful, <a href="mode/htmlmixed/index.html">composable</a> language mode <a href="doc/manual.html#modeapi">system</a>
    <li><a href="doc/manual.html#addon_show-hint">Autocompletion</a> (<a href="demo/xmlcomplete.html">XML</a>)
    <li><a href="doc/manual.html#addon_foldcode">Code folding</a>
    <li><a href="doc/manual.html#option_extraKeys">Configurable</a> keybindings
    <li><a href="demo/vim.html">Vim</a>, <a href="demo/emacs.html">Emacs</a>, and <a href="demo/sublime.html">Sublime Text</a> bindings
    <li><a href="doc/manual.html#addon_search">Search and replace</a> interface
    <li><a href="doc/manual.html#addon_matchbrackets">Bracket</a> and <a href="doc/manual.html#addon_matchtags">tag</a> matching
    <li>Support for <a href="demo/buffers.html">split views</a>
    <li><a href="doc/manual.html#addon_lint">Linter integration</a>
    <li><a href="demo/variableheight.html">Mixing font sizes and styles</a>
    <li><a href="demo/theme.html">Various themes</a>
    <li>Able to <a href="demo/resize.html">resize to fit content</a>
    <li><a href="doc/manual.html#mark_replacedWith">Inline</a> and <a href="doc/manual.html#addLineWidget">block</a> widgets
    <li>Programmable <a href="demo/marker.html">gutters</a>
    <li>Making ranges of text <a href="doc/manual.html#markText">styled, read-only, or atomic</a>
    <li><a href="demo/bidi.html">Bi-directional text</a> support
    <li>Many other <a href="doc/manual.html#api">methods</a> and <a href="doc/manual.html#addons">addons</a>...
  </ul>
</section>

<section id=community>
  <h2>Community</h2>

  <p>CodeMirror is an open-source project shared under
  an <a href="LICENSE">MIT license</a>.</p>

  <p>Development and bug tracking happens
  on <a href="https://github.com/codemirror/CodeMirror/">github</a>
  (<a href="http://marijnhaverbeke.nl/git/codemirror">alternate git
  repository</a>).
  Please <a href="https://codemirror.net/5/doc/reporting.html">read these
  pointers</a> before submitting a bug. Use pull requests to submit
  patches. All contributions must be released under the same MIT
  license that CodeMirror uses.</p>

  <p>Discussion around the project is done on
  a <a href="https://discuss.codemirror.net">discussion forum</a>.
  Announcements related to the project, such as new versions, are
  posted in the
  forum's <a href="https://discuss.codemirror.net/c/announce">"announce"</a>
  category. If needed, you can
  contact <a href="mailto:<EMAIL>">the maintainer</a>
  directly. We aim to be an inclusive, welcoming community. To make
  that explicit, we have
  a <a href="http://contributor-covenant.org/version/1/1/0/">code of
  conduct</a> that applies to communication around the project.</p>
</section>

<section id=browsersupport>
  <h2>Browser support</h2>
  <p>The <em>desktop</em> versions of the following browsers,
  in <em>standards mode</em> (HTML5 <code>&lt;!doctype html></code>
  recommended) are supported:</p>
  <table style="margin-bottom: 1em">
    <tr><th>Firefox</th><td>version 4 and up</td></tr>
    <tr><th>Chrome</th><td>any version</td></tr>
    <tr><th>Safari</th><td>version 5.2 and up</td></tr>
    <tr><th style="padding-right: 1em;">Internet Explorer/Edge</th><td>version 8 and up</td></tr>
    <tr><th>Opera</th><td>version 9 and up</td></tr>
  </table>
  <p>Support for modern mobile browsers is experimental. Recent
  versions of the iOS browser and Chrome on Android should work
  pretty well.</p>
</section>

</article>
