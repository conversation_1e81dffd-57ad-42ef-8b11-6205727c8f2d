<!doctype html>

<title>CodeMirror: troff mode</title>
<meta charset="utf-8"/>
<link rel=stylesheet href="../../doc/docs.css">

<link rel=stylesheet href=../../lib/codemirror.css>
<script src=../../lib/codemirror.js></script>
<script src="../../addon/edit/matchbrackets.js"></script>
<script src=troff.js></script>
<style type=text/css>
  .CodeMirror {border-top: 1px solid black; border-bottom: 1px solid black;}
</style>
<div id=nav>
  <a href="https://codemirror.net/5"><h1>CodeMirror</h1><img id=logo src="../../doc/logo.png" alt=""></a>

  <ul>
    <li><a href="../../index.html">Home</a>
    <li><a href="../../doc/manual.html">Manual</a>
    <li><a href="https://github.com/codemirror/codemirror5">Code</a>
  </ul>
  <ul>
    <li><a href="../index.html">Language modes</a>
    <li><a class=active href="#">troff</a>
  </ul>
</div>

<article>
<h2>troff</h2>


<textarea id=code>
'\" t
.\"     Title: mkvextract
.TH "MKVEXTRACT" "1" "2015\-02\-28" "MKVToolNix 7\&.7\&.0" "User Commands"
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\" -----------------------------------------------------------------
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.SH "NAME"
mkvextract \- extract tracks from Matroska(TM) files into other files
.SH "SYNOPSIS"
.HP \w'\fBmkvextract\fR\ 'u
\fBmkvextract\fR {mode} {source\-filename} [options] [extraction\-spec]
.SH "DESCRIPTION"
.PP
.B mkvextract
extracts specific parts from a
.I Matroska(TM)
file to other useful formats\&. The first argument,
\fBmode\fR, tells
\fBmkvextract\fR(1)
what to extract\&. Currently supported is the extraction of
tracks,
tags,
attachments,
chapters,
CUE sheets,
timecodes
and
cues\&. The second argument is the name of the source file\&. It must be a
Matroska(TM)
file\&. All following arguments are options and extraction specifications; both of which depend on the selected mode\&.
.SS "Common options"
.PP
The following options are available in all modes and only described once in this section\&.
.PP
\fB\-f\fR, \fB\-\-parse\-fully\fR
.RS 4
Sets the parse mode to \*(Aqfull\*(Aq\&. The default mode does not parse the whole file but uses the meta seek elements for locating the required elements of a source file\&. In 99% of all cases this is enough\&. But for files that do not contain meta seek elements or which are damaged the user might have to use this mode\&. A full scan of a file can take a couple of minutes while a fast scan only takes seconds\&.
.RE
.PP
\fB\-\-command\-line\-charset\fR \fIcharacter\-set\fR
.RS 4
Sets the character set to convert strings given on the command line from\&. It defaults to the character set given by system\*(Aqs current locale\&.
.RE
.PP
\fB\-\-output\-charset\fR \fIcharacter\-set\fR
.RS 4
Sets the character set to which strings are converted that are to be output\&. It defaults to the character set given by system\*(Aqs current locale\&.
.RE
.PP
\fB\-r\fR, \fB\-\-redirect\-output\fR \fIfile\-name\fR
.RS 4
Writes all messages to the file
\fIfile\-name\fR
instead of to the console\&. While this can be done easily with output redirection there are cases in which this option is needed: when the terminal reinterprets the output before writing it to a file\&. The character set set with
\fB\-\-output\-charset\fR
is honored\&.
.RE
.PP
\fB\-\-ui\-language\fR \fIcode\fR
.RS 4
Forces the translations for the language
\fIcode\fR
to be used (e\&.g\&. \*(Aqde_DE\*(Aq for the German translations)\&. It is preferable to use the environment variables
\fILANG\fR,
\fILC_MESSAGES\fR
and
\fILC_ALL\fR
though\&. Entering \*(Aqlist\*(Aq as the
\fIcode\fR
will cause
\fBmkvextract\fR(1)
to output a list of available translations\&.

.\" [...]

.SH "SEE ALSO"
.PP
\fBmkvmerge\fR(1),
\fBmkvinfo\fR(1),
\fBmkvpropedit\fR(1),
\fBmmg\fR(1)
.SH "WWW"
.PP
The latest version can always be found at
\m[blue]\fBthe MKVToolNix homepage\fR\m[]\&\s-2\u[1]\d\s+2\&.
.SH "AUTHOR"
.PP
\(co \fBMoritz Bunkus\fR <\&moritz@bunkus\&.org\&>
.RS 4
Developer
.RE
.SH "NOTES"
.IP " 1." 4
the MKVToolNix homepage
.RS 4
\%https://www.bunkus.org/videotools/mkvtoolnix/
.RE
</textarea>

<script>
  var editor = CodeMirror.fromTextArea(document.getElementById('code'), {
    mode: 'troff',
    lineNumbers: true,
    matchBrackets: false
  });
</script>

<p><strong>MIME types defined:</strong> <code>troff</code>.</p>
</article>
