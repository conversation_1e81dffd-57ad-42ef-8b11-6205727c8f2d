<!doctype html>

<title>CodeMirror: VB.NET mode</title>
<meta charset="utf-8"/>
<link rel=stylesheet href="../../doc/docs.css">

<link rel="stylesheet" href="../../lib/codemirror.css">
<link href="http://fonts.googleapis.com/css?family=Inconsolata" rel="stylesheet">
<script src="../../lib/codemirror.js"></script>
<script src="vb.js"></script>
<script src="../../addon/runmode/runmode.js"></script>
<style>
      .CodeMirror {border: 1px solid #aaa; height:210px; height: auto;}
      .CodeMirror-scroll { overflow-x: auto; overflow-y: hidden;}
      .CodeMirror pre { font-family: Inconsolata; font-size: 14px}
    </style>
<div id=nav>
  <a href="https://codemirror.net/5"><h1>CodeMirror</h1><img id=logo src="../../doc/logo.png" alt=""></a>

  <ul>
    <li><a href="../../index.html">Home</a>
    <li><a href="../../doc/manual.html">Manual</a>
    <li><a href="https://github.com/codemirror/codemirror5">Code</a>
  </ul>
  <ul>
    <li><a href="../index.html">Language modes</a>
    <li><a class=active href="#">VB.NET</a>
  </ul>
</div>

<article>
<h2>VB.NET mode</h2>
  <div id="edit">
  <textarea name="code" id="code" >
Class rocket
  Private quality as Double
  Public Sub launch() as String
    If quality > 0.8
      launch = "Successful"
    Else
      launch = "Failed"
    End If
  End sub
End class
</textarea>
  </div>
  <p>MIME type defined: <code>text/x-vb</code>.</p>
<script>CodeMirror.fromTextArea(document.getElementById("code"))</script>
</article>
