{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\teachingproject\\teaching\\web\\src\\components\\_util\\util.js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\_util\\util.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1745675041593}], "contextDependencies": [], "result": ["/**\n * components util\n */\n\n/**\n * 清理空值，对象\n * @param children\n * @returns {*[]}\n */\nexport function filterEmpty() {\n  var children = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n  return children.filter(function (c) {\n    return c.tag || c.text && c.text.trim() !== '';\n  });\n}", {"version": 3, "names": ["filterEmpty", "children", "arguments", "length", "undefined", "filter", "c", "tag", "text", "trim"], "sources": ["E:/teachingproject/teaching/web/src/components/_util/util.js"], "sourcesContent": ["/**\n * components util\n */\n\n/**\n * 清理空值，对象\n * @param children\n * @returns {*[]}\n */\nexport function filterEmpty (children = []) {\n  return children.filter(c => c.tag || (c.text && c.text.trim() !== ''))\n}"], "mappings": "AAAA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASA,WAAWA,CAAA,EAAiB;EAAA,IAAfC,QAAQ,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,EAAE;EACxC,OAAOD,QAAQ,CAACI,MAAM,CAAC,UAAAC,CAAC;IAAA,OAAIA,CAAC,CAACC,GAAG,IAAKD,CAAC,CAACE,IAAI,IAAIF,CAAC,CAACE,IAAI,CAACC,IAAI,CAAC,CAAC,KAAK,EAAG;EAAA,EAAC;AACxE", "ignoreList": []}]}