{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\dict\\JDictSelectTag.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\dict\\JDictSelectTag.vue", "mtime": 1751357905810}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["import { ajaxGetDictItems, getDictItemsFromCache, addDictItemsCache, deleteDictItems, deleteDictItemsCache } from '@/api/api';\nexport default {\n  name: \"JDictSelectTag\",\n  props: {\n    dictCode: String,\n    placeholder: String,\n    triggerChange: <PERSON><PERSON><PERSON>,\n    disabled: <PERSON><PERSON><PERSON>,\n    value: [String, Number],\n    type: String,\n    canDeleteItem: {\n      //是否可以删除字典项\n      type: Boolean,\n      default: false\n    },\n    getPopupContainer: {\n      type: Function,\n      default: function _default(node) {\n        return node.parentNode;\n      }\n    },\n    defaultShowAll: {\n      //是否显示全部选项\n      type: Boolean,\n      default: false\n    },\n    //默认选项\n    defaultDictOptions: {\n      type: Array,\n      default: function _default() {\n        return [];\n      }\n    },\n    // 是否隐藏绿色状态（学生端设为true，教师和管理员端设为false）\n    hideGreenStatus: {\n      type: Boolean,\n      default: false\n    }\n  },\n  data: function data() {\n    return {\n      dictOptions: [],\n      tagType: \"\",\n      originalValue: null\n    };\n  },\n  watch: {\n    dictCode: {\n      immediate: true,\n      handler: function handler() {\n        this.initDictData();\n      }\n    },\n    value: {\n      immediate: true,\n      handler: function handler(val) {\n        // 如果外部未手动设置originalValue，则使用第一次加载的值作为当前状态\n        if (this.originalValue === null && val !== null) {\n          this.originalValue = val !== undefined ? val.toString() : undefined;\n        }\n      }\n    }\n  },\n  created: function created() {\n    // console.log(this.dictCode);\n    if (!this.type || this.type === \"list\") {\n      this.tagType = \"select\";\n    } else {\n      this.tagType = this.type;\n    }\n    //获取字典数据\n    // this.initDictData();\n  },\n  computed: {\n    getValueSting: function getValueSting() {\n      // 修复：确保值不是null或undefined再调用toString()\n      // 当有null或\"\" placeholder不显示\n      return this.value !== undefined && this.value !== null ? String(this.value) : undefined;\n    }\n  },\n  methods: {\n    initDictData: function initDictData() {\n      var _this = this;\n      if (this.defaultShowAll) {\n        this.dictOptions = [{\n          title: \"全部\",\n          text: \"全部\",\n          description: '',\n          value: ''\n        }];\n      } else {\n        this.dictOptions = [];\n      }\n      if (this.defaultDictOptions != null && this.defaultDictOptions.length > 0) {\n        this.dictOptions = this.defaultDictOptions;\n      }\n      if (this.dictCode) {\n        if (getDictItemsFromCache(this.dictCode)) {\n          this.dictOptions = this.dictOptions.concat(getDictItemsFromCache(this.dictCode));\n        } else {\n          //根据字典Code, 初始化字典数组\n          ajaxGetDictItems(this.dictCode, null).then(function (res) {\n            if (res.success) {\n              _this.dictOptions = _this.dictOptions.concat(res.result);\n            }\n          });\n        }\n      }\n      console.log(this.dictOptions);\n    },\n    //添加字典项缓存\n    addDictItems: function addDictItems(dictValue, dictText, dictTitle, dictDesc) {\n      addDictItemsCache(this.dictCode, dictValue, dictText, dictTitle, dictDesc);\n      this.initDictData();\n    },\n    //删除字典项\n    deleteDictItem: function deleteDictItem(e, v) {\n      var _this2 = this;\n      e.stopPropagation();\n      if (confirm(\"是否确定删除 \" + v.text + \"?\")) {\n        deleteDictItems(this.dictCode, v.text).then(function (res) {\n          if (res.success) {\n            deleteDictItemsCache(_this2.dictCode, v.text);\n            _this2.initDictData();\n          }\n        });\n      }\n    },\n    handleInput: function handleInput(e) {\n      var val;\n      if (this.tagType == \"radio\") {\n        val = e.target.value;\n      } else {\n        val = e;\n      }\n      console.log(val);\n      if (this.triggerChange) {\n        this.$emit('change', val);\n      } else {\n        this.$emit('input', val);\n      }\n    },\n    setCurrentDictOptions: function setCurrentDictOptions(dictOptions) {\n      this.dictOptions = dictOptions;\n    },\n    getCurrentDictOptions: function getCurrentDictOptions() {\n      return this.dictOptions;\n    }\n  }\n};", {"version": 3, "names": ["ajaxGetDictItems", "getDictItemsFromCache", "addDictItemsCache", "deleteDictItems", "deleteDictItemsCache", "name", "props", "dictCode", "String", "placeholder", "trigger<PERSON>hange", "Boolean", "disabled", "value", "Number", "type", "canDeleteItem", "default", "getPopupContainer", "Function", "_default", "node", "parentNode", "defaultShowAll", "defaultDictOptions", "Array", "hideGreenStatus", "data", "dictOptions", "tagType", "originalValue", "watch", "immediate", "handler", "initDictData", "val", "undefined", "toString", "created", "computed", "getValueSting", "methods", "_this", "title", "text", "description", "length", "concat", "then", "res", "success", "result", "console", "log", "addDictItems", "dict<PERSON><PERSON>ue", "dictText", "dictTitle", "dictDesc", "deleteDictItem", "e", "v", "_this2", "stopPropagation", "confirm", "handleInput", "target", "$emit", "setCurrentDictOptions", "getCurrentDictOptions"], "sources": ["src/components/dict/JDictSelectTag.vue"], "sourcesContent": ["<template>\n  <a-radio-group v-if=\"tagType=='radio'\" @change=\"handleInput\" :value=\"getValueSting\" :disabled=\"disabled\">\n    <a-radio v-for=\"(item, key) in dictOptions\" :key=\"key\" :value=\"item.value\">{{ item.text }}</a-radio>\n  </a-radio-group>\n\n  <a-radio-group v-else-if=\"tagType=='radioButton'\"  buttonStyle=\"solid\" @change=\"handleInput\" :value=\"getValueSting\" :disabled=\"disabled\">\n    <a-radio-button \n      v-for=\"(item, key) in dictOptions\" \n      :key=\"key\" \n      :value=\"item.value\" \n      :class=\"{'current-status': !hideGreenStatus && item.value === originalValue && item.value !== getValueSting}\">\n      {{ item.text }}\n    </a-radio-button>\n  </a-radio-group>\n\n  <a-select v-else-if=\"tagType=='select'\" :getPopupContainer = \"getPopupContainer\" :placeholder=\"placeholder\" :disabled=\"disabled\" :value=\"getValueSting\" @change=\"handleInput\">\n    <a-select-option :value=\"undefined\">请选择</a-select-option>\n    <a-select-option v-for=\"(item, key) in dictOptions\" :key=\"key\" :value=\"item.value\">\n      <span style=\"display: inline-block;width: 100%\" :title=\" item.text || item.label \">\n        <a-icon v-if=\"canDeleteItem\" type=\"delete\" @click=\"deleteDictItem($event,item)\"/>\n        {{ item.text || item.label }}\n      </span>\n    </a-select-option>\n  </a-select>\n</template>\n\n<script>\n  import {ajaxGetDictItems,getDictItemsFromCache,addDictItemsCache,deleteDictItems,deleteDictItemsCache} from '@/api/api'\n\n  export default {\n    name: \"JDictSelectTag\",\n    props: {\n      dictCode: String,\n      placeholder: String,\n      triggerChange: Boolean,\n      disabled: Boolean,\n      value: [String, Number],\n      type: String,\n      canDeleteItem: { //是否可以删除字典项\n        type: Boolean,\n        default: false\n      },\n      getPopupContainer:{\n        type: Function,\n        default: (node) => node.parentNode\n      },\n      defaultShowAll: { //是否显示全部选项\n        type: Boolean,\n        default: false\n      },\n      //默认选项\n      defaultDictOptions:{\n        type: Array,\n        default: ()=>[]\n      },\n      // 是否隐藏绿色状态（学生端设为true，教师和管理员端设为false）\n      hideGreenStatus: {\n        type: Boolean,\n        default: false\n      } \n    },\n    data() {\n      return {\n        dictOptions: [],\n        tagType:\"\",\n        originalValue: null\n      }\n    },\n    watch:{\n      dictCode:{\n        immediate:true,\n        handler() {\n          this.initDictData()\n        },\n      },\n      value: {\n        immediate: true,\n        handler(val) {\n          // 如果外部未手动设置originalValue，则使用第一次加载的值作为当前状态\n          if (this.originalValue === null && val !== null) {\n            this.originalValue = val !== undefined ? val.toString() : undefined;\n          }\n        }\n      }\n    },\n    created() {\n      // console.log(this.dictCode);\n      if(!this.type || this.type===\"list\"){\n        this.tagType = \"select\"\n      }else{\n        this.tagType = this.type\n      }\n      //获取字典数据\n      // this.initDictData();\n    },\n    computed: {\n      getValueSting(){\n        // 修复：确保值不是null或undefined再调用toString()\n        // 当有null或\"\" placeholder不显示\n        return (this.value !== undefined && this.value !== null) ? String(this.value) : undefined;\n      },\n    },\n    methods: {\n      initDictData() {\n        if(this.defaultShowAll){\n          this.dictOptions = [{title: \"全部\", text: \"全部\", description:'', value: ''}]\n        }else{\n          this.dictOptions = []\n        }\n\n        if(this.defaultDictOptions != null && this.defaultDictOptions.length > 0){\n          this.dictOptions = this.defaultDictOptions\n        }\n        \n        if(this.dictCode){\n          if(getDictItemsFromCache(this.dictCode)){\n            this.dictOptions = this.dictOptions.concat(getDictItemsFromCache(this.dictCode))\n          }else{\n            //根据字典Code, 初始化字典数组\n            ajaxGetDictItems(this.dictCode, null).then((res) => {\n              if (res.success) {\n                this.dictOptions = this.dictOptions.concat(res.result);\n              }\n            })\n          }\n        }\n        console.log(this.dictOptions);\n      },\n      //添加字典项缓存\n      addDictItems(dictValue, dictText, dictTitle, dictDesc){\n        addDictItemsCache(this.dictCode, dictValue, dictText, dictTitle, dictDesc)\n        this.initDictData()\n      },\n      //删除字典项\n      deleteDictItem(e,v){\n        e.stopPropagation()\n        if(confirm(\"是否确定删除 \" + v.text + \"?\")){\n          deleteDictItems(this.dictCode, v.text).then(res=>{\n            if(res.success){\n              deleteDictItemsCache(this.dictCode, v.text)\n              this.initDictData()\n            }\n          })\n        }\n      },\n      handleInput(e) {\n        let val;\n        if(this.tagType==\"radio\"){\n          val = e.target.value\n        }else{\n          val = e\n        }\n        console.log(val);\n        if(this.triggerChange){\n          this.$emit('change', val);\n        }else{\n          this.$emit('input', val);\n        }\n      },\n      setCurrentDictOptions(dictOptions){\n        this.dictOptions = dictOptions\n      },\n      getCurrentDictOptions(){\n        return this.dictOptions\n      }\n    }\n  }\n</script>\n\n<style scoped>\n.current-status {\n  background-color: #52c41a !important;\n  border-color: #52c41a !important;\n  color: #fff !important;\n  box-shadow: -1px 0 0 0 #52c41a;\n}\n/* 当鼠标悬停在绿色状态按钮上，保持绿色 */\n.current-status:hover {\n  background-color: #73d13d !important;\n  border-color: #73d13d !important;\n}\n</style>"], "mappings": "AA2BA,SAAAA,gBAAA,EAAAC,qBAAA,EAAAC,iBAAA,EAAAC,eAAA,EAAAC,oBAAA;AAEA;EACAC,IAAA;EACAC,KAAA;IACAC,QAAA,EAAAC,MAAA;IACAC,WAAA,EAAAD,MAAA;IACAE,aAAA,EAAAC,OAAA;IACAC,QAAA,EAAAD,OAAA;IACAE,KAAA,GAAAL,MAAA,EAAAM,MAAA;IACAC,IAAA,EAAAP,MAAA;IACAQ,aAAA;MAAA;MACAD,IAAA,EAAAJ,OAAA;MACAM,OAAA;IACA;IACAC,iBAAA;MACAH,IAAA,EAAAI,QAAA;MACAF,OAAA,WAAAG,SAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,UAAA;MAAA;IACA;IACAC,cAAA;MAAA;MACAR,IAAA,EAAAJ,OAAA;MACAM,OAAA;IACA;IACA;IACAO,kBAAA;MACAT,IAAA,EAAAU,KAAA;MACAR,OAAA,WAAAG,SAAA;QAAA;MAAA;IACA;IACA;IACAM,eAAA;MACAX,IAAA,EAAAJ,OAAA;MACAM,OAAA;IACA;EACA;EACAU,IAAA,WAAAA,KAAA;IACA;MACAC,WAAA;MACAC,OAAA;MACAC,aAAA;IACA;EACA;EACAC,KAAA;IACAxB,QAAA;MACAyB,SAAA;MACAC,OAAA,WAAAA,QAAA;QACA,KAAAC,YAAA;MACA;IACA;IACArB,KAAA;MACAmB,SAAA;MACAC,OAAA,WAAAA,QAAAE,GAAA;QACA;QACA,SAAAL,aAAA,aAAAK,GAAA;UACA,KAAAL,aAAA,GAAAK,GAAA,KAAAC,SAAA,GAAAD,GAAA,CAAAE,QAAA,KAAAD,SAAA;QACA;MACA;IACA;EACA;EACAE,OAAA,WAAAA,QAAA;IACA;IACA,UAAAvB,IAAA,SAAAA,IAAA;MACA,KAAAc,OAAA;IACA;MACA,KAAAA,OAAA,QAAAd,IAAA;IACA;IACA;IACA;EACA;EACAwB,QAAA;IACAC,aAAA,WAAAA,cAAA;MACA;MACA;MACA,YAAA3B,KAAA,KAAAuB,SAAA,SAAAvB,KAAA,YAAAL,MAAA,MAAAK,KAAA,IAAAuB,SAAA;IACA;EACA;EACAK,OAAA;IACAP,YAAA,WAAAA,aAAA;MAAA,IAAAQ,KAAA;MACA,SAAAnB,cAAA;QACA,KAAAK,WAAA;UAAAe,KAAA;UAAAC,IAAA;UAAAC,WAAA;UAAAhC,KAAA;QAAA;MACA;QACA,KAAAe,WAAA;MACA;MAEA,SAAAJ,kBAAA,iBAAAA,kBAAA,CAAAsB,MAAA;QACA,KAAAlB,WAAA,QAAAJ,kBAAA;MACA;MAEA,SAAAjB,QAAA;QACA,IAAAN,qBAAA,MAAAM,QAAA;UACA,KAAAqB,WAAA,QAAAA,WAAA,CAAAmB,MAAA,CAAA9C,qBAAA,MAAAM,QAAA;QACA;UACA;UACAP,gBAAA,MAAAO,QAAA,QAAAyC,IAAA,WAAAC,GAAA;YACA,IAAAA,GAAA,CAAAC,OAAA;cACAR,KAAA,CAAAd,WAAA,GAAAc,KAAA,CAAAd,WAAA,CAAAmB,MAAA,CAAAE,GAAA,CAAAE,MAAA;YACA;UACA;QACA;MACA;MACAC,OAAA,CAAAC,GAAA,MAAAzB,WAAA;IACA;IACA;IACA0B,YAAA,WAAAA,aAAAC,SAAA,EAAAC,QAAA,EAAAC,SAAA,EAAAC,QAAA;MACAxD,iBAAA,MAAAK,QAAA,EAAAgD,SAAA,EAAAC,QAAA,EAAAC,SAAA,EAAAC,QAAA;MACA,KAAAxB,YAAA;IACA;IACA;IACAyB,cAAA,WAAAA,eAAAC,CAAA,EAAAC,CAAA;MAAA,IAAAC,MAAA;MACAF,CAAA,CAAAG,eAAA;MACA,IAAAC,OAAA,aAAAH,CAAA,CAAAjB,IAAA;QACAzC,eAAA,MAAAI,QAAA,EAAAsD,CAAA,CAAAjB,IAAA,EAAAI,IAAA,WAAAC,GAAA;UACA,IAAAA,GAAA,CAAAC,OAAA;YACA9C,oBAAA,CAAA0D,MAAA,CAAAvD,QAAA,EAAAsD,CAAA,CAAAjB,IAAA;YACAkB,MAAA,CAAA5B,YAAA;UACA;QACA;MACA;IACA;IACA+B,WAAA,WAAAA,YAAAL,CAAA;MACA,IAAAzB,GAAA;MACA,SAAAN,OAAA;QACAM,GAAA,GAAAyB,CAAA,CAAAM,MAAA,CAAArD,KAAA;MACA;QACAsB,GAAA,GAAAyB,CAAA;MACA;MACAR,OAAA,CAAAC,GAAA,CAAAlB,GAAA;MACA,SAAAzB,aAAA;QACA,KAAAyD,KAAA,WAAAhC,GAAA;MACA;QACA,KAAAgC,KAAA,UAAAhC,GAAA;MACA;IACA;IACAiC,qBAAA,WAAAA,sBAAAxC,WAAA;MACA,KAAAA,WAAA,GAAAA,WAAA;IACA;IACAyC,qBAAA,WAAAA,sBAAA;MACA,YAAAzC,WAAA;IACA;EACA;AACA", "ignoreList": []}]}