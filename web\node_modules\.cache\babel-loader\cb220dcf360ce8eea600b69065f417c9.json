{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\teachingproject\\teaching\\web\\src\\views\\account\\center\\page\\index.js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\views\\account\\center\\page\\index.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": *************}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": *************}], "contextDependencies": [], "result": ["import MineWorksPage from './MineWorks';\nexport { MineWorksPage };", {"version": 3, "names": ["MineWorksPage"], "sources": ["E:/teachingproject/teaching/web/src/views/account/center/page/index.js"], "sourcesContent": ["import MineWorksPage from './MineWorks'\n\nexport { MineWorksPage }"], "mappings": "AAAA,OAAOA,aAAa,MAAM,aAAa;AAEvC,SAASA,aAAa", "ignoreList": []}]}