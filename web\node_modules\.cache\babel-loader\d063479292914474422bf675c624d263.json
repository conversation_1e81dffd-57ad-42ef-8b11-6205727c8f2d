{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\teachingproject\\teaching\\web\\src\\utils\\device.js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\utils\\device.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1745675041593}], "contextDependencies": [], "result": ["import enquireJs from 'enquire.js';\nvar enquireScreen = function enquireScreen(call) {\n  // tablet\n  var handler = {\n    match: function match() {\n      call && call(0);\n    },\n    unmatch: function unmatch() {\n      call && call(-1);\n    }\n  };\n  // mobile\n  var handler2 = {\n    match: function match() {\n      call && call(1);\n    }\n  };\n  enquireJs.register('screen and (max-width: 1087.99px)', handler);\n  enquireJs.register('screen and (max-width: 767.99px)', handler2);\n};\nexport default enquireScreen;", {"version": 3, "names": ["enquireJs", "enquireScreen", "call", "handler", "match", "unmatch", "handler2", "register"], "sources": ["E:/teachingproject/teaching/web/src/utils/device.js"], "sourcesContent": ["import enquireJs from 'enquire.js'\n\nconst enquireScreen = function (call) {\n  // tablet\n  const handler = {\n    match: function () {\n      call && call(0)\n    },\n    unmatch: function () {\n      call && call(-1)\n    }\n  }\n  // mobile\n  const handler2 = {\n    match: () => {\n      call && call(1)\n    }\n  }\n  enquireJs.register('screen and (max-width: 1087.99px)', handler)\n  enquireJs.register('screen and (max-width: 767.99px)', handler2)\n}\n\nexport default enquireScreen"], "mappings": "AAAA,OAAOA,SAAS,MAAM,YAAY;AAElC,IAAMC,aAAa,GAAG,SAAhBA,aAAaA,CAAaC,IAAI,EAAE;EACpC;EACA,IAAMC,OAAO,GAAG;IACdC,KAAK,EAAE,SAAAA,MAAA,EAAY;MACjBF,IAAI,IAAIA,IAAI,CAAC,CAAC,CAAC;IACjB,CAAC;IACDG,OAAO,EAAE,SAAAA,QAAA,EAAY;MACnBH,IAAI,IAAIA,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB;EACF,CAAC;EACD;EACA,IAAMI,QAAQ,GAAG;IACfF,KAAK,EAAE,SAAAA,MAAA,EAAM;MACXF,IAAI,IAAIA,IAAI,CAAC,CAAC,CAAC;IACjB;EACF,CAAC;EACDF,SAAS,CAACO,QAAQ,CAAC,mCAAmC,EAAEJ,OAAO,CAAC;EAChEH,SAAS,CAACO,QAAQ,CAAC,kCAAkC,EAAED,QAAQ,CAAC;AAClE,CAAC;AAED,eAAeL,aAAa", "ignoreList": []}]}