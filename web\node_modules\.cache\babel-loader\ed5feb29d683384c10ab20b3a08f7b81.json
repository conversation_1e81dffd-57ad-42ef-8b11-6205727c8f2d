{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js!E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\App.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\App.vue", "mtime": 1753620136233}, {"path": "E:\\teachingproject\\teaching\\web\\babel.config.js", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["import zhCN from 'ant-design-vue/lib/locale-provider/zh_CN';\nimport enquireScreen from '@/utils/device';\nexport default {\n  data: function data() {\n    return {\n      locale: zhCN\n    };\n  },\n  created: function created() {\n    var that = this;\n    enquireScreen(function (deviceType) {\n      // tablet\n      if (deviceType === 0) {\n        that.$store.commit('TOGGLE_DEVICE', 'mobile');\n        that.$store.dispatch('setSidebar', false);\n      }\n      // mobile\n      else if (deviceType === 1) {\n        that.$store.commit('TOGGLE_DEVICE', 'mobile');\n        that.$store.dispatch('setSidebar', false);\n      } else {\n        that.$store.commit('TOGGLE_DEVICE', 'desktop');\n        that.$store.dispatch('setSidebar', true);\n      }\n    });\n  }\n};", {"version": 3, "names": ["zhCN", "enquireScreen", "data", "locale", "created", "that", "deviceType", "$store", "commit", "dispatch"], "sources": ["src/App.vue"], "sourcesContent": ["<template>\n  <a-config-provider :locale=\"locale\">\n    <div id=\"app\">\n      <router-view/>\n    </div>\n  </a-config-provider>\n</template>\n<script>\n  import zhCN from 'ant-design-vue/lib/locale-provider/zh_CN'\n  import enquireScreen from '@/utils/device'\n\n  export default {\n    data () {\n      return {\n        locale: zhCN,\n      }\n    },\n    created () {\n      let that = this\n      enquireScreen(deviceType => {\n        // tablet\n        if (deviceType === 0) {\n          that.$store.commit('TOGGLE_DEVICE', 'mobile')\n          that.$store.dispatch('setSidebar', false)\n        }\n        // mobile\n        else if (deviceType === 1) {\n          that.$store.commit('TOGGLE_DEVICE', 'mobile')\n          that.$store.dispatch('setSidebar', false)\n        }\n        else {\n          that.$store.commit('TOGGLE_DEVICE', 'desktop')\n          that.$store.dispatch('setSidebar', true)\n        }\n\n      })\n    }\n  }\n</script>\n<style>\n  #app {\n    height: 100%;\n  }\n</style>"], "mappings": "AAQA,OAAAA,IAAA;AACA,OAAAC,aAAA;AAEA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,MAAA,EAAAH;IACA;EACA;EACAI,OAAA,WAAAA,QAAA;IACA,IAAAC,IAAA;IACAJ,aAAA,WAAAK,UAAA;MACA;MACA,IAAAA,UAAA;QACAD,IAAA,CAAAE,MAAA,CAAAC,MAAA;QACAH,IAAA,CAAAE,MAAA,CAAAE,QAAA;MACA;MACA;MAAA,KACA,IAAAH,UAAA;QACAD,IAAA,CAAAE,MAAA,CAAAC,MAAA;QACAH,IAAA,CAAAE,MAAA,CAAAE,QAAA;MACA,OACA;QACAJ,IAAA,CAAAE,MAAA,CAAAC,MAAA;QACAH,IAAA,CAAAE,MAAA,CAAAE,QAAA;MACA;IAEA;EACA;AACA", "ignoreList": []}]}