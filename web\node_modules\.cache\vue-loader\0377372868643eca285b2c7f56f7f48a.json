{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\dict\\JDictSelectTag.vue?vue&type=style&index=0&id=33375052&scoped=true&lang=css", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\dict\\JDictSelectTag.vue", "mtime": 1751357905810}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\css-loader\\index.js", "mtime": 1745674981656}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1745675053827}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n.current-status {\n  background-color: #52c41a !important;\n  border-color: #52c41a !important;\n  color: #fff !important;\n  box-shadow: -1px 0 0 0 #52c41a;\n}\n/* 当鼠标悬停在绿色状态按钮上，保持绿色 */\n.current-status:hover {\n  background-color: #73d13d !important;\n  border-color: #73d13d !important;\n}\n", {"version": 3, "sources": ["JDictSelectTag.vue"], "names": [], "mappings": ";AA0KA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "JDictSelectTag.vue", "sourceRoot": "src/components/dict", "sourcesContent": ["<template>\n  <a-radio-group v-if=\"tagType=='radio'\" @change=\"handleInput\" :value=\"getValueSting\" :disabled=\"disabled\">\n    <a-radio v-for=\"(item, key) in dictOptions\" :key=\"key\" :value=\"item.value\">{{ item.text }}</a-radio>\n  </a-radio-group>\n\n  <a-radio-group v-else-if=\"tagType=='radioButton'\"  buttonStyle=\"solid\" @change=\"handleInput\" :value=\"getValueSting\" :disabled=\"disabled\">\n    <a-radio-button \n      v-for=\"(item, key) in dictOptions\" \n      :key=\"key\" \n      :value=\"item.value\" \n      :class=\"{'current-status': !hideGreenStatus && item.value === originalValue && item.value !== getValueSting}\">\n      {{ item.text }}\n    </a-radio-button>\n  </a-radio-group>\n\n  <a-select v-else-if=\"tagType=='select'\" :getPopupContainer = \"getPopupContainer\" :placeholder=\"placeholder\" :disabled=\"disabled\" :value=\"getValueSting\" @change=\"handleInput\">\n    <a-select-option :value=\"undefined\">请选择</a-select-option>\n    <a-select-option v-for=\"(item, key) in dictOptions\" :key=\"key\" :value=\"item.value\">\n      <span style=\"display: inline-block;width: 100%\" :title=\" item.text || item.label \">\n        <a-icon v-if=\"canDeleteItem\" type=\"delete\" @click=\"deleteDictItem($event,item)\"/>\n        {{ item.text || item.label }}\n      </span>\n    </a-select-option>\n  </a-select>\n</template>\n\n<script>\n  import {ajaxGetDictItems,getDictItemsFromCache,addDictItemsCache,deleteDictItems,deleteDictItemsCache} from '@/api/api'\n\n  export default {\n    name: \"JDictSelectTag\",\n    props: {\n      dictCode: String,\n      placeholder: String,\n      triggerChange: Boolean,\n      disabled: Boolean,\n      value: [String, Number],\n      type: String,\n      canDeleteItem: { //是否可以删除字典项\n        type: Boolean,\n        default: false\n      },\n      getPopupContainer:{\n        type: Function,\n        default: (node) => node.parentNode\n      },\n      defaultShowAll: { //是否显示全部选项\n        type: Boolean,\n        default: false\n      },\n      //默认选项\n      defaultDictOptions:{\n        type: Array,\n        default: ()=>[]\n      },\n      // 是否隐藏绿色状态（学生端设为true，教师和管理员端设为false）\n      hideGreenStatus: {\n        type: Boolean,\n        default: false\n      } \n    },\n    data() {\n      return {\n        dictOptions: [],\n        tagType:\"\",\n        originalValue: null\n      }\n    },\n    watch:{\n      dictCode:{\n        immediate:true,\n        handler() {\n          this.initDictData()\n        },\n      },\n      value: {\n        immediate: true,\n        handler(val) {\n          // 如果外部未手动设置originalValue，则使用第一次加载的值作为当前状态\n          if (this.originalValue === null && val !== null) {\n            this.originalValue = val !== undefined ? val.toString() : undefined;\n          }\n        }\n      }\n    },\n    created() {\n      // console.log(this.dictCode);\n      if(!this.type || this.type===\"list\"){\n        this.tagType = \"select\"\n      }else{\n        this.tagType = this.type\n      }\n      //获取字典数据\n      // this.initDictData();\n    },\n    computed: {\n      getValueSting(){\n        // 修复：确保值不是null或undefined再调用toString()\n        // 当有null或\"\" placeholder不显示\n        return (this.value !== undefined && this.value !== null) ? String(this.value) : undefined;\n      },\n    },\n    methods: {\n      initDictData() {\n        if(this.defaultShowAll){\n          this.dictOptions = [{title: \"全部\", text: \"全部\", description:'', value: ''}]\n        }else{\n          this.dictOptions = []\n        }\n\n        if(this.defaultDictOptions != null && this.defaultDictOptions.length > 0){\n          this.dictOptions = this.defaultDictOptions\n        }\n        \n        if(this.dictCode){\n          if(getDictItemsFromCache(this.dictCode)){\n            this.dictOptions = this.dictOptions.concat(getDictItemsFromCache(this.dictCode))\n          }else{\n            //根据字典Code, 初始化字典数组\n            ajaxGetDictItems(this.dictCode, null).then((res) => {\n              if (res.success) {\n                this.dictOptions = this.dictOptions.concat(res.result);\n              }\n            })\n          }\n        }\n        console.log(this.dictOptions);\n      },\n      //添加字典项缓存\n      addDictItems(dictValue, dictText, dictTitle, dictDesc){\n        addDictItemsCache(this.dictCode, dictValue, dictText, dictTitle, dictDesc)\n        this.initDictData()\n      },\n      //删除字典项\n      deleteDictItem(e,v){\n        e.stopPropagation()\n        if(confirm(\"是否确定删除 \" + v.text + \"?\")){\n          deleteDictItems(this.dictCode, v.text).then(res=>{\n            if(res.success){\n              deleteDictItemsCache(this.dictCode, v.text)\n              this.initDictData()\n            }\n          })\n        }\n      },\n      handleInput(e) {\n        let val;\n        if(this.tagType==\"radio\"){\n          val = e.target.value\n        }else{\n          val = e\n        }\n        console.log(val);\n        if(this.triggerChange){\n          this.$emit('change', val);\n        }else{\n          this.$emit('input', val);\n        }\n      },\n      setCurrentDictOptions(dictOptions){\n        this.dictOptions = dictOptions\n      },\n      getCurrentDictOptions(){\n        return this.dictOptions\n      }\n    }\n  }\n</script>\n\n<style scoped>\n.current-status {\n  background-color: #52c41a !important;\n  border-color: #52c41a !important;\n  color: #fff !important;\n  box-shadow: -1px 0 0 0 #52c41a;\n}\n/* 当鼠标悬停在绿色状态按钮上，保持绿色 */\n.current-status:hover {\n  background-color: #73d13d !important;\n  border-color: #73d13d !important;\n}\n</style>"]}]}