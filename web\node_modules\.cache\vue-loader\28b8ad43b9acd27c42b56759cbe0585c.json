{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\App.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\App.vue", "mtime": 1753620136233}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750057698841}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n  import zhCN from 'ant-design-vue/lib/locale-provider/zh_CN'\n  import enquireScreen from '@/utils/device'\n\n  export default {\n    data () {\n      return {\n        locale: zhCN,\n      }\n    },\n    created () {\n      let that = this\n      enquireScreen(deviceType => {\n        // tablet\n        if (deviceType === 0) {\n          that.$store.commit('TOGGLE_DEVICE', 'mobile')\n          that.$store.dispatch('setSidebar', false)\n        }\n        // mobile\n        else if (deviceType === 1) {\n          that.$store.commit('TOGGLE_DEVICE', 'mobile')\n          that.$store.dispatch('setSidebar', false)\n        }\n        else {\n          that.$store.commit('TOGGLE_DEVICE', 'desktop')\n          that.$store.dispatch('setSidebar', true)\n        }\n\n      })\n    }\n  }\n", {"version": 3, "sources": ["App.vue"], "names": [], "mappings": ";AAQA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "App.vue", "sourceRoot": "src", "sourcesContent": ["<template>\n  <a-config-provider :locale=\"locale\">\n    <div id=\"app\">\n      <router-view/>\n    </div>\n  </a-config-provider>\n</template>\n<script>\n  import zhCN from 'ant-design-vue/lib/locale-provider/zh_CN'\n  import enquireScreen from '@/utils/device'\n\n  export default {\n    data () {\n      return {\n        locale: zhCN,\n      }\n    },\n    created () {\n      let that = this\n      enquireScreen(deviceType => {\n        // tablet\n        if (deviceType === 0) {\n          that.$store.commit('TOGGLE_DEVICE', 'mobile')\n          that.$store.dispatch('setSidebar', false)\n        }\n        // mobile\n        else if (deviceType === 1) {\n          that.$store.commit('TOGGLE_DEVICE', 'mobile')\n          that.$store.dispatch('setSidebar', false)\n        }\n        else {\n          that.$store.commit('TOGGLE_DEVICE', 'desktop')\n          that.$store.dispatch('setSidebar', true)\n        }\n\n      })\n    }\n  }\n</script>\n<style>\n  #app {\n    height: 100%;\n  }\n</style>"]}]}