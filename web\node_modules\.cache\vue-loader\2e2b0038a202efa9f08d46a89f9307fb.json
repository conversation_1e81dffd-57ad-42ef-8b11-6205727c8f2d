{"remainingRequest": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\teachingproject\\teaching\\web\\src\\components\\jeecg\\JFormContainer.vue?vue&type=style&index=0&id=4d757dc4&lang=css", "dependencies": [{"path": "E:\\teachingproject\\teaching\\web\\src\\components\\jeecg\\JFormContainer.vue", "mtime": 1719209168000}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\css-loader\\index.js", "mtime": 1745674981656}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1745674970532}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1745675053827}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1745675051303}, {"path": "E:\\teachingproject\\teaching\\web\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1745674970532}], "contextDependencies": [], "result": ["\n.jeecg-form-container-disabled{\n  cursor: not-allowed;\n}\n.jeecg-form-container-disabled fieldset[disabled] {\n  -ms-pointer-events: none;\n  pointer-events: none;\n}\n.jeecg-form-container-disabled .ant-select{\n  -ms-pointer-events: none;\n  pointer-events: none;\n}\n\n.jeecg-form-container-disabled .ant-upload-select{display:none}\n.jeecg-form-container-disabled .ant-upload-list{cursor:grabbing}\n.jeecg-form-container-disabled fieldset[disabled] .ant-upload-list{\n  -ms-pointer-events: auto !important;\n  pointer-events: auto !important;\n}\n\n.jeecg-form-container-disabled .ant-upload-list-item-actions .anticon-delete,\n.jeecg-form-container-disabled .ant-upload-list-item .anticon-close{\n  display: none;\n}\n", {"version": 3, "sources": ["JFormContainer.vue"], "names": [], "mappings": ";AA6DA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "JFormContainer.vue", "sourceRoot": "src/components/jeecg", "sourcesContent": ["<template>\n  <div :class=\"disabled?'jeecg-form-container-disabled':''\">\n    <fieldset :disabled=\"disabled\">\n      <slot name=\"detail\"></slot>\n    </fieldset>\n    <slot name=\"edit\"></slot>\n    <fieldset disabled>\n      <slot></slot>\n    </fieldset>\n  </div>\n</template>\n\n<script>\n  /**\n   * 使用方法\n   * 在form下直接写这个组件就行了，\n   *<a-form layout=\"inline\" :form=\"form\" >\n   *     <j-form-container :disabled=\"true\">\n   *         <!-- 表单内容省略..... -->\n   *     </j-form-container>\n   *</a-form>\n   */\n  export default {\n    name: 'JFormContainer',\n    props:{\n      disabled:{\n        type:Boolean,\n        default:false,\n        required:false\n      }\n    },\n    mounted(){\n      console.log(\"我是表单禁用专用组件,但是我并不支持表单中iframe的内容禁用\")\n    }\n  }\n</script>\n<style>\n  .jeecg-form-container-disabled{\n    cursor: not-allowed;\n  }\n  .jeecg-form-container-disabled fieldset[disabled] {\n    -ms-pointer-events: none;\n    pointer-events: none;\n  }\n  .jeecg-form-container-disabled .ant-select{\n    -ms-pointer-events: none;\n    pointer-events: none;\n  }\n\n  .jeecg-form-container-disabled .ant-upload-select{display:none}\n  .jeecg-form-container-disabled .ant-upload-list{cursor:grabbing}\n  .jeecg-form-container-disabled fieldset[disabled] .ant-upload-list{\n    -ms-pointer-events: auto !important;\n    pointer-events: auto !important;\n  }\n\n  .jeecg-form-container-disabled .ant-upload-list-item-actions .anticon-delete,\n  .jeecg-form-container-disabled .ant-upload-list-item .anticon-close{\n    display: none;\n  }\n</style>"]}]}